<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

class an_productfieldsajaxModuleFrontController extends ModuleFrontController
{    
    public function initContent()
    {
        $result = [];
        if (Tools::isSubmit('action')) {

            if (Tools::getValue('token') != Tools::getToken(false)){
                Tools::redirect('index.php?controller=404');
            }

            $actionName = Tools::getValue('action', '') . 'Action';
            if (method_exists($this, $actionName)) {
                $result = $this->$actionName();
            }
        }

        die(json_encode($result));
    }    
    
    public function getPricesAction()
    {
        $price = Tools::getValue('price');
        $return['price'] = '';
 
        if (!is_array($price) && !$price){
            return $return;
        } elseif (!is_array($price)) {
            $return['price'] = anPfs::displayPrice((float)$price, $this->context);
        }

        if (is_array($price) && count($price) < 15){
            foreach ($price as $key => $item){
                if ($item != ''){
                    $return['prices'][$key] = anPfs::displayPrice((float)$item, $this->context);                 
                }
            }
        }

        // $price = preg_replace('#[^0-9\.,]#', '', $price);
        // $price = str_replace(',', '.', $price);        
        
        return $return;
    }    

    public function getFieldsAction()
    {
        $idProduct = Tools::getValue('id_product');
    
        if (!$idProduct) {
            return [];
        }

        $id_product_attribute = Tools::getValue('id_product_attribute');            

        $prices['price_tax_exc'] = Product::getPriceStatic($idProduct, false, null, 2);

        $fields = anProductFields::getFieldsByIdProduct($idProduct, $id_product_attribute, $prices);
    
        if (!count($fields)){
            return;
        }

        $classprice = trim(Configuration::get(an_productfields::PREFIX . 'classprice'));
        if (!$classprice || $classprice == ''){ 
            $classprice = 0;
        }
        $classpricecontainer = trim(Configuration::get(an_productfields::PREFIX . 'classpricecontainer'));
        if (!$classpricecontainer || $classpricecontainer == ''){ 
            $classpricecontainer = 0;
        }            
        $classfieldscontainer = trim(Configuration::get(an_productfields::PREFIX . 'classfieldscontainer'));
        if (!$classfieldscontainer || $classfieldscontainer == ''){ 
            $classfieldscontainer = 0;
        }            
        $classfieldsposition = trim(Configuration::get(an_productfields::PREFIX . 'classfieldsposition'));
        if (!$classfieldsposition || $classfieldsposition == ''){ 
            $classfieldsposition = 0;
        }            
        
        $this->context->smarty->assign('widget', [
            'fields' => $fields,
            'urlPriceFormat' => $this->context->link->getModuleLink('an_productfields', 'ajax', ['token' => Tools::getToken(false)], true),
            'config' => [
                'label_char_counter' => Configuration::get(an_productfields::PREFIX . 'label_char_counter', $this->context->language->id),
                'label_total' => Configuration::get(an_productfields::PREFIX . 'label_total', $this->context->language->id),
                'title' => Configuration::get(an_productfields::PREFIX . 'title', $this->context->language->id),
                'text_header' => Configuration::get(an_productfields::PREFIX . 'text_header', $this->context->language->id),
                'text_footer' => Configuration::get(an_productfields::PREFIX . 'text_footer', $this->context->language->id),
            //    'style_negative_price' => Configuration::get(an_productfields::PREFIX . 'style_negative_price'),
                'priceDisplayMethod' => !Group::getDefaultPriceDisplayMethod() && Configuration::get(an_productfields::PREFIX . 'use_tax'),
                'calculationTotalFields' => Configuration::get(an_productfields::PREFIX . 'calculationTotalFields'),
                'calculationTotalFieldsPlusProduct' => Configuration::get(an_productfields::PREFIX . 'calculationTotalFieldsPlusProduct'),
                'videoPoster' => __PS_BASE_URI__ . 'modules/an_productfields/views/img/video2.jpg',

                'classprice' => $classprice,
                'classpricecontainer' => $classpricecontainer,
                'classfieldscontainer' => $classfieldscontainer,
                'classfieldsposition' => $classfieldsposition,

            ]
        ]);
        
        $return['fields'] = $this->module->display($this->module->name, 'widget.tpl');;
        
        return $return;
    } 

    public function getTotalPriceAction()
    {
        $return['price'] = '';
        $price = Tools::getValue('price');
        $id_product = Tools::getValue('id_product');

        $return['price']  = anProductFieldsTax::addTax($price, null, $id_product);

        return $return;
    }


    public function getTotalPriceFormulaAction()
    {
        $return['total'] = 0;
        $return['total_wt'] = 0;

        $id_product = Tools::getValue('id_product');
        $qty = (int) Tools::getValue('qty');
        $productPrice = (float) Tools::getValue('product_price');
        $productPricerRegular = (float) Tools::getValue('product_price_regular');
        $fieldsPrice = Tools::getValue('anpfs_fields_price', []);
        $formulaOrigin = anPfsFormula::getFormulaByIdProduct($id_product);

        if ($formulaOrigin !=''){
            preg_match_all('/(?<=\[).+?(?=\])/', $formulaOrigin, $formulaIdsFields, PREG_SET_ORDER);
        }

        $formula = $formulaOrigin;
        if ($formula !=''){
            foreach ($formulaIdsFields as $idField){
                if (!isset($fieldsPrice[$idField['0']])){
                    $fieldsPrice[$idField['0']] = 0;
                }
                $formula = str_replace('['.$idField['0'].']', floatval($fieldsPrice[$idField['0']]), $formula);
            }

            $formula = str_replace(array('product', 'quantity'), array($productPrice, $qty), $formula);

            eval('$formula_price = ' . $formula . ';');

            if (anPfs::getPriceDisplayMethod()){
                $return['calc'] =  anProductFieldsTax::addTax($formula_price, null, $id_product);
                $return['sum'] =  $productPrice + anProductFieldsTax::addTax($formula_price, null, $id_product);
                $return['regular'] =  $productPricerRegular + anProductFieldsTax::addTax($formula_price, null, $id_product);
            } else {
                $return['calc'] =  $formula_price;
                $return['sum'] =  $productPrice + $formula_price;
                $return['regular'] =  $productPricerRegular + $formula_price;
            }

            $return['calc'] = anPfs::displayPrice($return['calc'], $this->context);
            $return['sum'] = anPfs::displayPrice($return['sum'], $this->context);
            $return['regular'] = anPfs::displayPrice($return['regular'], $this->context);



            /*
            calc - цена филдов, которая не учитывает цену товара
            sum - цена товара + calc
            regular - цена товара без скидки + calc
            */
        }
        
        return $return;
    }
}