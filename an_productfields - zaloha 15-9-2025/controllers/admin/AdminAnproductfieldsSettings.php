<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminAnproductfieldsSettingsController extends ModuleAdminController
{
    protected $_module = null;

    public function __construct()
    {
        $this->bootstrap = true;
        $this->display = 'view';
 
		$this->name = 'AdminAnproductfieldsSettingsController';
		$this->controller = 'AdminAnproductfieldsSettings';
		
        parent::__construct();
    }

    // public function setMedia($isNewTheme = false)
    // {
    //     parent::setMedia($isNewTheme);

    //     $this->addJquery();
    //     $this->js_files[] = _MODULE_DIR_ . 'an_homeproducts/views/js/back_settings.js';
    // } 

    public function l($string, $class = null, $addslashes = false, $htmlentities = true)
    {
        return parent::l($string, pathinfo(__FILE__, PATHINFO_FILENAME), $addslashes, $htmlentities);
    }    

    public function initToolBarTitle()
    {
        $this->toolbar_title[] = $this->l('Product Fields: ') . $this->trans('Settings', [], 'Admin.Global');
    }

    public function initHeader()
    {
        parent::initHeader();
        $tabs = &$this->context->smarty->tpl_vars['tabs']->value;    
        $tabs = $this->module->createAdminTabs($tabs, $this->controller_name);
    }
	
    public function initContent()
    {
        $this->context->smarty->assign('current_tab_level', 3);

        return parent::initContent();
    }    
	
	
    /**
     * Create the structure of your form.
     */
    protected function getSettingsForm()
    {
        $this->context->smarty->assign([
            'desc_html_image_url' => __PS_BASE_URI__ . 'modules/an_productfields/views/img/',
        ]);
        
        $form['0']['form']['legend'] = [
            'title' => $this->trans('Settings', [], 'Admin.Global'),
        ];

        $form['0']['form']['submit'] = [
            'name' => 'save',
            'title' => $this->trans('Save', [], 'Admin.Actions'),
        ];
        
        $form['0']['form']['input'][] = [
			'type' => 'switch',
			'name' => an_productfields::PREFIX . 'use_tax',
			'label' => $this->l('Apply tax to fields value'),
			'values' => [
				[
					'id' => 'active_on',
					'value' => 1,
					'label' => $this->trans('Enabled', [], 'Admin.Global')
				],
				[
					'id' => 'active_off',
					'value' => 0,
					'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
			]
		];

        $form['0']['form']['input'][] = [
			'type' => 'switch',
			'name' => 'an_pf_price_wt_round',
			'label' => $this->l('Round up price with tax'),
			'values' => [
				[
					'id' => 'active_on',
					'value' => 1,
					'label' => $this->trans('Enabled', [], 'Admin.Global')
				],
				[
					'id' => 'active_off',
					'value' => 0,
					'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
			],
            'form_group_class' => 'hide'
		];        

        $form['0']['form']['input'][] = [
			'type' => 'switch',
			'name' => an_productfields::PREFIX . 'calculationTotalFields',
			'label' => $this->l('Field total price calculation'),
			'values' => [
				[
					'id' => 'active_on',
					'value' => 1,
					'label' => $this->trans('Enabled', [], 'Admin.Global')
				],
				[
					'id' => 'active_off',
					'value' => 0,
					'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
			]
		];

        $form['0']['form']['input'][] = [
			'type' => 'switch',
			'name' => an_productfields::PREFIX . 'calculationTotalFieldsPlusProduct',
			'label' => $this->l('Add field price to the product price on a product page'),
            'desc_html' => $this->module->display(_PS_MODULE_DIR_.'an_productfields', 'views/templates/admin/setting-desc-TotalFieldsPlusProduct.tpl'),
			'values' => [
				[
					'id' => 'active_on',
					'value' => 1,
					'label' => $this->trans('Enabled', [], 'Admin.Global')
				],
				[
					'id' => 'active_off',
					'value' => 0,
					'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
			],
		];
        
        
        $form['0']['form']['input'][] = [
			'type' => 'switch',
			'name' => an_productfields::PREFIX . 'totalFieldsPlusProductInCart',
			'label' => $this->l('Add field price to product total in Cart'),
            'desc_html' => $this->module->display(_PS_MODULE_DIR_.'an_productfields', 'views/templates/admin/setting-desc-totalFieldsPlusProductInCart.tpl'),
			'values' => [
				[
					'id' => 'active_on',
					'value' => 1,
					'label' => $this->trans('Enabled', [], 'Admin.Global')
				],
				[
					'id' => 'active_off',
					'value' => 0,
					'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
			],
		];        
        
        //  The option is no longer relevant since version 3.3.67
        // $form['0']['form']['input'][] = [
		// 	'type' => 'switch',
		// 	'name' => an_productfields::PREFIX . 'savePfOrder',
		// 	'label' => $this->l('Save fields for orders'),
        //     'form_group_class' => 'hide',
		// 	'values' => [
		// 		[
		// 			'id' => 'active_on',
		// 			'value' => 1,
		// 			'label' => $this->trans('Enabled', [], 'Admin.Global')
		// 		],
		// 		[
		// 			'id' => 'active_off',
		// 			'value' => 0,
		// 			'label' => $this->trans('Disabled', [], 'Admin.Global')
        //         ]
		// 	],
		// ]; 
        
        $form['0']['form']['input'][] = [
			'type' => 'switch',
			'name' => an_productfields::PREFIX . 'create_order_bo',
			'label' => $this->l('Use fields for Create order on back office'),
            'form_group_class' => 'hide',
			'values' => [
				[
					'id' => 'active_on',
					'value' => 1,
					'label' => $this->trans('Enabled', [], 'Admin.Global')
				],
				[
					'id' => 'active_off',
					'value' => 0,
					'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
			],
		];        

        $form['0']['form']['input'][] = [
			'type' => 'switch',
			'name' => an_productfields::PREFIX . 'type_field_file',
			'label' => $this->l('Type file'),
            'form_group_class' => 'hide',
			'values' => [
				[
					'id' => 'active_on',
					'value' => 1,
					'label' => $this->trans('Enabled', [], 'Admin.Global')
				],
				[
					'id' => 'active_off',
					'value' => 0,
					'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
			],
		];   

        $form['0']['form']['input'][] = [
			'type' => 'switch',
			'name' => an_productfields::PREFIX . 'customer_groups',
			'label' => $this->l('Use customer groups'),
            'form_group_class' => 'hide',
			'values' => [
				[
					'id' => 'active_on',
					'value' => 1,
					'label' => $this->trans('Enabled', [], 'Admin.Global')
				],
				[
					'id' => 'active_off',
					'value' => 0,
					'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
			],
		];   
 
        $form['0']['form']['input'][] = [
			'type' => 'switch',
			'name' => an_productfields::PREFIX . 'applyReductionCustomerGroup',
			'label' => $this->l('Apply customer group discount (Only if the price is positive.)'),
			'desc' => $this->l('Customer Settings / Groups'),
			'values' => [
				[
					'id' => 'active_on',
					'value' => 1,
					'label' => $this->trans('Enabled', [], 'Admin.Global')
				],
				[
					'id' => 'active_off',
					'value' => 0,
					'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
			],
		];        

        $form['0']['form']['input'][] = [
			'type' => 'switch',
			'name' => an_productfields::PREFIX . 'hide_field_prices',
			'label' => $this->l('Hide Field Prices'),
			'values' => [
				[
					'id' => 'active_on',
					'value' => 1,
					'label' => $this->trans('Enabled', [], 'Admin.Global')
				],
				[
					'id' => 'active_off',
					'value' => 0,
					'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
			],
		];

        $form['0']['form']['input'][] = [
			'type' => 'html',
			'name' => 'line1',
			'html_content' => 'hr',	
        ];		   

        $form['0']['form']['input'][] = [
            'type' => 'text',
            'label' => $this->l('Label for Total'),
            'name' => an_productfields::PREFIX . 'label_total',
            'lang' => true,
        ];

        /*
        $form['0']['form']['input'][] = [
            'type' => 'text',
            'label' => $this->l('Label for Character counter'),
            'name' => an_productfields::PREFIX . 'label_char_counter',
            'lang' => true,
        ];        
        */      

        $form['0']['form']['input'][] = [
            'type' => 'text',
            'label' => $this->trans('Title', [], 'Admin.Global'),
            'name' => an_productfields::PREFIX . 'title',
            'lang' => true,
        ];

        $form['0']['form']['input'][] = [
			'type' => 'textarea',
			'class' => 'autoload_rte',
			'name' => an_productfields::PREFIX . 'text_header',
			'label' => $this->l('Text for header'),	
            'lang' => true,
            'html' => true,
        ];   
        
        $form['0']['form']['input'][] = [
			'type' => 'textarea',
			'class' => 'autoload_rte',
			'name' => an_productfields::PREFIX . 'text_footer',
			'label' => $this->l('Text for footer'),	
            'lang' => true,
            'html' => true,
        ];





        

        $form['1']['form']['legend'] = [
            'title' => $this->trans('Additional settings', [], 'Admin.Global'),
        ];

        $form['1']['form']['submit'] = [
            'name' => 'save',
            'title' => $this->trans('Save', [], 'Admin.Actions'),
        ];

        $form['1']['form']['input'][] = [
            'type' => 'text',
            'label' => $this->l('Class price (For developer only)'),
            'name' => an_productfields::PREFIX . 'classprice',
        ];

        $form['1']['form']['input'][] = [
            'type' => 'text',
            'label' => $this->l('Class price container (For developer only)'),
            'name' => an_productfields::PREFIX . 'classpricecontainer',
        ];
        
        $form['1']['form']['input'][] = [
            'type' => 'text',
            'label' => $this->l('Class fields container (For developer only)'),
            'name' => an_productfields::PREFIX . 'classfieldscontainer',
        ];        

        
        $form['1']['form']['input'][] = [
            'type' => 'text',
            'label' => $this->l('Class fields position (For developer only)'),
            'name' => an_productfields::PREFIX . 'classfieldsposition',
            'desc' => $this->l('Before which class the fields should be displayed. For example: .js-product-add-to-cart'),
        ];     

        // $form['0']['form']['input'][] = [
        //     'type' => 'select',
        //     'label' => $this->l('Display style for negative price'),
        //     'name' => an_productfields::PREFIX . 'style_negative_price',
        //     'default' => 'standart',
        //     'options' => [
        //         'query' => [
        //             ['id' => 'standart', 'name' => 'Standart'],
        //             ['id' => 'red_white', 'name' => 'Red and white']
        //         ],
        //         'id' => 'id',
        //         'name' => 'name',
        //     ],
        // ]; 


        return $form;
    }

    public function renderView()
    {
        $languages = $this->context->controller->getLanguages();
  
        $helper = new HelperForm();
        $helper->show_toolbar = false;
        $helper->name_controller = $this->name;
        $helper->submit_action = $this->name;
        $helper->currentIndex = $this->context->link->getAdminLink($this->controller, false);
        $helper->token = Tools::getAdminTokenLite($this->controller);
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG') ? Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG') : 0;
        $helper->tpl_vars = [
            'uri' => $this->module->getPathUri(),
            'languages' => $languages,
            'id_language' => $this->context->language->id
        ];

        $helper->override_folder = 'setting/';
        $helper->setTpl($this->override_folder);

        $form = $this->getSettingsForm();

        foreach($form as $subForm){
            foreach ($subForm['form']['input'] as $input){
                if (isset($input['lang']) && $input['lang']){
                    $value = [];
                    foreach ($languages  as $language){
                        $value[$language['id_lang']] = Configuration::get($input['name'], $language['id_lang']);
                    }
                    $helper->tpl_vars['fields_value'][$input['name']] = $value;
                } else {
                    $helper->tpl_vars['fields_value'][$input['name']] = Configuration::get($input['name']);
                }
            }
        }
                	
        return $helper->generateForm($form) . $this->module->topPromo();		
	}

    public function postProcess()
    {    
        $form = $this->getSettingsForm();

        $isSubmit = false;
        foreach($form as $subForm){
            if (Tools::isSubmit($subForm['form']['submit']['name'])){
                $isSubmit = true;
            }
        }
        
        if ($isSubmit) {

            $languages = Language::getLanguages(false);

            foreach($form as $subForm){
                
                foreach ($subForm['form']['input'] as $input){
                    
                    $html = false;

                    if (isset($input['html']) && $input['html']){
                        $html = true;
                    }

                    if (isset($input['lang']) && $input['lang']){
                        $value = [];
                        foreach ($languages  as $language){
                            $value[$language['id_lang']] = Tools::getValue($input['name'].'_' . $language['id_lang']);
                        }
            
                        Configuration::updateValue($input['name'], $value, $html);
                    } else {
                        Configuration::updateValue($input['name'], Tools::getValue($input['name']), $html);  
                    }
                }  
            }
            
            $this->module->_clearCache('*');

            $currentIndex = $this->context->link->getAdminLink($this->controller, false);
            $token = Tools::getAdminTokenLite($this->controller);            
            Tools::redirectAdmin($currentIndex . '&token=' . $token . '&conf=4');
        }
		return true;
    }
}
