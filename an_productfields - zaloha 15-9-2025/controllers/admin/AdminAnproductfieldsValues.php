<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anProductFields.php';
require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anProductFieldsValues.php';
require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anProductFieldsFiles.php';
require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anPfsCustomerGroups.php';

class AdminAnproductfieldsValuesController extends ModuleAdminController
{
    protected $_module = null;
    
	protected $position_identifier = 'position';
    protected $_defaultOrderBy = 'position';
    protected $_defaultOrderWay = 'ASC';   
    protected $bulk_actions = []; 

    public $id_field;
    public $objField;

    public function __construct()
    {
        $this->bootstrap = true;
        $this->context = Context::getContext();
        $this->table = 'an_productfields_fields_values';
        $this->identifier = 'id_value';
        $this->className = 'anProductFieldsValues';
        $this->lang = true;

        $this->id_field = (int)Tools::getValue('id_field');
        $this->titleToolbar = '';
        
        if ($this->id_field) {
            $this->objField = new anProductFields($this->id_field, null, true);
        }
        
        if (isset($this->objField) && $this->objField->id){
            $this->titleToolbar = $this->objField->title[$this->context->language->id];
        }         

        $this->addRowAction('edit');
        $this->addRowAction('delete');
 
        $this->name = 'AdminAnproductfieldsValuesController';
        
        parent::__construct();

        $this->tpl_list_vars['action'] = $this->context->link->getAdminLink(
            $this->controller_name, true, [], ['id_field' => $this->id_field]) . '#' . $this->table;

        $this->fields_list = [
            'id_value' => [
                'title' => $this->trans('ID', [], 'Admin.Global'), 
                'width' => 25,
                'search'  => false,
            ],

            // 'id_field' => [
            //     'title' => $this->l('id_field'), 
            //     'width' => 25,
            //     'search'  => false,
            // ],

            // 'file' => [
            //     'title' => $this->l('file'), 
            //     'search'  => false,
            // ],  

            'title' => [
                'title' => $this->trans('Title', [], 'Admin.Global'), 
                'search'  => false
            ],  

            'price' => [
                'title' => $this->trans('Price', [], 'Admin.Global'), 
                'search'  => false
            ],

            'position' => [
                'title' => $this->trans('Position', [], 'Admin.Global'), 
                'search'  => false,
                'position' => true
            ],            

            'active' => [
                'title' => $this->trans('Active', [], 'Admin.Global'),
                'width' => 40,
                'active' => 'update',
                'align' => 'center',
                'type' => 'bool',
                'search'  => false,
                'orderby' => false
            ],          

        ];

        $this->_where .= ' AND a.id_field = ' . $this->id_field . ' ';

        if (Shop::isFeatureActive() && Shop::getContext() != Shop::CONTEXT_ALL) {
            $this->_where .= ' AND a.' . $this->identifier . ' IN (
                SELECT sa.' . $this->identifier . '
                FROM `' . _DB_PREFIX_ . $this->table . '_shop` sa
                WHERE sa.id_shop IN (' . implode(', ', Shop::getContextListShopID()) . ')
            )';
        }
    }

    public function l($string, $class = null, $addslashes = false, $htmlentities = true)
    {
        return parent::l($string, pathinfo(__FILE__, PATHINFO_FILENAME), $addslashes, $htmlentities);
    }   

    public function getList($id_lang, $order_by = null, $order_way = null, $start = 0, $limit = null, $id_lang_shop = false)
    {
        parent::getList($id_lang, $order_by, $order_way, $start, $limit, $id_lang_shop);
        
        foreach ($this->_list as &$list) {
            if ($list['type_price'] == 1){
                $list['price'] = $list['price_percent'] .'%';
            }
        } 
    }    
    
    public function setMedia($isNewTheme = false)
    {
        parent::setMedia($isNewTheme);
        $this->js_files[] = _MODULE_DIR_ . 'an_productfields/views/js/back_fields.js';
        $this->css_files[_MODULE_DIR_ . 'an_productfields/views/css/back.css'] = 'all';
    }    
    
    public function renderList()
    {            
        return parent::renderList() . $this->module->topPromo();
    } 

    public function initToolbar()
    {
        if ($this->titleToolbar != '') {
            $this->toolbar_title[] = $this->l('Product Fields: ' . $this->titleToolbar);
        } else {
            $this->toolbar_title[] = $this->l('Product Fields: Values for Field');
        }

        parent::initToolbar();


        $this->toolbar_btn['new'] = array(
            'href' => self::$currentIndex.'&add'.$this->table.'&token='.$this->token.'&id_field='. $this->id_field,
            'desc' => $this->l('Add new')
        );
    }

    public function initPageHeaderToolbar()
    {
        if (empty($this->display)) {
            $this->page_header_toolbar_btn['add_field_value'] = array(
                'href' => self::$currentIndex.'&add'.$this->table.'&token='.$this->token.'&id_field='. $this->id_field,
                'desc' => $this->trans('Add new', array(), 'Admin.Actions'),
                'icon' => 'process-icon-new',
            );
        }
        parent::initPageHeaderToolbar();
    }

    public function initHeader()
    {
        parent::initHeader();
        $tabs = &$this->context->smarty->tpl_vars['tabs']->value;    
        $tabs = $this->module->createAdminTabs($tabs, $this->controller_name, 'AdminAnproductfieldsFields');
    }     
    
    public function initContent()
    {
        $this->context->smarty->assign('current_tab_level', 3);
        return parent::initContent();
    }   

    public function renderForm()
    {
        $this->initToolbar();

        $obj = $this->loadObject(true);
        if (!$obj) {
            return;
        } 

        if (!isset($this->objField) && isset($obj->id_field)){
            $this->objField = new anProductFields($obj->id_field, null, true);
        }
    
        $this->fields_form = array(
            'tinymce' => false,
            'legend' => ['title' => $this->l('Product Fields: Value for Field')],
            'input' => [],
            'buttons' => [
                [
                    'type' => 'submit',
                    'title' => $this->trans('Save', [], 'Admin.Actions'),
                    'icon' => 'process-icon-save',
                    'class' => 'pull-right',
                    'name' => 'submit'.$this->table
                ],
                [
                    'type' => 'submit',
                    'title' => $this->trans('Save and stay', [], 'Admin.Actions'),
                    'icon' => 'process-icon-save',
                    'class' => 'pull-right',
                    'name' => 'submit'.$this->table.'AndStay'
                ],
            ],
        );

        $this->fields_form['input'][] = [
            'type' => 'hidden',
            'label' => $this->l('id_field:'),
            'name' => 'id_field',
            'index' => 'id_field',
            'value' => $this->id_field,  
        ];

        $this->fields_form['input'][] = [
            'type' => 'switch',
            'name' => 'active',
            'label' => $this->trans('Active', [], 'Admin.Global'),
            'values' => [
                [
                    'id' => 'active_on',
                    'value' => 1,
                    'label' => $this->trans('Enabled', [], 'Admin.Global')
                ],
                [
                    'id' => 'active_off',
                    'value' => 0,
                    'label' => $this->l('Disabled')
                ]
            ],
        ];   
 
        $this->fields_form['input'][] = [
            'type' => 'text',
            'name' => 'title',
            'label' => $this->trans('Title', [], 'Admin.Global'),    
            'lang' => true,
        ];    

        $this->fields_form['input'][] = [
			'type' => 'radio',
			'label' => 'Type of price',
			'name' => 'type_price',
			'form_group_class' => 'js-pf-price-tax an-pf-hide js-an-pf-type-price',
			'values' => [
				[
					'id' => 'Amount',
					'value' => '0',
					'label' => $this->trans('Price', [], 'Admin.Global'),
				], 
				[
					'id' => 'Percent',
					'value' => '1',
					'label' => $this->l('Percentage of the price of a product')
				], 
			]
		];

        if ($this->objField->type != 'select' && $this->objField->type != 'multiselect'){
            $this->fields_form['input'][] = [
                'type' => 'select',
                'label' => $this->trans('Show price as', [], 'Admin.Global'),
                'name' => 'type_show_price',
                'default' => '0',
                'options' => [
                    'query' => array_merge(
                        [
                            ['id' => 'percent', 'name' => $this->l('Percent')],
                            ['id' => 'calculated', 'name' => $this->l('Calculated amount')]
                        //    ['id' => 'percentCalculated', 'name' => $this->l('Percent & Calculated amount')]
                        ]
                    ),
                    'id' => 'id',
                    'name' => 'name',
                ],
                'form_group_class' => 'an-pf-hide js-pf-type_show_price',
            ];   
        }     

        $this->fields_form['input'][] = [
            'type' => 'text',
            'name' => 'price',
            'label' => $this->trans('Price', [], 'Admin.Global'),
            'col' => 2,
            'form_group_class' => 'js-pf-price-tax an-pf-hide js-pf-price',
        ];         

        $this->fields_form['input'][] = [
            'type' => 'switch',
            'name' => 'apply_specific_price',
            'label' => $this->l('Apply specific price to field'),
            'form_group_class' => 'js-pf-price-tax an-pf-hide js-pf-price',
            'values' => [
                [
                    'id' => 'requirede_on',
                    'value' => 1,
                    'label' => $this->trans('Enabled', [], 'Admin.Global')
                ],
                [
                    'id' => 'required_off',
                    'value' => 0,
                    'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
            ],
            'desc' => $this->l('Used only for Specific prices set generally for the product and affecting the price in percentages.'),
        ];   

        $this->fields_form['input'][] = [
            'type' => 'text',
            'name' => 'price_percent',
            'label' => $this->l('Percent'),
            'col' => 2,
            'form_group_class' => 'js-pf-price-tax an-pf-hide js-pf-price_percent',
        ]; 


        $this->fields_form['input'][] = [
            'type' => 'select',
            'label' => $this->l('Custom tax'),
            'name' => 'custom_tax',
            'id' => 'custom_tax',
            'form_group_class' => 'js-pf-price-tax an-pf-hide',
            'options' => array(
                'query' => array_merge(
                    [['id_tax_rules_group' => '0', 'name' => $this->l('None')]],
                    TaxRulesGroupCore::getTaxRulesGroups()
                ),
                'id' => 'id_tax_rules_group',
                'name' => 'name',
            ),
            'desc' => $this->l('Used if the option "Apply tax to fields value" is enabled'),
        ]; 

        if ($this->objField->type == 'select'){
            $this->fields_form['input'][] = [
                'type' => 'textarea',
                'class' => 'autoload_rte',
                'name' => 'descr',
                'label' => $this->l('Description'),
                'lang' => true		
            ];	
        }

        if ($this->objField->type == 'radio' || $this->objField->type == 'checkbox' || Configuration::get('an_pf_select_value_image')){
            $this->fields_form['input'][] = [
                'type' => 'html',
                'name' => 'line1',
                'html_content' => 'hr',	
            ];
            
            $this->fields_form['input'][] = [
                'type' => 'file_lang',
                'label' => $this->l('File (only radiobox or checkbox)'),
                'lang' => true,
                'name' => 'file',
                'display_image' => true,
                'delete_url' => ''
            ];

            $this->fields_form['input'][] = [
                'type' => 'select',
                'label' => $this->l('If the file is a photo, display as:'),
                'name' => 'file_display',
                'default' => 'text',
                'options' => [
                    'query' => array_merge(
                        [
                            ['id' => 'miniature_left', 'name' => 'Miniature'], 
                            ['id' => 'miniature_left_and_link', 'name' => 'Miniature + Link'], 
                        //    ['id' => 'miniature_left_link', 'name' => 'Miniature left + Clickable'], 
                        //    ['id' => 'miniature_right', 'name' => 'Miniature right'], 
                        //    ['id' => 'miniature_right_link', 'name' => 'Miniature right + Clickable'], 
                            ['id' => 'link', 'name' => 'Link']
                        ]
                    ),
                    'id' => 'id',
                    'name' => 'name',
                ],
            ];

            $this->fields_form['input'][] = [
                'type' => 'text',
                'name' => 'file_text_link',
                'label' => $this->l('Text for file link'),    
                'lang' => true,
                'desc' => $this->l('If a link to a file is displayed. If the field is empty, the text will be default.'),
            ];           
        }
        
        if (Configuration::get('an_pf_new_multi_files')){

            $this->fields_form['input'][] = [
                'type' => 'html',
                'name' => 'line1',
                'html_content' => 'hr',	
            ];

            $this->fields_form['input'][] = [
                'type' => 'multi_files_lang',
                'label' => $this->l('Files'),
                'lang' => true,
                'name' => 'files_multi',
                'display_image' => true
            ];        
            $id_field = (int)Tools::getValue('id_field');
            $id_value = (int)Tools::getValue('id_value');
            $this->fields_value['files_multi'] = [];
            if ($id_value){
                $this->fields_value['files_multi'] = anProductFieldsFiles::getFiles(['id_value' => $id_value]);
            }
        }

        if (Configuration::get('an_pf_new_youtube')){
            $this->fields_form['input'][] = [
                'type' => 'text',
                'name' => 'youtube',
                'label' => $this->l('YouTube link'),    
                'lang' => true,
            ];   
        }

        if (Configuration::get('an_pf_customer_groups')){
            $this->fields_form['input'][] = [
                'type' => 'html',
                'name' => 'line1',
                'html_content' => 'hr',	
            ];  

            $allGroups = Group::getGroups($this->context->language->id);
            $groups = [];
            foreach ($allGroups as $id => $group){
                $groups[$id]['id'] = $group['id_group'];
                $groups[$id]['name'] = $group['name'];
            }
            $this->fields_form['input'][] = [
                'type' => 'an_checkbox',
                'label' => $this->l('Customer Groups'),                        
                'name' => 'customer_groups',
                'values' => [
                    'query' => $groups,
                    'id' => 'id',
                    'name' => 'name'
                ]
            ]; 
        }

        if ($this->display == 'add'){
            $this->fields_value['customer_groups'] = anPfsCustomerGroups::setGroups();
        } else {
            $this->fields_value['customer_groups'] = $this->object->getCustomerGroups();            
        }


        if (Shop::isFeatureActive()) {

            $this->fields_form['input'][] = [
                'type' => 'html',
                'name' => 'line1',
                'html_content' => 'hr',	
            ];

            $this->fields_form['input'][] = [
                'required' => true,
                'type' => 'shop',
                'label' => $this->l('Shop association'),
                'name' => 'checkBoxShopAsso',
            ];
        }
       
        return parent::renderForm();
    }
  
    public function processSave()
    {
        $languages = Language::getLanguages(false);
        $isUpdateImage = false;

        foreach ($languages as $lang) {
            if (isset($_FILES['file_'.$lang['id_lang']]) && isset($_FILES['file_'.$lang['id_lang']]['tmp_name'])
                    && !empty($_FILES['file_'.$lang['id_lang']]['tmp_name'])) {
                if ($error = $this->validateUpload($_FILES['file_'.$lang['id_lang']])) {
                    $this->errors[] = $error;                 
                }
            }
        }        
        
        if (!empty($this->errors)) {
            $this->display = 'edit';
            return false;
        }        
        
        $object = parent::processSave();
        
        if (isset($object->id) && $object->id) {

            $object->setCustomerGroups(Tools::getValue('customer_groups'));


            anProductFieldsFiles::uploadFiles(['id_value' => $object->id]);
            anProductFieldsFiles::deleteFiles(Tools::getValue('delete_files_multi'));
            anProductFieldsFiles::setMainFiles(Tools::getValue('main_files_multi'));


            anProductFieldsFiles::addYouTube(['id_value' => $object->id], 'youtube');



            
            
            $delete_file = Tools::getValue('delete_file');
            if ($delete_file && is_array($delete_file)){
                
                foreach ($delete_file as $id){
                    @unlink(anProductFieldsValues::imgDir . $object->file[$id]);
                    $object->file[$id] = '';
                }
                $isUpdateImage = true;
            }

            foreach ($languages as $lang) {

                if (isset($_FILES['file_'.$lang['id_lang']]) && isset($_FILES['file_'.$lang['id_lang']]['tmp_name'])
                    && !empty($_FILES['file_'.$lang['id_lang']]['tmp_name'])) {

                    $ext = substr($_FILES['file_'.$lang['id_lang']]['name'], strrpos($_FILES['file_'.$lang['id_lang']]['name'], '.') + 1);
                    $ext = Tools::strtolower($ext);
                    $fileName = md5(uniqid()) . '_' . $lang['id_lang'] . '.' . $ext;

                    if (!move_uploaded_file($_FILES['file_'.$lang['id_lang']]['tmp_name'], anProductFieldsValues::imgDir . $fileName)) {
                        return $this->displayError($this->trans('An error occurred while attempting to upload the file.', [], 'Admin.Notifications.Error'));
                    }
                    
                    if (isset($object->file[$lang['id_lang']]) && $object->file[$lang['id_lang']] !=''){
                       @unlink(anProductFieldsValues::imgDir . $object->file[$lang['id_lang']]);
                    }
                    $object->file[$lang['id_lang']] = $fileName;
                   
                    $isUpdateImage = true;
                }
            }

			if ($isUpdateImage){
                $object->save();		
			}   
        }


        $this->module->_clearCache('*');

        
        if (Tools::getIsset('submit'.$this->table.'AndStay')) {
            $this->redirect_after = $this->context->link->getAdminLink($this->controller_name).'&conf=4&updatean_productfields_fields_values&token='.$this->token.'&id_value='.$object->id.'&id_field=' . $this->id_field;
        }

        if (Tools::getIsset('submit'.$this->table)) {
            $this->redirect_after = $this->context->link->getAdminLink($this->controller_name).'&conf=3&token='.$this->token.'&id_value='.$object->id.'&id_field=' . $this->id_field;
        }
        
        return $object;
    }

    public function validateUpload($file)
    {
        $maxFileSize = 8000000;
        $filesTypesString = '';
        $filesTypesForValid = [];
        foreach (anProductFieldsValues::$typesFiles as $key => $item){
            if ($filesTypesString != ''){
                $filesTypesString .= ', ';
            }
            $filesTypesString .= '.'.$key;

            $filesTypesForValid[] = $key;
        }

        if ((int) $maxFileSize > 0 && $file['size'] > (int) $maxFileSize) {
            return Context::getContext()->getTranslator()->trans('Image is too large (%1$d kB). Maximum allowed: %2$d kB', [$file['size'] / 1024, $maxFileSize / 1024], 'Admin.Notifications.Error');
        }

        if (!ImageManager::isCorrectImageFileExt($file['name'], $filesTypesForValid) || preg_match('/\%00/', $file['name'])) {
            return Context::getContext()->getTranslator()->trans('Image format not recognized, allowed formats are: ' . $filesTypesString, [], 'Admin.Notifications.Error');
        }  

        if ($file['error']) {
            return Context::getContext()->getTranslator()->trans('Error while uploading image; please change your server\'s settings. (Error code: %s)', [$file['error']], 'Admin.Notifications.Error');
        }   
        
        return false;
    }    

    public function processDelete()
    {    
        $object = parent::processDelete();
        
        if (Tools::getIsset('delete'.$this->table)) {
            $this->redirect_after = $this->context->link->getAdminLink($this->controller_name).'&conf=3&token='.$this->token.'&id_value='.$object->id.'&id_field=' . $object->id_field;
        } 	
        return $object;
    }  

    public function postProcess()
    {
        $object = parent::postProcess();

        if (!empty($_POST) && is_array($_POST) && (int) Tools::getValue('submitFilter' . $this->list_id) || Tools::isSubmit('submitReset' . $this->list_id)) {
            $this->setRedirectAfter(self::$currentIndex . '&token=' . $this->token . (Tools::isSubmit('submitFilter' . $this->list_id) ? '&id_field='.$this->id_field.'&submitFilter' . $this->list_id . '=' . (int) Tools::getValue('submitFilter' . $this->list_id) : ''));
        }

        return $object;
    }


    public function processBulkStatusSelection($status)
    {
        $object = parent::processBulkStatusSelection($status);
        $this->redirect_after = $this->context->link->getAdminLink($this->controller_name, true, [], [
            'id_field' => $this->id_field,
            'conf' => 5
            ]);
        
        return $object;
    }

    public function ajaxProcessUpdatePositions()
    {
        $status = false;
		$position = 0;
        $widget = (array)Tools::getValue('value');

        foreach ($widget as $key => $item){
            $ids = explode('_', $item);
			$sql = 'UPDATE `' . _DB_PREFIX_ . $this->table .'` SET position="'.(int) $position.'" WHERE '.$this->identifier.'="'.(int) $ids['2'].'" ';
			Db::getInstance(_PS_USE_SQL_SLAVE_)->execute($sql);
			$position++;
        }

        if (count($widget) > 0){
            $status = true;
        }

        return $this->setJsonResponse(array(
            'success' => $status,
            'message' => $this->l($status ? 'Blocks reordered successfully' : 'An error occurred')
        ));
    }

    protected function setJsonResponse($response)
    {
        header('Content-Type: application/json; charset=utf8');
        print(json_encode($response));
        exit;
    }     

    protected function updateAssoShop($id_object)
    {
        if (!Shop::isFeatureActive()) {
            return;
        }

        $assos_data = $this->getSelectedAssoShop($this->table, $id_object);

        $exclude_ids = $assos_data;

        foreach (Db::getInstance()->executeS('SELECT id_shop FROM ' . _DB_PREFIX_ . 'shop') as $row) {
            if (!$this->context->employee->hasAuthOnShop($row['id_shop'])) {
                $exclude_ids[] = $row['id_shop'];
            }
        }

        Db::getInstance()->delete($this->table . '_shop', '`' . $this->identifier . '` = ' . (int) $id_object . ($exclude_ids ? ' AND id_shop NOT IN (' . implode(', ', $exclude_ids) . ')' : ''));

        $insert = array();

        foreach ($assos_data as $id_shop) {
            $insert[] = array(
                $this->identifier => $id_object,
                'id_shop' => (int) $id_shop,
            );
        }

        return Db::getInstance()->insert($this->table . '_shop', $insert, false, true, Db::INSERT_IGNORE);
    }

    protected function getSelectedAssoShop($table)
    {
        if (!Shop::isFeatureActive()) {
            return array();
        }

        $shops = Shop::getShops(true, null, true);

        if (count($shops) == 1 && isset($shops[0])) {
            return array($shops[0], 'shop');
        }

        $assos = array();

        if (Tools::isSubmit('checkBoxShopAsso_' . $table)) {
            foreach (Tools::getValue('checkBoxShopAsso_' . $table) as $id_shop => $value) {
                $assos[] = (int) $id_shop;
            }
        } else if (Shop::getTotalShops(false) == 1) {
            // if we do not have the checkBox multishop, we can have an admin with only one shop and being in multishop
            $assos[] = (int) Shop::getContextShopID();
        }

        return $assos;
    }    
    
}
