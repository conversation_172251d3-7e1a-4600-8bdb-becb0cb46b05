<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anPfsDependencies.php';

class AdminAnproductfieldsDependenciesController extends ModuleAdminController
{
    protected $_module = null;
    
	protected $position_identifier = 'id_dependence';
    protected $_defaultOrderBy = 'id_dependence';
    protected $_defaultOrderWay = 'ASC';
    protected $bulk_actions = [];


    protected $allow_export = false;

    public function __construct()
    {
        $this->bootstrap = true;
        $this->context = Context::getContext();
        $this->table = 'an_productfields_dependencies';
        $this->identifier = 'id_dependence';
        $this->className = 'anPfsDependencies';
        $this->lang = false;

        $this->addRowAction('edit');
        $this->addRowAction('delete');
 
        $this->name = 'AdminAnproductfieldsDependenciesController';
        
        parent::__construct();

        $this->_select = 'sl.`title`, fvl.`title` as `field_title`';

        $this->_join = '
            LEFT JOIN `' . _DB_PREFIX_ . 'an_productfields_fields_values_lang` sl 
                    ON (a.`id_value` = sl.`id_value` AND sl.`id_lang` = ' . (int) $this->context->language->id . ')
            LEFT JOIN `' . _DB_PREFIX_ . 'an_productfields_fields_lang` fvl 
                    ON (fvl.`id_field` = a.`id_field_dependence` AND fvl.`id_lang` = ' . (int) $this->context->language->id . ')
        ';

        $this->fields_list = [
            // 'id_dependence' => [
            //     'title' => $this->trans('ID', [], 'Admin.Global'), 
            //     'width' => 25,
            //     'search'  => false,
            // ],

            // 'id_field_dependence' => [
            //     'title' => $this->trans('id_field_dependence', [], 'Admin.Global'), 
            //     'search'  => false,
            // ],
            
            'field_title' => [
                'title' => $this->l('Field'), 
                'search'  => true,
                'type' => 'select',
                'filter_key' => 'id_field_dependence',
                'list' => anPfsDependencies::getPfsFieldsForSearch()                
            ],

            // 'id_value' => [
            //     'title' => $this->trans('id_value', [], 'Admin.Global'), 
            //     'search'  => false,
            // ],

            'title' => [
                'title' => $this->trans('Value', [], 'Admin.Global'), 
                'search'  => false,
            ]   
        ];

    }

    public function l($string, $class = null, $addslashes = false, $htmlentities = true)
    {
        return parent::l($string, pathinfo(__FILE__, PATHINFO_FILENAME), $addslashes, $htmlentities);
    }    
    
    public function setMedia($isNewTheme = false)
    {
        parent::setMedia($isNewTheme);
        $this->js_files[] = _MODULE_DIR_ . 'an_productfields/views/js/back-dependencies.js';
        Media::addJsDef([
            'an_pf_values_fields' => anPfsDependencies::getValuesFields()
        ]);
    }   
    
    public function renderList()
    {                    
        return parent::renderList() . $this->module->topPromo();
    } 

    public function initPageHeaderToolbar()
    {
        if (empty($this->display)) {
            $this->page_header_toolbar_btn['add_field'] = array(
                'href' => self::$currentIndex . '&addan_productfields_dependencies&token=' . $this->token,
                'desc' => $this->trans('Add new', array(), 'Admin.Actions'),
                'icon' => 'process-icon-new',
            );
        }
        parent::initPageHeaderToolbar();
    }

    public function initToolBarTitle()
    {
        $this->toolbar_title[] = $this->l('Product Fields: ' . $this->l('Dependencies'));
    }

    public function initHeader()
    {
        parent::initHeader();
        $tabs = &$this->context->smarty->tpl_vars['tabs']->value;    
        $tabs = $this->module->createAdminTabs($tabs, $this->controller_name);
    }

    public function initContent()
    {
        $this->context->smarty->assign('current_tab_level', 3);
        return parent::initContent();
    }    
    
    public function renderForm()
    {
        $this->initToolbar();
        $obj = $this->loadObject(true);
        if (!$obj) {
            return;
        } 

        $this->fields_form = array(
            'tinymce' => false,
            'legend' => ['title' => $this->l('Product Fields: ') . $this->l('Dependencies')],
            'input' => [],
            'buttons' => [
                [
                    'type' => 'submit',
                    'title' => $this->trans('Save', [], 'Admin.Actions'),
                    'icon' => 'process-icon-save',
                    'class' => 'pull-right',
                    'name' => 'submit'.$this->table
                ],
                [
                    'type' => 'submit',
                    'title' => $this->trans('Save and stay', [], 'Admin.Actions'),
                    'icon' => 'process-icon-save',
                    'class' => 'pull-right',
                    'name' => 'submit'.$this->table.'AndStay'
                ],
            ],
        );

  

		$this->fields_form['input'][] = [
			'type' => 'select',
			'label' => $this->l('Field and Value'),
			'name' => 'id_value',
            'class' => 'js-pf-dependencies-fieldvalue',
			'options' => [
                'default' => [
                    'value' => '0',
                    'label' => '-'
                ],
                'optiongroup' => [
                    'label' => 'label',
                    'query' => array_merge(
                        $obj->getListFieldsValues()
                    ),
                ],
                'options' => [
                    'query' => 'options',
                    'id' => 'id',
                    'name' => 'name'
                ]
            ],
		];  

        $this->fields_form['input'][] = [
            'type' => 'an_checkbox',
            'label' => $this->l('Fields'),                        
            'name' => 'dependencies_fields',
            'values' => [
                'query' => $obj->getPfsFieldsForForm(),
                'id' => 'id',
                'name' => 'name'
            ],
            'form_group_class' => 'js-pf-dependencies-fields'
        ]; 
        $this->fields_value['dependencies_fields'] = $obj->getDependenciesFieldsById();

        return parent::renderForm();
    }
  
    public function processSave()
    {
        if (!empty($this->errors)) {
            $this->display = 'edit';
            return false;
        }        
        
        $object = parent::processSave();
        
        if (Tools::getIsset('submit'.$this->table.'AndStay')) {
            $this->redirect_after = $this->context->link->getAdminLink($this->controller_name).'&conf=4&updatean_productfields_dependencies&token='.$this->token.'&'.$this->identifier.'='.$object->id;
        }
        
        return $object;
    }

    protected function setJsonResponse($response)
    {
        header('Content-Type: application/json; charset=utf8');
        $this->ajaxDie(json_encode($response));
    }    

     
    
}
