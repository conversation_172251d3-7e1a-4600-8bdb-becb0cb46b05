<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anProductFieldsGroups.php';

class AdminAnproductfieldsGroupsController extends ModuleAdminController
{
    protected $_module = null;
    
	protected $position_identifier = 'position';
    protected $_defaultOrderBy = 'position';
    protected $_defaultOrderWay = 'ASC';
    protected $bulk_actions = [];


    protected $allow_export = true;

    public function __construct()
    {
        $this->bootstrap = true;
        $this->context = Context::getContext();
        $this->table = 'an_productfields_groups';
        $this->identifier = 'id_group';
        $this->className = 'anProductFieldsGroups';
        $this->lang = true;

        $this->addRowAction('edit');
        $this->addRowAction('delete');
        $this->addRowAction('duplicate');
 
        $this->name = 'AdminAnproductfieldsGroupsController';
        
        parent::__construct();

        $this->fields_list = [
            'id_group' => [
                'title' => $this->trans('ID', [], 'Admin.Global'), 
                'width' => 25,
                'search'  => false,
            ],

            'title_group' => [
                'title' => $this->trans('Title', [], 'Admin.Global'), 
                'search'  => false,
            ],  

            'relation' => array(
                'title' => $this->trans('Relation', [], 'Admin.Global'), 
				'search'  => false,
				'width' => 150,
			),	    

            'position' => [
                'title' => $this->trans('Position', [], 'Admin.Global'), 
                'search'  => false,
                'position'  => true,
            ],    
                            
            'active' => [
                'title' => $this->trans('Active', [], 'Admin.Global'),
                'width' => 40,
                'active' => 'update',
                'align' => 'center',
                'type' => 'bool',
                'search'  => false,
                'orderby' => false
            ],      
        ];

        if (Shop::isFeatureActive() && Shop::getContext() != Shop::CONTEXT_ALL) {
            $this->_where .= ' AND a.' . $this->identifier . ' IN (
                SELECT sa.' . $this->identifier . '
                FROM `' . _DB_PREFIX_ . $this->table . '_shop` sa
                WHERE sa.id_shop IN (' . implode(', ', Shop::getContextListShopID()) . ')
            )';
        }
    }

    public function l($string, $class = null, $addslashes = false, $htmlentities = true)
    {
        return parent::l($string, pathinfo(__FILE__, PATHINFO_FILENAME), $addslashes, $htmlentities);
    }    

    public function getList($id_lang, $order_by = null, $order_way = null, $start = 0, $limit = null, $id_lang_shop = false)
    {
        parent::getList($id_lang, $order_by, $order_way, $start, $limit, $id_lang_shop);

        foreach ($this->_list as &$list) {

			switch ($list['relation']){
				case 1:
					$list['relation'] = $this->trans('Categories', [], 'Admin.Global');
					break;		

				case 2:
					$list['relation'] = $this->trans('Products', [], 'Admin.Global');
					break;	
					
				default:
					$list['relation'] = $this->trans('All', [], 'Admin.Global');
			}            
        } 
    }    
    
    public function setMedia($isNewTheme = false)
    {
        parent::setMedia($isNewTheme);

        $this->addJquery();
        $this->js_files[] = _MODULE_DIR_ . 'an_productfields/views/js/back_fields.js';
        $this->js_files[] = _MODULE_DIR_ . 'an_productfields/views/js/anSearchProducts.js';
        $this->css_files[_MODULE_DIR_ . 'an_productfields/views/css/anSearchProducts.css'] = 'all';
        $this->css_files[_MODULE_DIR_ . 'an_productfields/views/css/back.css'] = 'all';
    }   
    
    public function renderList()
    {                    
        return parent::renderList() . $this->module->topPromo();
    } 

    public function initPageHeaderToolbar()
    {
        if (empty($this->display)) {
            $this->page_header_toolbar_btn['add_field'] = array(
                'href' => self::$currentIndex . '&addan_productfields_groups&token=' . $this->token,
                'desc' => $this->trans('Add new', array(), 'Admin.Actions'),
                'icon' => 'process-icon-new',
            );
        }
        parent::initPageHeaderToolbar();
    }

    public function initToolBarTitle()
    {
        $this->toolbar_title[] = $this->l('Product Fields: ' . $this->trans('Groups', [], 'Admin.Global'));
    }

    public function initHeader()
    {
        parent::initHeader();
        $tabs = &$this->context->smarty->tpl_vars['tabs']->value;    
        $tabs = $this->module->createAdminTabs($tabs, $this->controller_name);
    }

    public function initContent()
    {
        $this->context->smarty->assign('current_tab_level', 3);
        return parent::initContent();
    }    
    
    public function renderForm()
    {
        $this->initToolbar();
         if (!$this->loadObject(true)) {
            return;
        } 

        $this->fields_form = array(
            'tinymce' => false,
            'legend' => ['title' => $this->l('Product Fields: ' . $this->trans('Groups', [], 'Admin.Global'))],
            'input' => [],
            'buttons' => [
                [
                    'type' => 'submit',
                    'title' => $this->trans('Save', [], 'Admin.Actions'),
                    'icon' => 'process-icon-save',
                    'class' => 'pull-right',
                    'name' => 'submit'.$this->table
                ],
                [
                    'type' => 'submit',
                    'title' => $this->trans('Save and stay', [], 'Admin.Actions'),
                    'icon' => 'process-icon-save',
                    'class' => 'pull-right',
                    'name' => 'submit'.$this->table.'AndStay'
                ],
            ],
        );

        $this->fields_form['input'][] = [
            'type' => 'switch',
            'name' => 'active',
            'label' => $this->trans('Active', [], 'Admin.Global'),
            'values' => [
                [
                    'id' => 'active_on',
                    'value' => 1,
                    'label' => $this->trans('Enabled', [], 'Admin.Global')
                ],
                [
                    'id' => 'active_off',
                    'value' => 0,
                    'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
            ],
        ];   

        $this->fields_form['input'][] = [
            'type' => 'text',
            'name' => 'title_group',
            'label' => $this->trans('Title', [], 'Admin.Global'),    
            'lang' => true,
        ];    

        $this->fields_form['input'][] = [
			'type' => 'radio',
			'label' => 'Relation',
			'name' => 'relation',
			'class' => 'an-sz-type-view',
			'values' => [
				[
					'id' => 'relation_all',
					'value' => '0',
					'label' => $this->trans('All', [], 'Admin.Global')
				], 
				[
					'id' => 'relation_categories',
					'value' => '1',
					'label' => $this->trans('Categories', [], 'Admin.Global')
				], 
				[
					'id' => 'relation_products',
					'value' => '2',
					'label' => $this->trans('Products', [], 'Admin.Global')
				]
			]
		];	



        $this->fields_form['input'][] = array(
            'type' => 'anSearchProductsList',
            'ignore' => true,
            'name' => 'productIds[]',
            'class' => 'js-an_productfields_products js-an_productfields-searchProducts-components',
            'classSarchInput' => 'js-an_productfields-search-input',
            'label' => '',
            'col' => 6,
            'searchProdutsController' => $this->context->link->getAdminLink('AdminAnproductfieldsAjax', true, [], ['ajax'=>1, 'action' =>'searchProducts']),
            'form_group_class' => 'js-an_productfields-search-group'
        );

        $this->fields_form['input'][] = array(
            'type' => 'text',
            'anSearchProductsInput' => true,
            'ignore' => true,
            'label' => $this->trans('Products', [], 'Admin.Global'),
            'name' => 'products_input',
            'size' => 50,
            'maxlength' => 50,
            'col' => 6,
            'placeholder' => $this->l('Search and add a product'),
            'desc' => $this->l('Search for a product by typing the first letters of his name'),
            'class' => 'js-an_productfields-search-input',
            'form_group_class' => 'js-an_productfields-search-group'
        );

		$this->fields_value['productIds[]'] = anProductFieldsGroups::getProducsByIdGroup(Tools::getValue('id_group'));



 		 
		$this->fields_form['input'][] = [
			'type'  => 'categories',
			'label' => $this->trans('Categories', [], 'Admin.Global'),
			'name'  => 'id_categories',
			'class' => 'js-sz-block-categories',
			'tree'  => [
				'id' => 'id_root_category',
				'use_checkbox' => true,
			    'selected_categories' => anProductFieldsGroups::getRelationCategories(Tools::getValue('id_group'))
			]
		];	

        if (Shop::isFeatureActive()) {
            $this->fields_form['input'][] = [
                'required' => true,
                'type' => 'shop',
                'label' => $this->l('Shop association'),
                'name' => 'checkBoxShopAsso',
            ];
        }
       
        return parent::renderForm();
    }
  
    public function processSave()
    {
        if (!empty($this->errors)) {
            $this->display = 'edit';
            return false;
        }        
        
        $object = parent::processSave();
        
        if (isset($object->id) && $object->id) {
            $this->object->saveRelation();
        }

        if (Tools::getIsset('submit'.$this->table.'AndStay')) {
            $this->redirect_after = $this->context->link->getAdminLink($this->controller_name).'&conf=4&updatean_productfields_groups&token='.$this->token.'&id_group='.$object->id;
        }
        
        return $object;
    }

    public function ajaxProcessUpdatePositions()
    {
        $status = false;
		$position = 0;
        $widget = (array)Tools::getValue('group');

        foreach ($widget as $key => $item){
            $ids = explode('_', $item);
			$sql = 'UPDATE `' . _DB_PREFIX_ . $this->table .'` SET position="'.(int) $position.'" WHERE '.$this->identifier.'="'.(int) $ids['2'].'" ';
			Db::getInstance(_PS_USE_SQL_SLAVE_)->execute($sql);
			$position++;
        }

        if (count($widget) > 0){
            $status = true;
        }

        return $this->setJsonResponse(array(
            'success' => $status,
            'message' => $this->l($status ? 'Blocks reordered successfully' : 'An error occurred')
        ));
    }

    protected function setJsonResponse($response)
    {
        header('Content-Type: application/json; charset=utf8');
        $this->ajaxDie(json_encode($response));
    }    

    protected function updateAssoShop($id_object)
    {
        if (!Shop::isFeatureActive()) {
            return;
        }

        $assos_data = $this->getSelectedAssoShop($this->table, $id_object);

        $exclude_ids = $assos_data;

        foreach (Db::getInstance()->executeS('SELECT id_shop FROM ' . _DB_PREFIX_ . 'shop') as $row) {
            if (!$this->context->employee->hasAuthOnShop($row['id_shop'])) {
                $exclude_ids[] = $row['id_shop'];
            }
        }

        Db::getInstance()->delete($this->table . '_shop', '`' . $this->identifier . '` = ' . (int) $id_object . ($exclude_ids ? ' AND id_shop NOT IN (' . implode(', ', $exclude_ids) . ')' : ''));

        $insert = array();

        foreach ($assos_data as $id_shop) {
            $insert[] = array(
                $this->identifier => $id_object,
                'id_shop' => (int) $id_shop,
            );
        }

        return Db::getInstance()->insert($this->table . '_shop', $insert, false, true, Db::INSERT_IGNORE);
    }

    protected function getSelectedAssoShop($table)
    {
        if (!Shop::isFeatureActive()) {
            return array();
        }

        $shops = Shop::getShops(true, null, true);

        if (count($shops) == 1 && isset($shops[0])) {
            return array($shops[0], 'shop');
        }

        $assos = array();

        if (Tools::isSubmit('checkBoxShopAsso_' . $table)) {
            foreach (Tools::getValue('checkBoxShopAsso_' . $table) as $id_shop => $value) {
                $assos[] = (int) $id_shop;
            }
        } else if (Shop::getTotalShops(false) == 1) {
            // if we do not have the checkBox multishop, we can have an admin with only one shop and being in multishop
            $assos[] = (int) Shop::getContextShopID();
        }

        return $assos;
    }    
    
}
