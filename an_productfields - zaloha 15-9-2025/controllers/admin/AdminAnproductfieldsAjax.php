<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

use PrestaShop\PrestaShop\Core\Domain\Product\Query\SearchProducts;

class AdminAnproductfieldsAjaxController extends ModuleAdminController
{
    public function initContent()
    {
        $result = [];
        if (Tools::isSubmit('action')) {
            $actionName = Tools::getValue('action', '') . 'Action';
            if (method_exists($this, $actionName)) {
                $result = $this->$actionName();
            }
        }

        die(json_encode($result));
    }    


    public function saveProductsFieldsAction()
    {
        $return = [
            'status' => true
        ];

        $idProduct = (int) Tools::getValue('id_product', false);
        $anpfsFieldStatus = Tools::getValue('anpfsFieldStatus');

        foreach ($anpfsFieldStatus as $idField => $status){

            Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute('
                DELETE FROM `'._DB_PREFIX_.'an_productfields_fields_products` 
                WHERE `id_field`=' . (int) $idField . ' AND `id_product` = ' . (int) $idProduct.  '
            ');

            $sql = 'INSERT INTO `'._DB_PREFIX_.'an_productfields_fields_products`  (`id_field`, `id_product`, `active`) 
			VALUES ("'.(int) $idField.'", "'.(int) $idProduct.'", "'.(int) $status.'" )';
			Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute($sql);
        }

        return $return;
    }
    
    public function getFieldsAction()
    {        
        $return = [
            'status' => true,
            'message' => '',
            'fields' => ''
        ];

        $id_product = (int) Tools::getValue('id_product', false);

        $fields = anProductFields::getFieldsByIdProduct($id_product);

        if (!count($fields)){
            $return['status'] = false;
        }

        $this->context->smarty->assign('widget', [
            'fields' => $fields,
            'urlPriceFormat' => $this->context->link->getModuleLink('an_productfields', 'ajax', [], true),
            'config' => [
                'label_total' => Configuration::get(an_productfields::PREFIX . 'label_total', $this->context->language->id),
                'title' => Configuration::get(an_productfields::PREFIX . 'title', $this->context->language->id),
                'text_header' => Configuration::get(an_productfields::PREFIX . 'text_header', $this->context->language->id),
                'text_footer' => Configuration::get(an_productfields::PREFIX . 'text_footer', $this->context->language->id),
                'priceDisplayMethod' => !Group::getDefaultPriceDisplayMethod() && Configuration::get(an_productfields::PREFIX . 'use_tax'),
                'calculationTotalFields' => Configuration::get(an_productfields::PREFIX . 'calculationTotalFields'),
            ]
        ]);

        $return['fields'] = $this->module->display($this->module->name, '/views/templates/adminCreateOrderFields.tpl');

        die(json_encode($return));
    }  
    
    public function searchProductsAction()
    {    
        //    $defaultCurrencyId = (int) $this->get('prestashop.adapter.legacy.configuration')->get('PS_CURRENCY_DEFAULT');
        $defaultCurrencyId = Configuration::get('PS_CURRENCY_DEFAULT');

        //$searchPhrase = $request->query->get('search_phrase');
        $searchPhrase = Tools::getValue('q');

        if (!$searchPhrase){
            return [];
        }

        $foundProducts = Product::searchByName((int) $this->context->language->id, pSQL($searchPhrase));


        $products = [];
        if (is_array($foundProducts)){
            foreach ($foundProducts as $fProduct){

                $productObj = new Product($fProduct['id_product'], false, $this->context->language->id);
                $coverId = Product::getCover($fProduct['id_product']);
                
                $coverPhoto = '';
                if (isset($coverId['id_image'])){
                    $coverPhoto = Context::getContext()->link->getImageLink(
                        $productObj->link_rewrite, 
                        $coverId['id_image'], 
                        ImageType::getFormattedName('cart')
                    );
                }

                $products[] = [
                    'id' => $fProduct['id_product'],
                    'name' => $fProduct['name'],
                    'reference' => $fProduct['reference'],
                    'cover' => $coverPhoto
                ];
            }     
        }   

        return $products;
        /*


        $currencyIsoCode = Currency::getIsoCodeById($defaultCurrencyId);
        
        // @var FoundProduct[] $foundProducts 
        $foundProducts = $this->get('prestashop.core.query_bus')->handle(new SearchProducts($searchPhrase, 10, $currencyIsoCode));

        $products = [];
        foreach ($foundProducts as $fProduct){
            $products[] = [
                'id' => $fProduct->getProductId(),
                'name' => $fProduct->getName()
            ];
        }

        return $products;
        */
    }

    public function getAttributesAction()
	{		
		$return = [
			'status' => true,
			'message' => '',
		];
		
		$productId = (int) Tools::getValue('productId');
		
		$productObj = new Product($productId, false, (int)$this->context->language->id);
		$attributeCombinations = $productObj->getAttributeCombinations((int)$this->context->language->id);
		
		
		
		$productsAttr = [];
		foreach ($attributeCombinations as $attributeCombination) {
			$productsAttr[$attributeCombination['id_product_attribute']]['comb'][] = $attributeCombination;	
		}
		
		$comb = [];
		foreach ($productsAttr as $k => $v){
			
			$line = '';
			foreach ($productsAttr[$k]['comb'] as $k3 => $v3){					
				$line .= $v3['group_name'] . ': ' . $v3['attribute_name'] . ' ';
			}
			
			$comb[$k] = $line;
		}
		
		
		$this->context->smarty->assign('comb', $comb);
		


		$select = $this->module->display($this->module->name, '/views/templates/admin/products-accessories-attributes.tpl');
		
		$return['select'] = $select;
		
		return $return;
	}	

    
}
