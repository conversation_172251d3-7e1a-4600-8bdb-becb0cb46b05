<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once _PS_MODULE_DIR_.'an_productfields/classes/anPfCustomCssJss.php';

class AdminPfCustomcssjsController extends ModuleAdminController
{
    protected $_module = null;

    public function __construct()
    {
        $this->bootstrap = true;
        $this->display = 'view';
 
		$this->name = 'AdminPfCustomcssjsController';
		$this->controller = 'AdminPfCustomcssjs';
		
        parent::__construct();
    }

    public function setMedia($isNewTheme = false)
    {
        parent::setMedia($isNewTheme);

        $this->addJquery();

        $this->css_files[_MODULE_DIR_ . 'an_productfields/views/codemirror/lib/codemirror.css'] = 'all';
        $this->css_files[_MODULE_DIR_ . 'an_productfields/views/codemirror/codemirror.css'] = 'all';
        
        $this->js_files[] = _MODULE_DIR_ . 'an_productfields/views/codemirror/lib/codemirror.js';
        $this->js_files[] = _MODULE_DIR_ . 'an_productfields/views/codemirror/mode/javascript/javascript.js';
        $this->js_files[] = _MODULE_DIR_ . 'an_productfields/views/codemirror/mode/css/css.js';
        $this->js_files[] = _MODULE_DIR_ . 'an_productfields/views/codemirror/codemirror.js';
    } 

    public function l($string, $class = null, $addslashes = false, $htmlentities = true)
    {
        return parent::l($string, pathinfo(__FILE__, PATHINFO_FILENAME), $addslashes, $htmlentities);
    }    

    public function initToolBarTitle()
    {
        $this->toolbar_title[] = $this->l('Product Fields: CSS / JS');
    }

    public function initHeader()
    {
        parent::initHeader();
        $tabs = &$this->context->smarty->tpl_vars['tabs']->value;    
        $tabs = $this->module->createAdminTabs($tabs, $this->controller_name);
    }
	
    public function initContent()
    {
        $this->context->smarty->assign('current_tab_level', 3);
        return parent::initContent();
    }    

    protected function getSettingsForm()
    {
        $this->context->smarty->assign([
            'desc_html_image_url' => __PS_BASE_URI__ . 'modules/an_productfields/views/img/',
        ]);
        
        $form['0']['form']['legend'] = [
            'title' => $this->trans('Custom CSS / JS', [], 'Admin.Global'),
        ];

        $form['0']['form']['submit'] = [
            'name' => 'save',
            'title' => $this->trans('Save', [], 'Admin.Actions'),
        ];
        
        $form['0']['form']['input'][] = [
			'type' => 'codemirror',
            'format' => 'css',
			'class' => 'autoload_rte',
			'name' => an_productfields::PREFIX . 'code_css_custom',
			'label' => $this->l('Code CSS'),
            'rows' => 5
        ];

        $form['0']['form']['input'][] = [
			'type' => 'codemirror',
            'format' => 'js',
			'class' => 'autoload_rte',
			'name' => an_productfields::PREFIX . 'code_js_custom',
			'label' => $this->l('Code JS'),
            'rows' => 5
        ];

        return $form;
    }

    public function renderView()
    {
        $languages = $this->context->controller->getLanguages();
  
        $helper = new HelperForm();
        $helper->show_toolbar = false;
        $helper->name_controller = $this->name;
        $helper->submit_action = $this->name;
        $helper->currentIndex = $this->context->link->getAdminLink($this->controller, false);
        $helper->token = Tools::getAdminTokenLite($this->controller);
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG') ? Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG') : 0;
        $helper->tpl_vars = [
            'uri' => $this->module->getPathUri(),
            'languages' => $languages,
            'id_language' => $this->context->language->id
        ];

        $helper->override_folder = 'pfcustomcssjs/';
        $helper->setTpl($this->override_folder);

        $form = $this->getSettingsForm();

        foreach($form as $subForm){
            foreach ($subForm['form']['input'] as $input){
                if (isset($input['lang']) && $input['lang']){
                    $value = [];
                    foreach ($languages  as $language){
                        $value[$language['id_lang']] = Configuration::get($input['name'], $language['id_lang']);
                    }
                    $helper->tpl_vars['fields_value'][$input['name']] = $value;
                } elseif ($input['type'] == 'codemirror'){
                    $helper->tpl_vars['fields_value'][$input['name']] = anPfCustomCssJss::getFileContent(Configuration::get($input['name']));                
                } else {
                    $helper->tpl_vars['fields_value'][$input['name']] = Configuration::get($input['name']);
                }
            }
        }
                	
        return $helper->generateForm($form) . $this->module->topPromo();		
	}

    public function postProcess()
    {    
        $form = $this->getSettingsForm();

        $isSubmit = false;
        foreach($form as $subForm){
            if (Tools::isSubmit($subForm['form']['submit']['name'])){
                $isSubmit = true;
            }
        }
        
        if ($isSubmit) {
            foreach($form as $subForm){
                foreach ($subForm['form']['input'] as $input){

                    if ($input['type'] == 'codemirror'){
                        anPfCustomCssJss::updateFile(Tools::getValue($input['name']), $input['format']);
                    } else {
                        $html = false;
                    
                        if (isset($input['html']) && $input['html']){
                            $html = true;
                        }
                        
                        Configuration::updateValue($input['name'], Tools::getValue($input['name']), $html); 
                    }
                }
            }

            $currentIndex = $this->context->link->getAdminLink($this->controller, false);
            $token = Tools::getAdminTokenLite($this->controller);            
            Tools::redirectAdmin($currentIndex . '&token=' . $token . '&conf=4');
        }
		return true;
    }
}
