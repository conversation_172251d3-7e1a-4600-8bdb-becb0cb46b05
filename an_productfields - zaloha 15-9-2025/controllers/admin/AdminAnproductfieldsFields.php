<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anProductFields.php';
require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anProductFieldsValues.php';
require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anProductFieldsGroups.php';
require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anPfsPA.php';
require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anPfsCustomerGroups.php';

class AdminAnproductfieldsFieldsController extends ModuleAdminController
{
    protected $_module = null;
    
	protected $position_identifier = 'position';
    protected $_defaultOrderBy = 'position';
    protected $_defaultOrderWay = 'ASC';
    protected $bulk_actions = [];


    protected $allow_export = true;

    public function __construct()
    {
        $this->bootstrap = true;
        $this->context = Context::getContext();
        $this->table = 'an_productfields_fields';
        $this->identifier = 'id_field';
        $this->className = 'anProductFields';
        $this->lang = true;

        $this->addRowAction('edit');
        $this->addRowAction('delete');
        $this->addRowAction('duplicate');
 
        $this->name = 'AdminAnproductfieldsFieldsController';
        
        parent::__construct();

        $this->fields_list = [
            'id_field' => [
                'title' => $this->trans('ID', [], 'Admin.Global'), 
                'width' => 25,
                'search'  => true,
            ],

            'title' => [
                'title' => $this->trans('Title', [], 'Admin.Global'), 
                'search'  => true,
            ],  

            'note' => [
                'title' => $this->trans('Note', [], 'Admin.Global'), 
                'search'  => true,
            ],             
            
            'type' => [
                'title' => $this->trans('Type', [], 'Admin.Global'), 
                'search'  => false,
                // 'type' => 'select',
                // 'filter_key' => 'type',
                // 'list' => array_merge(
                //     anProductFields::getTypesFileds()
				// )
            ], 

            'price' => [
                'title' => $this->trans('Price', [], 'Admin.Global'), 
                'search'  => false,
            ], 

            'position' => [
                'title' => $this->trans('Position', [], 'Admin.Global'), 
                'search'  => false,
                'position'  => true,
            ],            

            'required' => [
                'title' => $this->trans('Required', [], 'Admin.Global'),
                'align' => 'center',
                'type' => 'bool',
                'search'  => false,
                'orderby' => false
            ],

            'relation' => array(
				'title' => $this->l('Relation'), 
				'search'  => false,
				'width' => 150,
			),	    

            'not_global' => array(
				'title' => $this->l('Not global'), 
				'search'  => false,
                'align' => 'center',
                'type' => 'bool'                
			),	            

            'active' => [
                'title' => $this->trans('Active', [], 'Admin.Global'),
                'width' => 40,
                'active' => 'update',
                'align' => 'center',
                'type' => 'bool',
                'search'  => false,
                'orderby' => false
            ],

            'viewBut' => [
                'title' => $this->trans('View', [], 'Admin.Global'),
                'align' => 'center',
                'search'  => false,
                'orderby' => false,
                'type' => 'nofilter',
            ],            

        ];

        if (Shop::isFeatureActive() && Shop::getContext() != Shop::CONTEXT_ALL) {
            $this->_where .= ' AND a.' . $this->identifier . ' IN (
                SELECT sa.' . $this->identifier . '
                FROM `' . _DB_PREFIX_ . $this->table . '_shop` sa
                WHERE sa.id_shop IN (' . implode(', ', Shop::getContextListShopID()) . ')
            )';
        }
    }

    public function l($string, $class = null, $addslashes = false, $htmlentities = true)
    {
        return parent::l($string, pathinfo(__FILE__, PATHINFO_FILENAME), $addslashes, $htmlentities);
    }    

    public function getList($id_lang, $order_by = null, $order_way = null, $start = 0, $limit = null, $id_lang_shop = false)
    {
        parent::getList($id_lang, $order_by, $order_way, $start, $limit, $id_lang_shop);

        $typesFields = anProductFields::getTypesFileds();

        foreach ($this->_list as &$list) {

            if ($list['type_price'] == 1){
                $list['price'] = $list['price_percent'] .'%';
            }

            $list['viewBut'] = '';
            if (isset($typesFields[$list['type']]) && $typesFields[$list['type']]['values']) {
                $this->context->smarty->assign('viewBut', ['action' => $this->trans('Values', [], 'Admin.Global')]);
                $list['viewBut'] = $this->context->link->getAdminLink('AdminAnproductfieldsValues'). '&id_field='. $list['id_field'];              
                $list['price'] = '-';
                $list['type'] = $typesFields[$list['type']]['name'];
            }

			switch ($list['relation']){
				case 1:
					$list['relation'] = $this->trans('Categories', [], 'Admin.Global');
					break;		

				case 2:
					$list['relation'] = $this->trans('Products', [], 'Admin.Global');
					break;	

                case 3:
                    $list['relation'] = $this->trans('Groups', [], 'Admin.Global');
                    break;          
                    
                case 4:
                    $list['relation'] = $this->l('Product + attribute');
                    break;	
					
				default:
					$list['relation'] = $this->trans('All', [], 'Admin.Global');
			}            

        } 
    }    
    
    public function setMedia($isNewTheme = false)
    {
        parent::setMedia($isNewTheme);
        $this->js_files[] = _MODULE_DIR_ . 'an_productfields/views/js/back_fields.js';
        $this->js_files[] = _MODULE_DIR_ . 'an_productfields/views/js/anSearchProducts.js';
        $this->js_files[] = _MODULE_DIR_ . 'an_productfields/views/js/back_product_attributes.js';
        $this->css_files[_MODULE_DIR_ . 'an_productfields/views/css/anSearchProducts.css'] = 'all';
        $this->css_files[_MODULE_DIR_ . 'an_productfields/views/css/back.css'] = 'all';


        $this->js_files[] = _MODULE_DIR_ . 'an_productfields/views/select2/js/select2.full.min.js';
        $this->css_files[_MODULE_DIR_ . 'an_productfields/views/select2/css/select2.min.css'] = 'all';
    }    
    
    public function renderList()
    {                    
        return parent::renderList() . $this->getImportForm() . $this->module->topPromo();
    } 

    public function initPageHeaderToolbar()
    {
        if (empty($this->display)) {
            $this->page_header_toolbar_btn['add_field'] = array(
                'href' => self::$currentIndex . '&addan_productfields_fields&token=' . $this->token,
                'desc' => $this->trans('Add new', array(), 'Admin.Actions'),
                'icon' => 'process-icon-new',
            );
        }
        parent::initPageHeaderToolbar();
    }

    public function getImportForm()
    {
        $form['form']['legend'] = [
            'title' => $this->trans('Import', [], 'Admin.Actions'),
        ];

        $form['form']['input'][] = [
            'type' => 'switch',
            'name' => 'an_delete_fields_before',
            'label' => $this->l('Delete current fields before import'),
            'values' => [
                [
                    'id' => 'active_on',
                    'value' => 1,
                    'label' => $this->l('Yes')
                ],
                [
                    'id' => 'active_off',
                    'value' => 0,
                    'label' => $this->l('No')
                ]
            ],
        ];  

        $form['form']['input'][] = [
            'type' => 'file',
            'label' => $this->l('Import from JSON'),
            'name' => 'an_import',
        ];

        $form['form']['submit'] = [
            'name' => 'import',
            'title' => $this->trans('Import', [], 'Admin.Actions'),
        ];

        $languages = $this->context->controller->getLanguages();
  
        $helper = new HelperForm();
        $helper->show_toolbar = false;
        $helper->name_controller = $this->name;
        $helper->submit_action = $this->name;
        $helper->currentIndex = $this->context->link->getAdminLink('AdminAnproductfieldsFields', false);
        $helper->token = Tools::getAdminTokenLite('AdminAnproductfieldsFields');
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG') ? Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG') : 0;
        $helper->tpl_vars = [
            'uri' => $this->module->getPathUri(),
            'languages' => $languages,
            'id_language' => $this->context->language->id
        ];
        
        foreach ($form['form']['input'] as $input){
            if ($input['name'] == 'an_delete_fields_before'){
                $helper->tpl_vars['fields_value'][$input['name']] = 0;
            } else {
                $helper->tpl_vars['fields_value'][$input['name']] = '';
            }
        }

        return $helper->generateForm([$form]);
    }

    public function processExport($text_delimiter = '"')
    {
        $fields = anProductFields::getAllFields();
        // clean buffer
        if (ob_get_level() && ob_get_length() > 0) {
            ob_clean();
        }

        header('Content-Type: application/json; charset=utf8');
        header('Content-Type: application/force-download; charset=UTF-8');
        header('Cache-Control: no-store, no-cache');
        header('Content-Disposition: attachment; filename="export-fields ' . date('Y-m-d h i s') . '.json"');

        echo json_encode($fields, JSON_PRETTY_PRINT);
        die;
    }

    public function postProcess(){
        
        if (isset($_FILES['an_import']) && $_FILES['an_import']['size'] > 0) {
            
            if (_PS_MODE_DEMO_) {
                $this->errors[] = $this->module->textDemoMode;
                return false;
            }

            if (Tools::substr($_FILES['an_import']['name'], -5) != '.json') {
                $this->errors[] = $this->l('Please check the file you are trying to upload. The format is required to be JSON.');
            } else {
                $data = json_decode(Tools::file_get_contents($_FILES['an_import']['tmp_name']), true);

                if(Tools::getValue('an_delete_fields_before') && $data){
                    Db::getInstance()->Execute('TRUNCATE TABLE `' . _DB_PREFIX_ . 'an_productfields_fields`');
                    Db::getInstance()->Execute('TRUNCATE TABLE `' . _DB_PREFIX_ . 'an_productfields_fields_shop`');
                    Db::getInstance()->Execute('TRUNCATE TABLE `' . _DB_PREFIX_ . 'an_productfields_fields_lang`');
                    Db::getInstance()->Execute('TRUNCATE TABLE `' . _DB_PREFIX_ . 'an_productfields_fields_relations`');
                    Db::getInstance()->Execute('TRUNCATE TABLE `' . _DB_PREFIX_ . 'an_productfields_fields_values`');
                    Db::getInstance()->Execute('TRUNCATE TABLE `' . _DB_PREFIX_ . 'an_productfields_fields_values_shop`');
                    Db::getInstance()->Execute('TRUNCATE TABLE `' . _DB_PREFIX_ . 'an_productfields_fields_values_lang`');
                }

                if (!anProductFields::importJsonAllFields($data)){
                    $this->errors[] = $this->l('Invalid file to import.');
                }
            }
        }

        if (Tools::getIsset('duplicate'.$this->table)){
            anProductFields::duplicate((int)Tools::getValue($this->identifier));
        }
            
        if (Tools::getIsset('name')) {
            
            if (!empty($this->errors)) {
                $this->display = 'edit';
                return false;
            }
        }
        
        return parent::postProcess();
    }

    public function initToolBarTitle()
    {
        $this->toolbar_title[] = $this->l('Product Fields: Fields');
    }

    public function initHeader()
    {
        parent::initHeader();
        $tabs = &$this->context->smarty->tpl_vars['tabs']->value;    
        $tabs = $this->module->createAdminTabs($tabs, $this->controller_name);
    }  
    
    public function initContent()
    {
        $this->context->smarty->assign('current_tab_level', 3);

        return parent::initContent();
    }    
    
    public function renderForm()
    {
        $this->initToolbar();

        $obj = $this->loadObject(true);
        if (!$obj) {
            return;
        } 
        
        $this->fields_form = array(
            'tinymce' => false,
            'legend' => ['title' => $this->l('Product Fields: Field')],
            'input' => [],
            'buttons' => [
                [
                    'type' => 'submit',
                    'title' => $this->trans('Save', [], 'Admin.Actions'),
                    'icon' => 'process-icon-save',
                    'class' => 'pull-right',
                    'name' => 'submit'.$this->table
                ],
                [
                    'type' => 'submit',
                    'title' => $this->trans('Save and stay', [], 'Admin.Actions'),
                    'icon' => 'process-icon-save',
                    'class' => 'pull-right',
                    'name' => 'submit'.$this->table.'AndStay'
                ],
            ],
        );

        $this->fields_form['input'][] = [
            'type' => 'switch',
            'name' => 'active',
            'label' => $this->trans('Active', [], 'Admin.Global'),
            'values' => [
                [
                    'id' => 'active_on',
                    'value' => 1,
                    'label' => $this->trans('Enabled', [], 'Admin.Global')
                ],
                [
                    'id' => 'active_off',
                    'value' => 0,
                    'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
            ],
        ];   

        $this->fields_form['input'][] = [
            'type' => 'text',
            'name' => 'title',
            'label' => $this->trans('Title', [], 'Admin.Global'),    
            'lang' => true,
            'required' => true
        ];    

        $this->fields_form['input'][] = [
            'type' => 'text',
            'name' => 'note',
            'label' => $this->trans('Note', [], 'Admin.Global'),    
            'desc' => $this->l('Only for back office'),
        ];          

        $this->fields_form['input'][] = [
			'type' => 'html',
			'name' => 'line1',
			'html_content' => 'hr',	
        ];		        

		$this->fields_form['input'][] = [
			'type' => 'select',
			'label' => $this->trans('Type', [], 'Admin.Global'),
			'name' => 'type',
            'default' => 'text',
            'class' => 'js-pf-field-type',
			'options' => [
				'query' => array_merge(
                    anProductFields::getTypesFileds()
				),
				'id' => 'id',
				'name' => 'name',
            ],
		];    

        $fieldId = (int)Tools::getValue('id_field');
        if ($fieldId){
           $values = anProductFieldsValues::getValuesByIdField($fieldId);
           $this->fields_form['input'][] = [
               'type' => 'select',
               'label' => $this->l('Default value'),
               'name' => 'id_default_value',
               'default' => 'text',
               'form_group_class' => 'js-pf-default-value',
               'options' => [
                   'query' => array_merge(
                        ['0' => '-'],
                        $values
                   ),
                   'id' => 'id_value',
                   'name' => 'title',
               ],
           ];   
        }



        $this->fields_form['input'][] = [
			'type' => 'radio',
			'label' => 'Type of price',
			'name' => 'type_price',
			'form_group_class' => 'js-pf-price-tax an-pf-hide js-an-pf-type-price',
			'values' => [
				[
					'id' => 'typePriceAmount',
					'value' => '0',
					'label' => $this->trans('Price', [], 'Admin.Global'),
				], 
				[
					'id' => 'typePricePercent',
					'value' => '1',
					'label' => $this->l('Percentage of the price of a product')
				], 
				[
					'id' => 'typePricePerCharacter',
					'value' => '2',
					'label' => $this->l('Per character')
				]
			]
		];

		$this->fields_form['input'][] = [
			'type' => 'select',
			'label' => $this->trans('Show price as', [], 'Admin.Global'),
			'name' => 'type_show_price',
            'default' => '0',
			'options' => [
				'query' => array_merge(
                    [
                        ['id' => 'percent', 'name' => $this->l('Percent')],
                        ['id' => 'calculated', 'name' => $this->l('Calculated amount')]
                    //    ['id' => 'percentCalculated', 'name' => $this->l('Percent & Calculated amount')]
                    ]
				),
				'id' => 'id',
				'name' => 'name',
            ],
            'form_group_class' => 'an-pf-hide js-pf-type_show_price',
		];        

        $this->fields_form['input'][] = [
            'type' => 'text',
            'name' => 'price',
            'label' => $this->trans('Price', [], 'Admin.Global'),
            'col' => 2,
            'form_group_class' => 'js-pf-price-tax an-pf-hide js-pf-price',
        ];   
        
        $this->fields_form['input'][] = [
            'type' => 'switch',
            'name' => 'apply_specific_price',
            'label' => $this->l('Apply specific price to field'),
            'form_group_class' => 'js-pf-price-tax an-pf-hide js-pf-price',
            'values' => [
                [
                    'id' => 'requirede_on',
                    'value' => 1,
                    'label' => $this->trans('Enabled', [], 'Admin.Global')
                ],
                [
                    'id' => 'required_off',
                    'value' => 0,
                    'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
            ],
            'desc' => $this->l('Used only for Specific prices set generally for the product and affecting the price in percentages.'),
        ];   

        $this->fields_form['input'][] = [
            'type' => 'number',
            'name' => 'free_characters',
            'label' => $this->l('Free characters'),
            'min' => 0,
            'max' => 10000,
            'col' => 2,
            'form_group_class' => 'an-pf-hide js-pf-free_characters'
        ];          

        $this->fields_form['input'][] = [
            'type' => 'switch',
            'name' => 'ignore_spaces',
            'label' => $this->l('Ignore spaces'),
            'form_group_class' => 'an-pf-hide js-pf-free_characters',
            'values' => [
                [
                    'id' => 'requirede_on',
                    'value' => 1,
                    'label' => $this->trans('Enabled', [], 'Admin.Global')
                ],
                [
                    'id' => 'required_off',
                    'value' => 0,
                    'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
            ],
        ];        

        $this->fields_form['input'][] = [
            'type' => 'text',
            'name' => 'price_percent',
            'label' => $this->l('Percent'),
            'col' => 2,
            'form_group_class' => 'js-pf-price-tax an-pf-hide js-pf-price_percent',
        ];

        $this->fields_form['input'][] = [
            'type' => 'select',
            'label' => $this->l('Custom tax'),
            'name' => 'custom_tax',
            'id' => 'custom_tax',
            'form_group_class' => 'js-pf-price-tax an-pf-hide',
            'options' => array(
                'query' => array_merge(
                    [['id_tax_rules_group' => '0', 'name' => $this->l('None')]],
                    TaxRulesGroupCore::getTaxRulesGroups()
                ),
                'id' => 'id_tax_rules_group',
                'name' => 'name',
            ),
            'desc' => $this->l('Used if the option "Apply tax to fields value" is enabled'),
        ]; 

		// $this->fields_form['input'][] = [
		// 	'type' => 'select',
		// 	'label' => $this->l('Validation'),
		// 	'name' => 'validation',
        //     'form_group_class' => 'js-pf-validation an-pf-hide',
        //     'default' => 'text',
		// 	'options' => [
		// 		'query' => array_merge(
        //            [['id' => '0', 'name' => $this->l('None')]],
        //            anProductFields::getValidationsList()
		// 		),
		// 		'id' => 'id',
		// 		'name' => 'name',
        //     ],
		// ];         

        $this->fields_form['input'][] = [
            'type' => 'text',
            'name' => 'num_min',
            'label' => $this->l('Min value'),
            'col' => 2,
            'form_group_class' => 'an-pf-hide js-pf-num-minmax',
        ];

        $this->fields_form['input'][] = [
            'type' => 'text',
            'name' => 'num_max',
            'label' => $this->l('Max value'),
            'col' => 2,
            'form_group_class' => 'an-pf-hide js-pf-num-minmax',
        ];

        $this->fields_form['input'][] = [
            'type' => 'switch',
            'name' => 'num_multiply_price',
            'label' => $this->l('Price of the field multiply by entered value'),
            'form_group_class' => 'an-pf-hide js-pf-num-minmax',
            'desc' => [
                $this->l('If the option is enabled, then the price of the field will be multiplied by the value from the field.'),
                $this->l('For example price of field: 10, entered value: 5, than 10 * 5 = 50')
            ],
            'values' => [
                [
                    'id' => 'requirede_on',
                    'value' => 1,
                    'label' => $this->trans('Enabled', [], 'Admin.Global')
                ],
                [
                    'id' => 'required_off',
                    'value' => 0,
                    'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
            ],
        ];

        $this->fields_form['input'][] = [
            'type' => 'number',
            'name' => 'max_length',
            'label' => $this->l('Max Length'),
            'form_group_class' => 'js-pf-max_length an-pf-hide',
            'col' => 2,
            'min' => 0,
            'max' => 10000,
            'desc' => $this->l('0 - no length limit'),
        ]; 
        
        $this->fields_form['input'][] = [
            'type' => 'switch',
            'name' => 'char_counter',
            'label' => $this->l('Character counter'),
            'form_group_class' => 'js-pf-char_counter an-pf-hide',
            'values' => [
                [
                    'id' => 'requirede_on',
                    'value' => 1,
                    'label' => $this->trans('Enabled', [], 'Admin.Global')
                ],
                [
                    'id' => 'required_off',
                    'value' => 0,
                    'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
            ],
        ];

        $this->fields_form['input'][] = [
            'type' => 'number',
            'name' => 'field_max_file_size',
            'label' => $this->l('Max file size'),
            'form_group_class' => 'js-pf-max_file_size an-pf-hide',
            'col' => 2,
            'min' => 0,
            'max' => 50000
        ];

        $typesFiles = anPfsCustomerFiles::$typesFiles;
        $typesFilesCheckbox = [];
        foreach ($typesFiles as $fileFormat => $fileType){
            $typesFilesCheckbox[] = [
                'id' => $fileFormat,
                'name' => $fileFormat
            ];
        }
        
        $this->fields_form['input'][] = [
            'type' => 'an_checkbox',
            'label' => $this->l('Allow files format'),                        
            'name' => 'allow_files_format_array',
            'values' => [
                'query' => $typesFilesCheckbox,
                'id' => 'id',
                'name' => 'name'
            ],
            'form_group_class' => 'js-pf-allow_files_format an-pf-hide'
        ]; 
        $this->fields_value['allow_files_format_array'] = $obj->getAllowFilesFormat();

        $this->fields_form['input'][] = [
            'type' => 'file_lang',
            'label' => $this->l('File'),
            'lang' => true,
            'name' => 'file',
            'form_group_class' => 'js-pf-file an-pf-hide'
        ];        

        $this->fields_form['input'][] = [
            'type' => 'switch',
            'name' => 'required',
            'label' => $this->trans('Required', [], 'Admin.Global'),
            'values' => [
                [
                    'id' => 'requirede_on',
                    'value' => 1,
                    'label' => $this->trans('Enabled', [], 'Admin.Global')
                ],
                [
                    'id' => 'required_off',
                    'value' => 0,
                    'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
            ],
        ];        

        $this->fields_form['input'][] = [
            'type' => 'text',
            'name' => 'placeholder',
            'label' => $this->l('Placeholder'),
            'form_group_class' => 'js-pf-placeholder an-pf-hide',
            'lang' => true,
        ];  

        $this->fields_form['input'][] = [
            'type' => 'text',
            'name' => 'descr',
            'label' => $this->l('Description'),
        //    'form_group_class' => 'js-pf-descr an-pf-hide',
            'lang' => true,
        ];  

        $this->fields_form['input'][] = [
            'type' => 'text',
            'name' => 'tooltip',
            'label' => $this->l('Hint (tooltip)'),
            'form_group_class' => '',
            'lang' => true,
        ];  
        



		// $this->fields_form['input'][] = [
		// 	'type' => 'select',
		// 	'label' => $this->l('If the file is a photo, display as:'),
		// 	'name' => 'file_display',
        //     'default' => 'text',
		// 	'options' => [
		// 		'query' => array_merge(
        //             [
        //                 ['id' => 'miniature_left', 'name' => 'Miniature'], 
        //                 ['id' => 'miniature_left_and_link', 'name' => 'Miniature + Link'], 
        //                 ['id' => 'link', 'name' => 'Link']
        //             ]
		// 		),
		// 		'id' => 'id',
		// 		'name' => 'name',
        //     ],
		// ];

        // $this->fields_form['input'][] = [
        //     'type' => 'text',
        //     'name' => 'file_text_link',
        //     'label' => $this->l('Text for file link'),    
        //     'lang' => true,
        //     'desc' => $this->l('If a link to a file is displayed. If the field is empty, the text will be default.'),
        // ];






        $this->fields_form['input'][] = [
			'type' => 'html',
			'name' => 'line1',
			'html_content' => 'hr',	
        ];		

		$this->fields_form['input'][] = [
			'type' => 'radio',
			'label' => 'Relation',
			'name' => 'relation',
			'class' => 'an-sz-type-view',
			'values' => [
				[
					'id' => 'relation_all',
					'value' => '0',
					'label' => $this->trans('All', [], 'Admin.Global')
				], 
				[
					'id' => 'relation_categories',
					'value' => '1',
					'label' => $this->trans('Categories', [], 'Admin.Global')
				], 
				[
					'id' => 'relation_products',
					'value' => '2',
					'label' => $this->trans('Products', [], 'Admin.Global')
                ],
                [
					'id' => 'relation_groups',
					'value' => '3',
					'label' => $this->trans('Groups', [], 'Admin.Global')
				],             
                // [
				// 	'id' => 'relation_product_attribute',
				// 	'value' => '4',
				// 	'label' => $this->l('Product + attributes')
				// ]                
			]
		];	

		// $this->fields_form['input'][] = [
        //     'type' => 'selectProductsAttributes',
        //     'label' => $this->l('Products and Attributes'),	
        //     'required' => false,
        //     'name' => 'products_accessories',
        //     'class' => 'js-example-basic-single js-an-dt-select-accessories an-dt-select-accessories js-anpfs-productsAttributes',
        //     'productAttributesList' => anPfsPA::getProductsAttributesQuery(Tools::getValue('id_field')),
        //     'options' => [
        //         'query' => anPfsPA::getProductsQuery(),
        //         'id' => 'id',
        //         'name' => 'name'
        //     ],
        //     'searchProdutsController' => $this->context->link->getAdminLink('AdminAnproductfieldsAjax', true, [], ['ajax'=>1, 'action' =>'getAttributes']),
        //     'form_group_class' => 'js-an_productfields-productsAttributes'
        // ];		
        // $this->fields_value['products_accessories'] = anPfsPA::getProductsAttributesQuery(Tools::getValue('id_field'));
 

        $this->fields_form['input'][] = array(
            'type' => 'anSearchProductsList',
            'ignore' => true,
            'name' => 'productIds[]',
            'class' => 'js-an_productfields_products js-an_productfields-searchProducts-components',
            'classSarchInput' => 'js-an_productfields-search-input',
            'label' => '',
            'col' => 6,
            'searchProdutsController' => $this->context->link->getAdminLink('AdminAnproductfieldsAjax', true, [], ['ajax'=>1, 'action' =>'searchProducts']),
            'form_group_class' => 'js-an_productfields-search-group'
        );
        $this->fields_value['productIds[]'] = anProductFields::getProducsByIdField(Tools::getValue('id_field'));

        $this->fields_form['input'][] = array(
            'type' => 'text',
            'anSearchProductsInput' => true,
            'ignore' => true,
            'label' => $this->trans('Products', [], 'Admin.Global'),
            'name' => 'products_input',
            'size' => 50,
            'maxlength' => 50,
            'col' => 6,
            'placeholder' => $this->l('Search and add a product'),
            'desc' => $this->l('Search for a product by typing the first letters of his name'),
            'class' => 'js-an_productfields-search-input',
            'form_group_class' => 'js-an_productfields-search-group'
        );
 		 
		$this->fields_form['input'][] = [
			'type'  => 'categories',
			'label' => $this->trans('Categories', [], 'Admin.Global'),
			'name'  => 'id_categories',
			'class' => 'js-sz-block-categories',
			'tree'  => [
				'id' => 'id_root_category',
				'use_checkbox' => true,
			    'selected_categories' => anProductFields::getRelationCategories(Tools::getValue('id_field'))
			]
		];	

        $groups = anProductFieldsGroups::getGroups();
        if ($groups){
            $relationGroup = anProductFields::getRelationsByIdField($this->object->id);
     
            if (is_array($relationGroup) && count($relationGroup) != 0) {
                foreach ($relationGroup as $val) {
                    $this->fields_value['checkbox_groups_'. $val['id_type']] = true;
                }
            }
            $this->fields_form['input'][] = [
                'type' => 'checkbox',
                'label' => $this->trans('Groups', [], 'Admin.Global'),                        
                'name' => 'checkbox_groups',
                'values' => [
                    'query' => $groups,
                    'id' => 'id_group',
                    'name' => 'title_group'
                ],
                'form_group_class' => 'js-anpfields-groups'
            ]; 
        }


        $this->fields_form['input'][] = [
            'type' => 'switch',
            'name' => 'not_global',
            'label' => $this->l('Manage visibility from the product edit page'),
            'desc' => $this->l('If this option is enabled, in order for the field to be displayed for the product, you must additionally enable it on the product edit page. If the option is disabled, the field will be displayed immediately.'),
            'values' => [
                [
                    'id' => 'requirede_on',
                    'value' => 1,
                    'label' => $this->trans('Enabled', [], 'Admin.Global')
                ],
                [
                    'id' => 'required_off',
                    'value' => 0,
                    'label' => $this->trans('Disabled', [], 'Admin.Global')
                ]
            ],
        ]; 

        if (Configuration::get('an_pf_customer_groups')){
            $this->fields_form['input'][] = [
                'type' => 'html',
                'name' => 'line1',
                'html_content' => 'hr',	
            ];  

            $allGroups = Group::getGroups($this->context->language->id);
            $groups = [];
            foreach ($allGroups as $id => $group){
                $groups[$id]['id'] = $group['id_group'];
                $groups[$id]['name'] = $group['name'];
            }
            $this->fields_form['input'][] = [
                'type' => 'an_checkbox',
                'label' => $this->l('Customer Groups'),                        
                'name' => 'customer_groups',
                'values' => [
                    'query' => $groups,
                    'id' => 'id',
                    'name' => 'name'
                ]
            ]; 
        }

        if ($this->display == 'add'){
            $this->fields_value['customer_groups'] = anPfsCustomerGroups::setGroups();
        } else {
            $this->fields_value['customer_groups'] = $this->object->getCustomerGroups();            
        }

        if (Shop::isFeatureActive()) {

            $this->fields_form['input'][] = [
                'type' => 'html',
                'name' => 'line1',
                'html_content' => 'hr',	
            ];            

            $this->fields_form['input'][] = [
                'required' => true,
                'type' => 'shop',
                'label' => $this->l('Shop association'),
                'name' => 'checkBoxShopAsso',
            ];
        }

        return parent::renderForm();
    }
  
    public function processSave()
    {

        $languages = Language::getLanguages(false);
        $isUpdateImage = false;

        foreach ($languages as $lang) {
            if (isset($_FILES['file_'.$lang['id_lang']]) && isset($_FILES['file_'.$lang['id_lang']]['tmp_name'])
                    && !empty($_FILES['file_'.$lang['id_lang']]['tmp_name'])) {
                if ($error = $this->validateUpload($_FILES['file_'.$lang['id_lang']])) {
                    $this->errors[] = $error;                 
                }
            }
        }  

        if (!empty($this->errors)) {
            $this->display = 'edit';
            return false;
        }        
        
        $object = parent::processSave();
        
        if (isset($object->id) && $object->id) {


            $object->setCustomerGroups(Tools::getValue('customer_groups'));



            $delete_file = Tools::getValue('delete_file');
            if ($delete_file && is_array($delete_file)){
                
                foreach ($delete_file as $id){
                    @unlink(anProductFieldsValues::imgDir . $object->file[$id]);
                    $object->file[$id] = '';
                }
                $isUpdateImage = true;
            }

            foreach ($languages as $lang) {

                $inputFile = 'file_'.$lang['id_lang'];
                
                if (isset($_FILES[$inputFile]) && isset($_FILES[$inputFile]['tmp_name']) && !empty($_FILES[$inputFile]['tmp_name'])) {
                    
                    $ext = substr($_FILES[$inputFile]['name'], strrpos($_FILES[$inputFile]['name'], '.') + 1);
                    $ext = Tools::strtolower($ext);
                    $fileName = md5(uniqid()) . '_' . $lang['id_lang'] . '.' . $ext;

                    if (!move_uploaded_file($_FILES[$inputFile]['tmp_name'], anProductFieldsValues::imgDir . $fileName)) {
                        return $this->displayError($this->trans('An error occurred while attempting to upload the file.', [], 'Admin.Notifications.Error'));
                    }
                    
                    if (isset($object->file[$lang['id_lang']]) && $object->file[$lang['id_lang']] !=''){
                       @unlink(anProductFieldsValues::imgDir . $object->file[$lang['id_lang']]);
                    }
                    $object->file[$lang['id_lang']] = $fileName;
                   
                    $isUpdateImage = true;

 
                }
            }
     
			if ($isUpdateImage){
                $object->save();		
			}   

            $groups = anProductFieldsGroups::getGroups();
            $checkOn = [];
            foreach ($groups as $idGroup => $group) {
                if (tools::getValue('checkbox_groups_'. $group['id_group'])){
                    $checkOn[] = $group['id_group'];
                }
            }            

			switch (Tools::getValue('relation')){
				
				case 1:
					$this->updateCategoriesProducts(Tools::getValue('id_categories'), $object->id, 1);
					break;
					
				case 2:
					$this->updateCategoriesProducts(Tools::getValue('productIds'), $object->id, 2);
					break;	

                case 3:
                    $this->updateCategoriesProducts($checkOn, $object->id, 3);    
                    break;                

                case 4:
                    anPfsPA::updateProductsAttributes(Tools::getValue('products_accessories_selected'), $object->id);
                    break;                

				default:
					$this->updateCategoriesProducts([], $object->id, 0);
			}
        }

        $this->module->_clearCache('*');

        if (Tools::getIsset('submit'.$this->table.'AndStay') && !count($this->errors) ) {
            $this->redirect_after = $this->context->link->getAdminLink($this->controller_name).'&conf=4&updatean_productfields_fields&token='.$this->token.'&id_field='.$object->id;
        }
        
        return $object;
    }

    public function validateUpload($file)
    {
        $maxFileSize = 8000000;
        $filesTypesString = '';
        $filesTypesForValid = [];
        foreach (anProductFieldsValues::$typesFiles as $key => $item){
            if ($filesTypesString != ''){
                $filesTypesString .= ', ';
            }
            $filesTypesString .= '.'.$key;

            $filesTypesForValid[] = $key;
        }

        if ((int) $maxFileSize > 0 && $file['size'] > (int) $maxFileSize) {
            return Context::getContext()->getTranslator()->trans('Image is too large (%1$d kB). Maximum allowed: %2$d kB', [$file['size'] / 1024, $maxFileSize / 1024], 'Admin.Notifications.Error');
        }

        if (!ImageManager::isCorrectImageFileExt($file['name'], $filesTypesForValid) || preg_match('/\%00/', $file['name'])) {
            return Context::getContext()->getTranslator()->trans('Image format not recognized, allowed formats are: ' . $filesTypesString, [], 'Admin.Notifications.Error');
        }  

        if ($file['error']) {
            return Context::getContext()->getTranslator()->trans('Error while uploading image; please change your server\'s settings. (Error code: %s)', [$file['error']], 'Admin.Notifications.Error');
        }   
        
        return false;
    }  

	public function updateCategoriesProducts($ids = [], $id_field = 0, $type = 0)
	{
		if (!$id_field){
			return false;
		}		
		
		Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute('DELETE FROM `'._DB_PREFIX_.'an_productfields_fields_relations` WHERE `id_field`='.(int) $id_field.' ');
		
		if (!$ids || count($ids) == 0) {
			$ids[] = 0;
		}
	
		$ids = array_unique($ids);
	
		foreach ($ids as $id) {						
			$sql = 'INSERT INTO `'._DB_PREFIX_.'an_productfields_fields_relations`  (`id_field`, `id_type`, `type`) 
			VALUES ("'.(int) $id_field.'", "'.(int) $id.'", "'.(int) $type.'" )';
			Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute($sql);
		}
		
		return true;
	}    

    public function ajaxProcessUpdatePositions()
    {
        $status = false;
		$position = 0;
        $widget = (array)Tools::getValue('field');

        foreach ($widget as $key => $item){
            $ids = explode('_', $item);
			$sql = 'UPDATE `' . _DB_PREFIX_ . $this->table .'` SET position="'.(int) $position.'" WHERE '.$this->identifier.'="'.(int) $ids['2'].'" ';
			Db::getInstance(_PS_USE_SQL_SLAVE_)->execute($sql);
			$position++;
        }

        if (count($widget) > 0){
            $status = true;
        }

        return $this->setJsonResponse(array(
            'success' => $status,
            'message' => $this->l($status ? 'Blocks reordered successfully' : 'An error occurred')
        ));
    }

    protected function setJsonResponse($response)
    {
        header('Content-Type: application/json; charset=utf8');
        $this->ajaxDie(json_encode($response));
    }    

    protected function updateAssoShop($id_object)
    {
        if (!Shop::isFeatureActive()) {
            return;
        }

        $assos_data = $this->getSelectedAssoShop($this->table, $id_object);

        $exclude_ids = $assos_data;

        foreach (Db::getInstance()->executeS('SELECT id_shop FROM ' . _DB_PREFIX_ . 'shop') as $row) {
            if (!$this->context->employee->hasAuthOnShop($row['id_shop'])) {
                $exclude_ids[] = $row['id_shop'];
            }
        }

        Db::getInstance()->delete($this->table . '_shop', '`' . $this->identifier . '` = ' . (int) $id_object . ($exclude_ids ? ' AND id_shop NOT IN (' . implode(', ', $exclude_ids) . ')' : ''));

        $insert = array();

        foreach ($assos_data as $id_shop) {
            $insert[] = array(
                $this->identifier => $id_object,
                'id_shop' => (int) $id_shop,
            );
        }

        return Db::getInstance()->insert($this->table . '_shop', $insert, false, true, Db::INSERT_IGNORE);
    }

    protected function getSelectedAssoShop($table)
    {
        if (!Shop::isFeatureActive()) {
            return array();
        }

        $shops = Shop::getShops(true, null, true);

        if (count($shops) == 1 && isset($shops[0])) {
            return array($shops[0], 'shop');
        }

        $assos = array();

        if (Tools::isSubmit('checkBoxShopAsso_' . $table)) {
            foreach (Tools::getValue('checkBoxShopAsso_' . $table) as $id_shop => $value) {
                $assos[] = (int) $id_shop;
            }
        } else if (Shop::getTotalShops(false) == 1) {
            // if we do not have the checkBox multishop, we can have an admin with only one shop and being in multishop
            $assos[] = (int) Shop::getContextShopID();
        }

        return $assos;
    }    
    
}
