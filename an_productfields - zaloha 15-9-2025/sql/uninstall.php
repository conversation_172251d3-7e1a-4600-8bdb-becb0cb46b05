<?php
/**
* 2022 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2022 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

$sql = [];

$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_shop`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_lang`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_relations`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_products`';

$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_values`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_values_shop`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_values_lang`';

$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_cart`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_cart_values`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_cart_values_lang`';

$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_order`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_order_lang`';

$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_groups`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_groups_shop`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_groups_lang`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_groups_relations`';

$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_dependencies`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'an_productfields_dependencies_fields`';

return $sql;