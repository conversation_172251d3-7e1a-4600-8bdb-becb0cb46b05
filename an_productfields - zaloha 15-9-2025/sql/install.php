<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

$sql = [];

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields` (
    `id_field` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `active` tinyint(1) unsigned NOT NULL DEFAULT 1,
    `type_price` int(10) NOT NULL DEFAULT 0,
    `type_show_price` varchar(20) NOT NULL,
    `price` decimal(12,2) NOT NULL,
    `price_percent` int(10) NOT NULL DEFAULT 0,
    `free_characters` int(10) NOT NULL DEFAULT 0,
    `ignore_spaces` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `custom_tax` int(11) NOT NULL,
    `apply_specific_price` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `relation` int(10) NOT NULL,
    `not_global` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `note` varchar(255) NOT NULL,
    `type` varchar(15) NOT NULL DEFAULT 0,
    `num_min` int(10) NOT NULL DEFAULT 1,
    `num_max` int(10) NOT NULL DEFAULT 100,
    `num_multiply_price` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `field_max_file_size` int(10) NOT NULL DEFAULT 2048,
    `allow_files_format` varchar(150) NOT NULL,
    `required` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `id_default_value` int(10) NOT NULL,
    `validation` varchar(25) NOT NULL DEFAULT 0,
    `max_length` int(10) NOT NULL,
    `char_counter` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `position` int(10) NOT NULL,
    `file_display` varchar(25) NOT NULL DEFAULT "miniature_left",
    PRIMARY KEY(`id_field`)
) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';

$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'an_productfields_fields_shop` (
    `id_field` int(10) unsigned NOT NULL,
    `id_shop` int(10) unsigned NOT NULL,
    PRIMARY KEY (`id_field`, `id_shop`)
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8;';

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_lang` (
    `id_field` int(10) unsigned NOT NULL,
    `title` varchar(255) NOT NULL,
    `placeholder` varchar(255) NOT NULL,
    `descr` varchar(255) NOT NULL,
    `tooltip` varchar(255) NOT NULL,
    `file_text_link` varchar(150) NOT NULL,
    `file` varchar(50) NOT NULL,
    `id_lang` int(10) unsigned NOT NULL,
    PRIMARY KEY(`id_field`, `id_lang`)
) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_relations` (
    `id_relation` int(11) NOT NULL AUTO_INCREMENT,
    `type` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `id_field` int(11) NOT NULL,
    `id_type` int(11) NOT NULL,
    `id_product_attribute` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id_relation`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_products` (
    `id_fp` int(11) NOT NULL AUTO_INCREMENT,
    `id_field` int(11) NOT NULL,
    `id_product` int(11) NOT NULL,
    `active` tinyint(1) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`id_fp`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_customer_groups` (
    `id_group` int(10) NOT NULL,
    `id_data` int(10) NOT NULL,
    `type` int(10) NOT NULL DEFAULT 0
) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';


$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_values` (
    `id_value` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_field` int(10) unsigned NOT NULL,
    `type_price` int(10) NOT NULL DEFAULT 0,
    `type_show_price` varchar(20) NOT NULL,
    `price` decimal(12,2) NOT NULL,
    `price_percent` int(10) NOT NULL DEFAULT 0,
    `custom_tax` int(11) NOT NULL,
    `apply_specific_price` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `position` int(10) NOT NULL,
    `file_display` varchar(25) NOT NULL DEFAULT "miniature_left",
    `active` tinyint(1) unsigned NOT NULL DEFAULT 1,
    PRIMARY KEY(`id_value`)
) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';

$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'an_productfields_fields_values_shop` (
    `id_value` int(10) unsigned NOT NULL,
    `id_shop` int(10) unsigned NOT NULL,
    PRIMARY KEY (`id_value`, `id_shop`)
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8;';

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_values_lang` (
    `id_value` int(10) unsigned NOT NULL,
    `title` varchar(255) NOT NULL,
    `descr` text NOT NULL,
    `file` varchar(50) NOT NULL,
    `file_text_link` varchar(150) NOT NULL,
    `id_lang` int(10) unsigned NOT NULL,
    PRIMARY KEY(`id_value`, `id_lang`)
) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';


$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_files` (
    `id_file` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_value` int(10) unsigned NOT NULL,
    `id_field` int(10) unsigned NOT NULL,    
    `file` varchar(50) NOT NULL,
    `label` varchar(150) NOT NULL,
    `position` int(10) NOT NULL,
    `main` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `id_lang` int(10) unsigned NOT NULL,
    PRIMARY KEY(`id_file`)
) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';


$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_cart` (
    `id_pf_cart` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_cart` int(10) NOT NULL,
    `id_product` int(10) NOT NULL,
    `id_product_attribute` int(10) NOT NULL,
    `id_customization` int(10) NOT NULL,
    `hash` varchar(50) NOT NULL,
    `qty` int(10) NOT NULL DEFAULT 1,
    `date_upd` datetime NOT NULL,
    `date_add` datetime NOT NULL,
    PRIMARY KEY(`id_pf_cart`)
) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_cart_values` (
    `id_pf_cart_value` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_pf_cart` int(10) NOT NULL,
    `id_field` int(10) NOT NULL,
    `value` text NOT NULL,
    `date_add` datetime NOT NULL,
    PRIMARY KEY(`id_pf_cart_value`)
) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_order` (
    `id_pf_order` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_field` int(10) NOT NULL,
    `id_cart` int(10) NOT NULL,
    `id_order` int(10) NOT NULL,
    `id_order_detail` int(10) NOT NULL,
    `id_product` int(10) NOT NULL,
    `id_product_attribute` int(10) NOT NULL,
    `id_customization` int(10) NOT NULL,
    `value` text NOT NULL,
    `qty` int(10) NOT NULL DEFAULT 1,
    `price` decimal(12,2) NOT NULL,
    `price_wt` decimal(12,2) NOT NULL,
    `date_add` datetime NOT NULL,
    PRIMARY KEY(`id_pf_order`)
) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_order_lang` (
    `id_pf_order` int(10) unsigned NOT NULL,
    `title` varchar(255) NOT NULL,
    `title_value` text NOT NULL,
    `id_lang` int(10) unsigned NOT NULL,
    PRIMARY KEY(`id_pf_order`, `id_lang`)
) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_files` (
    `id_file` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_value` int(10) unsigned NOT NULL,
    `id_field` int(10) unsigned NOT NULL,    
    `file` varchar(50) NOT NULL,
    `external` varchar(50) NOT NULL,
    `label` varchar(150) NOT NULL,
    `position` int(10) NOT NULL,
    `main` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `id_lang` int(10) unsigned NOT NULL,
    PRIMARY KEY(`id_file`)
) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';



$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_groups` (
    `id_group` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `active` tinyint(1) unsigned NOT NULL DEFAULT 1,
    `relation` int(10) NOT NULL,
    `position` int(10) NOT NULL,
    PRIMARY KEY(`id_group`)
) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';
$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'an_productfields_groups_shop` (
    `id_group` int(10) unsigned NOT NULL,
    `id_shop` int(10) unsigned NOT NULL,
    PRIMARY KEY (`id_group`, `id_shop`)
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8;';
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_groups_lang` (
    `id_group` int(10) unsigned NOT NULL,
    `title_group` varchar(255) NOT NULL,
    `id_lang` int(10) unsigned NOT NULL,
    PRIMARY KEY(`id_group`, `id_lang`)
) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_groups_relations` (
    `id_relation` int(11) NOT NULL AUTO_INCREMENT,
    `type` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `id_group` int(11) NOT NULL,
    `id_type` int(11) NOT NULL,
  PRIMARY KEY (`id_relation`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_dependencies` (
    `id_dependence` int(11) NOT NULL AUTO_INCREMENT,
    `id_field_dependence` int(11) NOT NULL,
    `id_value` int(11) NOT NULL,
  PRIMARY KEY (`id_dependence`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_dependencies_fields` (
    `id_dpf` int(11) NOT NULL AUTO_INCREMENT,
    `id_dependence` int(11) NOT NULL,
    `id_field` int(11) NOT NULL,
  PRIMARY KEY (`id_dpf`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';


return $sql;