{*
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*}

<h2>{l s='Anvanto Product Fields' mod='an_productfields'}</h2>

{*
<div class="alert alert-info">{l s='Templates not found. ' mod='an_productextratabs'} <a href="{$myAnvantoTabsAddTemplate|escape:'htmlall':'UTF-8'}">{l s='Create template for tabs. ' mod='an_productextratabs'}</a></div>
*}

<div 
    class="js-anpfs-product-form-wrapper" 
    data-saveController="{$anpfs.saveController|escape:'htmlall':'UTF-8'}"
    data-success='{l s='Settings updated.' mod='an_productextratabs'}'
>
    <div class="">
    
        <table class="table">
        <thead>
            <tr>
                <th>{l s='Name' mod='an_productextratabs'}</th>
                <th>{l s='Active' mod='an_productextratabs'}</th>
                <th>{l s='Note' mod='an_productextratabs'}</th>
            </tr>
        </thead>
        <tbody>
            {foreach from=$anpfs.fields item=field}
            <tr>
                <td style="width: 150px;">{$field.title|escape:'htmlall':'UTF-8'}</td>
                <td style="width: 100px;">
                    <input 
                        class="js-anpfs-active"
                        type="checkbox" 
                        data-toggle="switch" 
                        name="anpfsFieldStatus[{$field.id_field|intval}]"
                        {if $field.notGlobalActive}
                        checked="checked"
                        {/if}
                />
                </td>
                <td>{$field.note|escape:'htmlall':'UTF-8'}</td>
            </tr>
            {/foreach}
        </tbody>
        </table>
        {*
        <pre>{var_dump($anpfs.fields)}</pre>

        *}

               
    </div>
</div>



