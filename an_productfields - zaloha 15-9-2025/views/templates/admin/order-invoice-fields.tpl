{*
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*}

{if count($orderFields) > 0}

<table width="100%" border="0" cellpadding="0" cellspacing="0" style="margin:0;">
{foreach from=$orderFields item=$orderFieldsProduct}
<tr>
    <td>
        <p><b>{$orderFieldsProduct.productDetail.product_name}</b> <br>
{l s='Quantity:' mod='an_productfields'} {$orderFieldsProduct.productDetail.qty|escape:'htmlall':'UTF-8'}</p>
        <table class="product" cellpadding="4" cellspacing="0">
            <tbody>
                {foreach from=$orderFieldsProduct.fields item=$orderField}
                {cycle values=["color_line_even", "color_line_odd"] assign=bgcolor_class}
                <tr class="product {$bgcolor_class}">
                    <td class="product center">{$orderField.title|escape:'htmlall':'UTF-8'}</td>
                    <td class="product center">
                        {if isset($orderField.file)}
                        <a href="{$orderField.file|escape:'htmlall':'UTF-8'}" target="_blank">{$orderField.title_value|escape:'htmlall':'UTF-8'}</a>
                        {else}
                        {$orderField.title_value|escape:'htmlall':'UTF-8'}
                        {/if}
                        </td>
                    <td class="product center">{$orderField.priceFormatted|escape:'htmlall':'UTF-8'}</td>
                </tr>
                {/foreach}
            </tbody>
        </table>
    </td>
</tr>
{/foreach}
</table>
{/if}


{*
{if count($orderFields) > 0}
<div class="card mt-2" id="an_productfields-orders">

  
    <div class="card-body">
        {foreach from=$orderFields item=$orderFieldsProduct}
        <p>{$orderFieldsProduct.productDetail.product_name}</p>
        <p>{l s='Quantity:' mod='an_productfields'} {$orderFieldsProduct.productDetail.qty|escape:'htmlall':'UTF-8'}</p>
        <table class="table">
            <tbody>
                {foreach from=$orderFieldsProduct.fields item=$orderField}
                <tr>
                    <td>{$orderField.title|escape:'htmlall':'UTF-8'}</td>
                    <td>
                        {if isset($orderField.file)}
                        <a href="{$orderField.file|escape:'htmlall':'UTF-8'}" target="_blank">{$orderField.title_value|escape:'htmlall':'UTF-8'}</a>
                        {else}
                        {$orderField.title_value|escape:'htmlall':'UTF-8'}
                        {/if}
                        </td>
                    <td>{$orderField.priceFormatted|escape:'htmlall':'UTF-8'}</td>
                </tr>
                {/foreach}
            </tbody>
        </table>
        {/foreach}
    </div>
</div>
{/if}
*}