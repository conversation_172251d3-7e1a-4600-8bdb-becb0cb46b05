{*
* 2022 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2022 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*}

{extends file="helpers/form/form.tpl"}

{block name="input"}

	{if $input.type == 'number'}
	<input type="number"
		id="{if isset($input.id)}{$input.id}{else}{$input.name}{/if}"
		name="{$input.name}"
		class="form-control {if isset($input.class)} {$input.class} {/if}"
		onkeyup="return (function (el, e) {
			if (e.keyCode == 8) return true;
			jQuery(el).val((parseInt(jQuery(el).val()) || 0));
			if (jQuery(el).val() < (parseInt(jQuery(el).attr('min')) || 0)) {
				jQuery(el).val((parseInt(jQuery(el).attr('min')) || 0));
			} else if (jQuery(el).val() > (parseInt(jQuery(el).attr('max')) || 0)) {
				jQuery(el).val((parseInt(jQuery(el).attr('max')) || 0));
			}
		})(this, event);"
		value="{$fields_value[$input.name]|escape:'html':'UTF-8'}"
		{if isset($input.size)} size="{$input.size}"{/if}
		{if isset($input.maxchar) && $input.maxchar} data-maxchar="{$input.maxchar|intval}"{/if}
		{if isset($input.maxlength) && $input.maxlength} maxlength="{$input.maxlength|intval}"{/if}
		{if isset($input.readonly) && $input.readonly} readonly="readonly"{/if}
		{if isset($input.disabled) && $input.disabled} disabled="disabled"{/if}
		{if isset($input.autocomplete) && !$input.autocomplete} autocomplete="off"{/if}
		{if isset($input.required) && $input.required} required="required" {/if}
		{if isset($input.max)} max="{$input.max|intval}"{/if}
		{if isset($input.min)} min="{$input.min|intval}"{/if}
		{if isset($input.placeholder) && $input.placeholder} placeholder="{$input.placeholder}"{/if} />
		{if isset($input.suffix)}
		<span class="input-group-addon">
			{$input.suffix}
		</span>
		{/if}
	{elseif $input.type == 'an_checkbox'}

		{foreach $input.values.query as $value}
		{assign var=id_checkbox value=$input.name|cat:'_'|cat:$value[$input.values.id]}
		<div class="checkbox">
			{strip}
				<label for="{$id_checkbox|escape:'html':'UTF-8'}">
					<input type="checkbox" name="{$input.name|escape:'html':'UTF-8'}[]" id="{$id_checkbox|escape:'html':'UTF-8'}" class="{if isset($input.class)}{$input.class|escape:'html':'UTF-8'}{/if}"{if isset($value[$input.values.id])} value="{$value[$input.values.id]|escape:'html':'UTF-8'}"{/if}{if isset($fields_value[$input.name][$value[$input.values.id]])} checked="checked"{/if} />
					{$value[$input.values.name]|escape:'html':'UTF-8'}
				</label>
			{/strip}
		</div>
		{/foreach}
	{elseif $input.type == 'file_lang'}
		{foreach from=$languages item=language}
			{if $languages|count > 1}
			<div class="translatable-field lang-{$language.id_lang}" {if $language.id_lang != $defaultFormLanguage}style="display:none"{/if}>
			{/if}
				<div class="form-group form-group-image">
					{if isset($fields_value[$input.name][$language.id_lang]) && $fields_value[$input.name][$language.id_lang] != ''}
					<div class="form-group col-lg-12">
						<div id="{$input.name}-{$language.id_lang}-images-thumbnails" class="col-lg-12">
							<a href="../modules/an_productfields/img/{$fields_value[$input.name][$language.id_lang]}" target="_blank">
								{$fields_value[$input.name][$language.id_lang]}
							</a>
							{*
							<img src="../modules/an_productfields/img/{$fields_value[$input.name][$language.id_lang]}"
							class="img-thumbnail"
							style="max-height: 150px; max-width: 150px;"
							/>
							*}
						</div>
					</div>
					<div class="form-group col-lg-12">
						<div id="{$input.name}-{$language.id_lang}-images-delete" class="col-lg-12">
							<span id="{$input.name}_{$language.id_lang}-deletebutton" type="button" name="submitDeleteAttachments"
							class="btn btn-default an-image-deletebutton an-image-deletebutton-{$language.id_lang}">
								<i class="icon-trash"></i> {l s='Delete' mod='an_productfields'}
							</span>
						</div>
					</div>
					{/if}
				</div>
				<div class="form-group">
					<div class="col-lg-10">
						<input id="{$input.name}_{$language.id_lang}" type="file" name="{$input.name}_{$language.id_lang}" class="hide" />
						<div class="dummyfile input-group">
							<span class="input-group-addon"><i class="icon-file"></i></span>
							<input id="{$input.name}_{$language.id_lang}-name" type="text" class="disabled" name="filename" readonly />
							<span class="input-group-btn">
								<button id="{$input.name}_{$language.id_lang}-selectbutton" type="button" name="submitAddAttachments" class="btn btn-default">
									<i class="icon-folder-open"></i> {l s='Choose a file' mod='an_homeproducts'}
								</button>
							</span>
						</div>
					</div>
					{if $languages|count > 1}
					<div class="col-lg-2">
						<button type="button" class="btn btn-default dropdown-toggle" tabindex="-1" data-toggle="dropdown">
							{$language.iso_code}
							<span class="caret"></span>
						</button>
						<ul class="dropdown-menu">
							{foreach from=$languages item=lang}
							<li><a href="javascript:hideOtherLanguage({$lang.id_lang});" tabindex="-1">{$lang.name}</a></li>
							{/foreach}
						</ul>
					</div>
					{/if}
				</div>
			{if $languages|count > 1}
			</div>
			{/if}
			<script>
			$(document).ready(function(){

				$('.an-image-deletebutton-{$language.id_lang}').click(function(e){
					if (confirm('Are you sure?') ) {
						var formGroupImage = $(this).closest('.form-group-image');

						$(formGroupImage).append('<input type="hidden" name="delete_{$input.name}[{$language.id_lang}]" value="{$language.id_lang}" /> ');

						$(formGroupImage).find('.form-group').fadeOut(function () {
							$(this).remove();
						});
                	}
				});

				$('#{$input.name}_{$language.id_lang}-selectbutton').click(function(e){
					$('#{$input.name}_{$language.id_lang}').trigger('click');
				});
				$('#{$input.name}_{$language.id_lang}').change(function(e){
					var val = $(this).val();
					var file = val.split(/[\\/]/);
					$('#{$input.name}_{$language.id_lang}-name').val(file[file.length-1]);
				});
			});
			</script>
		{/foreach}
		{if isset($input.desc) && !empty($input.desc)}
			<p class="help-block">
				{$input.desc}
			</p>
		{/if}


	{elseif $input.type == 'multi_files_lang'}

		{foreach from=$languages item=language}
			{if $languages|count > 1}
			<div class="translatable-field lang-{$language.id_lang}" {if $language.id_lang != $defaultFormLanguage}style="display:none"{/if}>
			{/if}

				<div class="form-group-image">
				{if isset($fields_value[$input.name][$language.id_lang])}
					{foreach from=$fields_value[$input.name][$language.id_lang] item=$file}
					<div class="form-group row" data-fileId="{$file.id_file|escape:'htmlall':'UTF-8'}">
						<div class="col-sm-1 an-image-radio">
							<div class="radio">
								<label><input
									type="radio"
									name="main_{$input.name}[{$language.id_lang|escape:'htmlall':'UTF-8'}]"
									id="main_{$input.name}_{$file.id_file|escape:'htmlall':'UTF-8'}"
									value="{$file.id_file|escape:'htmlall':'UTF-8'}" {if $file.main} checked="checked"{/if}></label>
							</div>
						</div>
						<div class="col-sm-1 an-image-miniature">
                            <div class="an-image-miniature-img">
                                <a href="{$file.fileUrl|escape:'htmlall':'UTF-8'}" target="_blank">
                                    {if $file.isFileImg}
                                    <img src="{$file.fileUrl|escape:'htmlall':'UTF-8'}" />
									{else}
									<img src="{$file.fileUrlIcon|escape:'htmlall':'UTF-8'}" />
                                    {/if}
                                </a>
                            </div>
                            <span><a href="{if $file.format == 'youtube'}{$file.file|escape:'htmlall':'UTF-8'}{else}{$file.fileUrl|escape:'htmlall':'UTF-8'}{/if}" target="_blank">{$file.format|escape:'htmlall':'UTF-8'}</a></span>
						</div>
						<div class="col-sm-8">
							{*
							<input type="text" class="an-imagelink-input" value="" placeholder="{l s='Text for file link*' mod='an_productfields'}" />
							*}
							<a href="{$file.fileUrl|escape:'htmlall':'UTF-8'}"  target="_blank">
								{$file.file|escape:'htmlall':'UTF-8'}
							</a>
							
						</div>
						<div class="col-sm-3 an-image-button-wrap">
							<span id="{$input.name}_{$file.id_file|escape:'htmlall':'UTF-8'}-deletebutton"
							 class="btn btn-default an-image-deletebutton">
								<i class="icon-trash"></i> {l s='Delete' mod='an_productfields'}
							</span>
						</div>
					</div>
					{/foreach}
				{/if}
				    <div class="form-group-image-hint">* {l s='Select the main image in the radio button' mod='an_productfields'}</div>
				</div>
				<div class="form-group">
					<div class="col-lg-10">
						<input
							id="{$input.name}_{$language.id_lang}"
							type="file" class="hide" multiple
							name="{$input.name}_{$language.id_lang}[]"
						/>
						<div class="dummyfile input-group">
							<span class="input-group-addon"><i class="icon-file"></i></span>
							<input id="{$input.name}_{$language.id_lang}-name" type="text" class="disabled" name="filename" readonly />
							<span class="input-group-btn">
								<button id="{$input.name}_{$language.id_lang}-selectbutton" type="button" name="submitAddAttachments" class="btn btn-default">
									<i class="icon-folder-open"></i> {l s='Choose a file' mod='an_homeproducts'}
								</button>
							</span>
						</div>
					</div>
					{if $languages|count > 1}
					<div class="col-lg-2">
						<button type="button" class="btn btn-default dropdown-toggle" tabindex="-1" data-toggle="dropdown">
							{$language.iso_code}
							<span class="caret"></span>
						</button>
						<ul class="dropdown-menu">
							{foreach from=$languages item=lang}
							<li><a href="javascript:hideOtherLanguage({$lang.id_lang});" tabindex="-1">{$lang.name}</a></li>
							{/foreach}
						</ul>
					</div>
					{/if}
				</div>
			{if $languages|count > 1}
			</div>
			{/if}
			<script>
				$(document).ready(function(){
					$('#{$input.name}_{$language.id_lang}-selectbutton').click(function(e){
						$('#{$input.name}_{$language.id_lang}').trigger('click');
					});
					$('#{$input.name}_{$language.id_lang}').change(function(e){
						var val = $(this).val();
						var file = val.split(/[\\/]/);
						$('#{$input.name}_{$language.id_lang}-name').val(file[file.length-1]);
					});
				});
			</script>
		{/foreach}
		<script>
			$(document).ready(function(){
				$('.an-image-deletebutton').click(function(e){
					if (confirm('Are you sure?') ) {
						var groupFile = $(this).closest('.form-group');
						var fileId = $(groupFile).attr('data-fileId');
						var formGroupImage = $(this).closest('.form-group-image');
						$(formGroupImage).append('<input type="hidden" name="delete_{$input.name}[]" value="'+fileId+'" /> ');
						$(groupFile).fadeOut(function () {
							$(this).remove();
						});
                	}
				});
			});
		</script>
		{if isset($input.desc) && !empty($input.desc)}
			<p class="help-block">
				{$input.desc}
			</p>
		{/if}

	{elseif $input.type == 'html'}
		{if isset($input.html_content)}
			{if $input.html_content == 'hr'}
			<hr />
			{else}
			{$input.html_content}
			{/if}
		{else}
			{$input.name|escape:'htmlall':'UTF-8'}
		{/if}
	{else}
		{$smarty.block.parent}
    {/if}
{/block}