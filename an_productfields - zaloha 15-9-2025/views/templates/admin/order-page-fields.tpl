{*
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*}

{if count($orderFields) > 0}
<div class="card mt-2" id="an_productfields-orders">
    <div class="card-header">
      <h3 class="card-header-title">
        {l s='Anvanto Product Fields' mod='an_productfields'}
      </h3>
    </div>
  
    <div class="card-body">
        {foreach from=$orderFields item=$orderFieldsProduct}
        <p>{$orderFieldsProduct.productDetail.product_name}</p>
        <p>{l s='Quantity:' mod='an_productfields'} {$orderFieldsProduct.productDetail.qty|escape:'htmlall':'UTF-8'}</p>
        <table class="table">
            <tbody>
                {foreach from=$orderFieldsProduct.fields item=$orderField}
                <tr>
                    <td>{$orderField.title|escape:'htmlall':'UTF-8'}</td>
                    <td>
                        {if isset($orderField.file)}
                        <a href="{$orderField.file|escape:'htmlall':'UTF-8'}" target="_blank">{$orderField.title_value|escape:'htmlall':'UTF-8'}</a>
                        {else}
                        {$orderField.title_value|escape:'htmlall':'UTF-8'}
                        {/if}
                        </td>
                    <td>{$orderField.priceFormatted|escape:'htmlall':'UTF-8'}</td>
                </tr>
                {/foreach}
            </tbody>
        </table>
        {/foreach}
    </div>
</div>
{/if}