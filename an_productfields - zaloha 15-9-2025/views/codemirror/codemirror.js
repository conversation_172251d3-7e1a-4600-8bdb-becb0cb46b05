/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

$(document).ready(function () {

    var myCodeMirror = CodeMirror.fromTextArea(document.getElementById('an_pf_code_js_custom'), {
        lineNumbers: true,
        mode: 'javascript',
        highlightNonStandardPropertyKeywords: true
    });

    var myCodeMirror = CodeMirror.fromTextArea(document.getElementById('an_pf_code_css_custom'), {
        lineNumbers: true,
        mode: 'css',
        highlightNonStandardPropertyKeywords: true
    });
});