// CodeMirror, copyright (c) by <PERSON><PERSON> and others
// Distributed under an MIT license: https://codemirror.net/5/LICENSE
!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(nt){"use strict";nt.defineMode("javascript",function(e,l){var t,r,n,a,f=e.indentUnit,d=l.statementIndent,i=l.jsonld,o=l.json||i,c=!1!==l.trackScope,u=l.typescript,p=l.wordCharacters||/[\w$\xa1-\uffff]/,s=(t=m("keyword a"),r=m("keyword b"),n=m("keyword c"),a=m("keyword d"),e=m("operator"),{if:m("if"),while:t,with:t,else:r,do:r,try:r,finally:r,return:a,break:a,continue:a,new:m("new"),delete:n,void:n,throw:n,debugger:m("debugger"),var:m("var"),const:m("var"),let:m("var"),function:m("function"),catch:m("catch"),for:m("for"),switch:m("switch"),case:m("case"),default:m("default"),in:e,typeof:e,instanceof:e,true:e={type:"atom",style:"atom"},false:e,null:e,undefined:e,NaN:e,Infinity:e,this:m("this"),class:m("class"),super:m("atom"),yield:n,export:m("export"),import:m("import"),extends:n,await:n});function m(e){return{type:e,style:"keyword"}}var k,v,y=/[+\-*&%=<>!?|~^@]/,w=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function b(e,t,r){return k=e,v=r,t}function x(e,t){var a,r=e.next();if('"'==r||"'"==r)return t.tokenize=(a=r,function(e,t){var r,n=!1;if(i&&"@"==e.peek()&&e.match(w))return t.tokenize=x,b("jsonld-keyword","meta");for(;null!=(r=e.next())&&(r!=a||n);)n=!n&&"\\"==r;return n||(t.tokenize=x),b("string","string")}),t.tokenize(e,t);if("."==r&&e.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return b("number","number");if("."==r&&e.match(".."))return b("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(r))return b(r);if("="==r&&e.eat(">"))return b("=>","operator");if("0"==r&&e.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return b("number","number");if(/\d/.test(r))return e.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),b("number","number");if("/"==r)return e.eat("*")?(t.tokenize=h)(e,t):e.eat("/")?(e.skipToEnd(),b("comment","comment")):rt(e,t,1)?(function(e){for(var t,r=!1,n=!1;null!=(t=e.next());){if(!r){if("/"==t&&!n)return;"["==t?n=!0:n&&"]"==t&&(n=!1)}r=!r&&"\\"==t}}(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),b("regexp","string-2")):(e.eat("="),b("operator","operator",e.current()));if("`"==r)return(t.tokenize=g)(e,t);if("#"==r&&"!"==e.peek())return e.skipToEnd(),b("meta","meta");if("#"==r&&e.eatWhile(p))return b("variable","property");if("<"==r&&e.match("!--")||"-"==r&&e.match("->")&&!/\S/.test(e.string.slice(0,e.start)))return e.skipToEnd(),b("comment","comment");if(y.test(r))return">"==r&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=r&&"="!=r||e.eat("="):/[<>*+\-|&?]/.test(r)&&(e.eat(r),">"==r&&e.eat(r))),"?"==r&&e.eat(".")?b("."):b("operator","operator",e.current());if(p.test(r)){e.eatWhile(p);r=e.current();if("."!=t.lastType){if(s.propertyIsEnumerable(r)){t=s[r];return b(t.type,t.style,r)}if("async"==r&&e.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return b("async","keyword",r)}return b("variable","variable",r)}}function h(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=x;break}n="*"==r}return b("comment","comment")}function g(e,t){for(var r,n=!1;null!=(r=e.next());){if(!n&&("`"==r||"$"==r&&e.eat("{"))){t.tokenize=x;break}n=!n&&"\\"==r}return b("quasi","string-2",e.current())}var j="([{}])";function M(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var r,n=e.string.indexOf("=>",e.start);if(!(n<0)){!u||(r=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,n)))&&(n=r.index);for(var a=0,i=!1,o=n-1;0<=o;--o){var c=e.string.charAt(o),s=j.indexOf(c);if(0<=s&&s<3){if(!a){++o;break}if(0==--a){"("==c&&(i=!0);break}}else if(3<=s&&s<6)++a;else if(p.test(c))i=!0;else if(/["'\/`]/.test(c))for(;;--o){if(0==o)return;if(e.string.charAt(o-1)==c&&"\\"!=e.string.charAt(o-2)){o--;break}}else if(i&&!a){++o;break}}i&&!a&&(t.fatArrowAt=o)}}var A={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function V(e,t,r,n,a,i){this.indented=e,this.column=t,this.type=r,this.prev=a,this.info=i,null!=n&&(this.align=n)}function E(e,t,r,n,a){var i=e.cc;for(z.state=e,z.stream=a,z.marked=null,z.cc=i,z.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;)if((i.length?i.pop():o?G:H)(r,n)){for(;i.length&&i[i.length-1].lex;)i.pop()();return z.marked?z.marked:"variable"==r&&function(e,t){if(c){for(var r=e.localVars;r;r=r.next)if(r.name==t)return 1;for(var n=e.context;n;n=n.prev)for(r=n.vars;r;r=r.next)if(r.name==t)return 1}}(e,n)?"variable-2":t}}var z={state:null,column:null,marked:null,cc:null};function I(){for(var e=arguments.length-1;0<=e;e--)z.cc.push(arguments[e])}function T(){return I.apply(null,arguments),!0}function $(e,t){for(var r=t;r;r=r.next)if(r.name==e)return 1}function q(e){var t=z.state;if(z.marked="def",c){if(t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var r=function e(t,r){{if(r){if(r.block){var n=e(t,r.prev);return n?n==r.prev?r:new S(n,r.vars,!0):null}return $(t,r.vars)?r:new S(r.prev,new _(t,r.vars),!1)}return null}}(e,t.context);if(null!=r)return void(t.context=r)}else if(!$(e,t.localVars))return void(t.localVars=new _(e,t.localVars));l.globalVars&&!$(e,t.globalVars)&&(t.globalVars=new _(e,t.globalVars))}}function C(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function S(e,t,r){this.prev=e,this.vars=t,this.block=r}function _(e,t){this.name=e,this.next=t}var O=new _("this",new _("arguments",null));function P(){z.state.context=new S(z.state.context,z.state.localVars,!1),z.state.localVars=O}function N(){z.state.context=new S(z.state.context,z.state.localVars,!0),z.state.localVars=null}function U(){z.state.localVars=z.state.context.vars,z.state.context=z.state.context.prev}function W(n,a){function e(){var e=z.state,t=e.indented;if("stat"==e.lexical.type)t=e.lexical.indented;else for(var r=e.lexical;r&&")"==r.type&&r.align;r=r.prev)t=r.indented;e.lexical=new V(t,z.stream.column(),n,null,e.lexical,a)}return e.lex=!0,e}function B(){var e=z.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function F(r){return function e(t){return t==r?T():";"==r||"}"==t||")"==t||"]"==t?I():T(e)}}function H(e,t){return"var"==e?T(W("vardef",t),Ee,F(";"),B):"keyword a"==e?T(W("form"),K,H,B):"keyword b"==e?T(W("form"),H,B):"keyword d"==e?z.stream.match(/^\s*$/,!1)?T():T(W("stat"),Q,F(";"),B):"debugger"==e?T(F(";")):"{"==e?T(W("}"),N,fe,B,U):";"==e?T():"if"==e?("else"==z.state.lexical.info&&z.state.cc[z.state.cc.length-1]==B&&z.state.cc.pop()(),T(W("form"),K,H,B,Ce)):"function"==e?T(Pe):"for"==e?T(W("form"),N,Se,H,U,B):"class"==e||u&&"interface"==t?(z.marked="keyword",T(W("form","class"==e?e:t),Fe,B)):"variable"==e?u&&"declare"==t?(z.marked="keyword",T(H)):u&&("module"==t||"enum"==t||"type"==t)&&z.stream.match(/^\s*\w/,!1)?(z.marked="keyword","enum"==t?T(et):"type"==t?T(Ue,F("operator"),ve,F(";")):T(W("form"),ze,F("{"),W("}"),fe,B,B)):u&&"namespace"==t?(z.marked="keyword",T(W("form"),G,H,B)):u&&"abstract"==t?(z.marked="keyword",T(H)):T(W("stat"),ae):"switch"==e?T(W("form"),K,F("{"),W("}","switch"),N,fe,B,B,U):"case"==e?T(G,F(":")):"default"==e?T(F(":")):"catch"==e?T(W("form"),P,D,H,B,U):"export"==e?T(W("stat"),Je,B):"import"==e?T(W("stat"),Le,B):"async"==e?T(H):"@"==t?T(G,H):I(W("stat"),G,F(";"),B)}function D(e){if("("==e)return T(We,F(")"))}function G(e,t){return L(e,t,!1)}function J(e,t){return L(e,t,!0)}function K(e){return"("!=e?I():T(W(")"),Q,F(")"),B)}function L(e,t,r){if(z.state.fatArrowAt==z.stream.start){var n=r?te:ee;if("("==e)return T(P,W(")"),ue(We,")"),B,F("=>"),n,U);if("variable"==e)return I(P,ze,F("=>"),n,U)}var a,n=r?X:R;return A.hasOwnProperty(e)?T(n):"function"==e?T(Pe,n):"class"==e||u&&"interface"==t?(z.marked="keyword",T(W("form"),Be,B)):"keyword c"==e||"async"==e?T(r?J:G):"("==e?T(W(")"),Q,F(")"),B,n):"operator"==e||"spread"==e?T(r?J:G):"["==e?T(W("]"),Ze,B,n):"{"==e?le(oe,"}",null,n):"quasi"==e?I(Y,n):"new"==e?T((a=r,function(e){return"."==e?T(a?ne:re):"variable"==e&&u?T(Me,a?X:R):I(a?J:G)})):T()}function Q(e){return e.match(/[;\}\)\],]/)?I():I(G)}function R(e,t){return","==e?T(Q):X(e,t,!1)}function X(e,t,r){var n=0==r?R:X,a=0==r?G:J;return"=>"==e?T(P,r?te:ee,U):"operator"==e?/\+\+|--/.test(t)||u&&"!"==t?T(n):u&&"<"==t&&z.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?T(W(">"),ue(ve,">"),B,n):"?"==t?T(G,F(":"),a):T(a):"quasi"==e?I(Y,n):";"!=e?"("==e?le(J,")","call",n):"."==e?T(ie,n):"["==e?T(W("]"),Q,F("]"),B,n):u&&"as"==t?(z.marked="keyword",T(ve,n)):"regexp"==e?(z.state.lastType=z.marked="operator",z.stream.backUp(z.stream.pos-z.stream.start-1),T(a)):void 0:void 0}function Y(e,t){return"quasi"!=e?I():"${"!=t.slice(t.length-2)?T(Y):T(Q,Z)}function Z(e){if("}"==e)return z.marked="string-2",z.state.tokenize=g,T(Y)}function ee(e){return M(z.stream,z.state),I("{"==e?H:G)}function te(e){return M(z.stream,z.state),I("{"==e?H:J)}function re(e,t){if("target"==t)return z.marked="keyword",T(R)}function ne(e,t){if("target"==t)return z.marked="keyword",T(X)}function ae(e){return":"==e?T(B,H):I(R,F(";"),B)}function ie(e){if("variable"==e)return z.marked="property",T()}function oe(e,t){return"async"==e?(z.marked="property",T(oe)):"variable"!=e&&"keyword"!=z.style?"number"==e||"string"==e?(z.marked=i?"property":z.style+" property",T(se)):"jsonld-keyword"==e?T(se):u&&C(t)?(z.marked="keyword",T(oe)):"["==e?T(G,de,F("]"),se):"spread"==e?T(J,se):"*"==t?(z.marked="keyword",T(oe)):":"==e?I(se):void 0:(z.marked="property","get"==t||"set"==t?T(ce):(u&&z.state.fatArrowAt==z.stream.start&&(r=z.stream.match(/^\s*:\s*/,!1))&&(z.state.fatArrowAt=z.stream.pos+r[0].length),T(se)));var r}function ce(e){return"variable"!=e?I(se):(z.marked="property",T(Pe))}function se(e){return":"==e?T(J):"("==e?I(Pe):void 0}function ue(n,a,i){function o(e,t){if(i?-1<i.indexOf(e):","==e){var r=z.state.lexical;return"call"==r.info&&(r.pos=(r.pos||0)+1),T(function(e,t){return e==a||t==a?I():I(n)},o)}return e==a||t==a?T():i&&-1<i.indexOf(";")?I(n):T(F(a))}return function(e,t){return e==a||t==a?T():I(n,o)}}function le(e,t,r){for(var n=3;n<arguments.length;n++)z.cc.push(arguments[n]);return T(W(t,r),ue(e,t),B)}function fe(e){return"}"==e?T():I(H,fe)}function de(e,t){if(u)return":"==e?T(ve):"?"==t?T(de):void 0}function pe(e,t){if(u&&(":"==e||"in"==t))return T(ve)}function me(e){if(u&&":"==e)return z.stream.match(/^\s*\w+\s+is\b/,!1)?T(G,ke,ve):T(ve)}function ke(e,t){if("is"==t)return z.marked="keyword",T()}function ve(e,t){return"keyof"==t||"typeof"==t||"infer"==t||"readonly"==t?(z.marked="keyword",T("typeof"==t?J:ve)):"variable"==e||"void"==t?(z.marked="type",T(je)):"|"==t||"&"==t?T(ve):"string"==e||"number"==e||"atom"==e?T(je):"["==e?T(W("]"),ue(ve,"]",","),B,je):"{"==e?T(W("}"),we,B,je):"("==e?T(ue(ge,")"),ye,je):"<"==e?T(ue(ve,">"),ve):"quasi"==e?I(xe,je):void 0}function ye(e){if("=>"==e)return T(ve)}function we(e){return e.match(/[\}\)\]]/)?T():","==e||";"==e?T(we):I(be,we)}function be(e,t){return"variable"==e||"keyword"==z.style?(z.marked="property",T(be)):"?"==t||"number"==e||"string"==e?T(be):":"==e?T(ve):"["==e?T(F("variable"),pe,F("]"),be):"("==e?I(Ne,be):e.match(/[;\}\)\],]/)?void 0:T()}function xe(e,t){return"quasi"!=e?I():"${"!=t.slice(t.length-2)?T(xe):T(ve,he)}function he(e){if("}"==e)return z.marked="string-2",z.state.tokenize=g,T(xe)}function ge(e,t){return"variable"==e&&z.stream.match(/^\s*[?:]/,!1)||"?"==t?T(ge):":"==e?T(ve):"spread"==e?T(ge):I(ve)}function je(e,t){return"<"==t?T(W(">"),ue(ve,">"),B,je):"|"==t||"."==e||"&"==t?T(ve):"["==e?T(ve,F("]"),je):"extends"==t||"implements"==t?(z.marked="keyword",T(ve)):"?"==t?T(ve,F(":"),ve):void 0}function Me(e,t){if("<"==t)return T(W(">"),ue(ve,">"),B,je)}function Ae(){return I(ve,Ve)}function Ve(e,t){if("="==t)return T(ve)}function Ee(e,t){return"enum"==t?(z.marked="keyword",T(et)):I(ze,de,$e,qe)}function ze(e,t){return u&&C(t)?(z.marked="keyword",T(ze)):"variable"==e?(q(t),T()):"spread"==e?T(ze):"["==e?le(Te,"]"):"{"==e?le(Ie,"}"):void 0}function Ie(e,t){return"variable"!=e||z.stream.match(/^\s*:/,!1)?("variable"==e&&(z.marked="property"),"spread"==e?T(ze):"}"==e?I():"["==e?T(G,F("]"),F(":"),Ie):T(F(":"),ze,$e)):(q(t),T($e))}function Te(){return I(ze,$e)}function $e(e,t){if("="==t)return T(J)}function qe(e){if(","==e)return T(Ee)}function Ce(e,t){if("keyword b"==e&&"else"==t)return T(W("form","else"),H,B)}function Se(e,t){return"await"==t?T(Se):"("==e?T(W(")"),_e,B):void 0}function _e(e){return"var"==e?T(Ee,Oe):("variable"==e?T:I)(Oe)}function Oe(e,t){return")"==e?T():";"==e?T(Oe):"in"==t||"of"==t?(z.marked="keyword",T(G,Oe)):I(G,Oe)}function Pe(e,t){return"*"==t?(z.marked="keyword",T(Pe)):"variable"==e?(q(t),T(Pe)):"("==e?T(P,W(")"),ue(We,")"),B,me,H,U):u&&"<"==t?T(W(">"),ue(Ae,">"),B,Pe):void 0}function Ne(e,t){return"*"==t?(z.marked="keyword",T(Ne)):"variable"==e?(q(t),T(Ne)):"("==e?T(P,W(")"),ue(We,")"),B,me,U):u&&"<"==t?T(W(">"),ue(Ae,">"),B,Ne):void 0}function Ue(e,t){return"keyword"==e||"variable"==e?(z.marked="type",T(Ue)):"<"==t?T(W(">"),ue(Ae,">"),B):void 0}function We(e,t){return"@"==t&&T(G,We),"spread"==e?T(We):u&&C(t)?(z.marked="keyword",T(We)):u&&"this"==e?T(de,$e):I(ze,de,$e)}function Be(e,t){return("variable"==e?Fe:He)(e,t)}function Fe(e,t){if("variable"==e)return q(t),T(He)}function He(e,t){return"<"==t?T(W(">"),ue(Ae,">"),B,He):"extends"==t||"implements"==t||u&&","==e?("implements"==t&&(z.marked="keyword"),T(u?ve:G,He)):"{"==e?T(W("}"),De,B):void 0}function De(e,t){return"async"==e||"variable"==e&&("static"==t||"get"==t||"set"==t||u&&C(t))&&z.stream.match(/^\s+#?[\w$\xa1-\uffff]/,!1)?(z.marked="keyword",T(De)):"variable"==e||"keyword"==z.style?(z.marked="property",T(Ge,De)):"number"==e||"string"==e?T(Ge,De):"["==e?T(G,de,F("]"),Ge,De):"*"==t?(z.marked="keyword",T(De)):u&&"("==e?I(Ne,De):";"==e||","==e?T(De):"}"==e?T():"@"==t?T(G,De):void 0}function Ge(e,t){if("!"==t)return T(Ge);if("?"==t)return T(Ge);if(":"==e)return T(ve,$e);if("="==t)return T(J);t=z.state.lexical.prev;return I(t&&"interface"==t.info?Ne:Pe)}function Je(e,t){return"*"==t?(z.marked="keyword",T(Ye,F(";"))):"default"==t?(z.marked="keyword",T(G,F(";"))):"{"==e?T(ue(Ke,"}"),Ye,F(";")):I(H)}function Ke(e,t){return"as"==t?(z.marked="keyword",T(F("variable"))):"variable"==e?I(J,Ke):void 0}function Le(e){return"string"==e?T():"("==e?I(G):"."==e?I(R):I(Qe,Re,Ye)}function Qe(e,t){return"{"==e?le(Qe,"}"):("variable"==e&&q(t),"*"==t&&(z.marked="keyword"),T(Xe))}function Re(e){if(","==e)return T(Qe,Re)}function Xe(e,t){if("as"==t)return z.marked="keyword",T(Qe)}function Ye(e,t){if("from"==t)return z.marked="keyword",T(G)}function Ze(e){return"]"==e?T():I(ue(J,"]"))}function et(){return I(W("form"),ze,F("{"),W("}"),ue(tt,"}"),B,B)}function tt(){return I(ze,$e)}function rt(e,t,r){return t.tokenize==x&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(r||0)))}return P.lex=N.lex=!0,B.lex=U.lex=!0,{startState:function(e){e={tokenize:x,lastType:"sof",cc:[],lexical:new V((e||0)-f,0,"block",!1),localVars:l.localVars,context:l.localVars&&new S(null,null,!1),indented:e||0};return l.globalVars&&"object"==typeof l.globalVars&&(e.globalVars=l.globalVars),e},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),M(e,t)),t.tokenize!=h&&e.eatSpace())return null;var r=t.tokenize(e,t);return"comment"==k?r:(t.lastType="operator"!=k||"++"!=v&&"--"!=v?k:"incdec",E(t,r,k,v,e))},indent:function(e,t){if(e.tokenize==h||e.tokenize==g)return nt.Pass;if(e.tokenize!=x)return 0;var r,n=t&&t.charAt(0),a=e.lexical;if(!/^\s*else\b/.test(t))for(var i=e.cc.length-1;0<=i;--i){var o=e.cc[i];if(o==B)a=a.prev;else if(o!=Ce&&o!=U)break}for(;("stat"==a.type||"form"==a.type)&&("}"==n||(r=e.cc[e.cc.length-1])&&(r==R||r==X)&&!/^[,\.=+\-*:?[\(]/.test(t));)a=a.prev;d&&")"==a.type&&"stat"==a.prev.type&&(a=a.prev);var c,s=a.type,u=n==s;return"vardef"==s?a.indented+("operator"==e.lastType||","==e.lastType?a.info.length+1:0):"form"==s&&"{"==n?a.indented:"form"==s?a.indented+f:"stat"==s?a.indented+(c=t,"operator"==(s=e).lastType||","==s.lastType||y.test(c.charAt(0))||/[,.]/.test(c.charAt(0))?d||f:0):"switch"!=a.info||u||0==l.doubleIndentSwitch?a.align?a.column+(u?0:1):a.indented+(u?0:f):a.indented+(/^(?:case|default)\b/.test(t)?f:2*f)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:o?null:"/*",blockCommentEnd:o?null:"*/",blockCommentContinue:o?null:" * ",lineComment:o?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:o?"json":"javascript",jsonldMode:i,jsonMode:o,expressionAllowed:rt,skipExpression:function(e){E(e,"atom","atom","true",new nt.StringStream("",2,null))}}}),nt.registerHelper("wordChars","javascript",/[\w$]/),nt.defineMIME("text/javascript","javascript"),nt.defineMIME("text/ecmascript","javascript"),nt.defineMIME("application/javascript","javascript"),nt.defineMIME("application/x-javascript","javascript"),nt.defineMIME("application/ecmascript","javascript"),nt.defineMIME("application/json",{name:"javascript",json:!0}),nt.defineMIME("application/x-json",{name:"javascript",json:!0}),nt.defineMIME("application/manifest+json",{name:"javascript",json:!0}),nt.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),nt.defineMIME("text/typescript",{name:"javascript",typescript:!0}),nt.defineMIME("application/typescript",{name:"javascript",typescript:!0})});