/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/
 
(function ($, window, undefined) {
    'use strict';

	$(document).ready(function() {

        $('.js-anpfs-active').on('click', function(){
			console.log('test');
			console.log($(this).find('input').attr('name'));

			getData(
				$('.js-anpfs-product-form-wrapper').attr('data-savecontroller'), 
				$(this).find('input').attr('name') + '=' + Number($(this).hasClass('-checked')),
				function(data){
					showSuccessMessage($('.js-anpfs-product-form-wrapper').attr('data-success'));
			});
		});		

		
		
	});
	
	function getData(controller, dataUrl, callback){
		$.ajax({
			type: "POST",
			url: controller,
			data: dataUrl,
			dataType: 'json',
		}).done(function(data){
			callback(data);
		}).always(function() {

		});
	}

})(jQuery, window);	
