/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/
 
(function ($, window, undefined) {
    'use strict';

    function getData() {
		if ($('.js-an-pf-fields-wrap').data('dependencies')) {
			return $('.js-an-pf-fields-wrap').data('dependencies');
		}
		return 0;
	}

	function hideFields(data) {
		for (var fieldId in data) {
			$('[data-apfs-field-id="'+data[fieldId]+'"]').addClass('an-pf-hide');
		}
	}

	function showFields(data, id, value) {
		for (var dataId in data) {
			if (dataId == id) {
				for (var dataValue in data[dataId]) {
					if (dataValue == value) {
						for (var fieldId in data[dataId][dataValue]) {
							$('[data-apfs-field-id="'+data[dataId][dataValue][fieldId]+'"]').removeClass('an-pf-hide');
							showFields(data, data[dataId][dataValue][fieldId], $('[data-apfs-field-id="'+data[dataId][dataValue][fieldId]+'"] :checked').val());
						}
					}
				}
			}
		}
		showDescription();
	}

	function showDescription() {
		$('.js-anpf-field select').each(function () {
			var item = $(this),
				text = item.find(':selected').data('descr');

			if (text) {
				if (!item.parents('.js-anpf-field').find('.apfs-field-description').length) {
					item.parents('.js-anpf-field').append('<div class="apfs-field-description">'+text+'</div>')
				}
			} else {
				item.parents('.js-anpf-field').find('.apfs-field-description').remove();
			}
		});
	}

	function init(data) {
		if (data.hidden) {
			hideFields(data.hidden);
		}
		if (data.dependencies) {
			$('.js-anpf-field:not(.an-pf-hide) :checked').each(function () {
				var item = $(this);
				showFields(data.dependencies, item.parents('[data-apfs-field-id]').attr('data-apfs-field-id'), item.val(), item.data('descr'));
				showDescription();
				setTimeout(function () {
					calcPrice();
				}, 100);
			});
		}
	}

	$(document).ready(function() {
		var data = getData();

		if (data) {
			init(data);
			$(document).on('change', '.js-an_productfields-price:not([type="text"])', function () {
				init(data);
			});
		}

		$(document).ajaxComplete(function(event, xhr, settings) {
			if ((xhr.responseJSON != undefined) && xhr.responseJSON.hasOwnProperty('quantity') && (xhr.responseJSON.quantity != undefined)) {
				if (!xhr.responseJSON.hasError) {
					setTimeout(function () {
						init(data);
					}, 500);
				}
			}
		});
	});
	
})(jQuery, window);	
