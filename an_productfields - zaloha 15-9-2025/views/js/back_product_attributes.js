/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

/**
 * 2021 Anvanto
 *
 * NOTICE OF LICENSE
 *
 * This file is not open source! Each license that you purchased is only available for 1 wesite only.
 * If you want to use this file on more websites (or projects), you need to purchase additional licenses.
 * You are not allowed to redistribute, resell, lease, license, sub-license or offer our resources to any third party.
 *
 *  <AUTHOR> <<EMAIL>>
 *  @copyright  2021 Anvanto
 *  @license    Valid for 1 website (or project) for each purchase of license
 *  International Registered Trademark & Property of Anvanto
 */
 
(function ($, window, undefined) {
    'use strict';


    function qtyRules(){
    	let min = $('#select_min'),
			max = $('#select_max');

    	if ($('#required_on:checked').length) {
    		if (min.val() < 1) {
				min.val(1);
			}
			if (max.val() < 1) {
				max.val(1);
				max.attr('min','1');
			}
			min.attr('min','1');
			min.attr('max', max.val());
		} else {
			//min.val(0);
			min.attr('min','0');
			//min.attr('max', '0');
		}
	}
	function qtyRulesChange(){
		let min = $('#select_min'),
			max = $('#select_max');

		if (+max.val() < +min.val()) {
			min.val(max.val());
		}
		min.attr('max', max.val());
	}

	function textRules(){
		var  type = $('.an-dt-type :selected').val();
		if ( (type == 'text') && $('#show_qty_on:checked').length ){
			$('#min_for_text, #max_for_text').parents('.form-group').show();
		} else {
			$('#min_for_text, #max_for_text').parents('.form-group').hide();
		}
	}
	function textRulesChange(){
		let min = $('#min_for_text'),
			max = $('#max_for_text');

		if ($('#required_on:checked').length) {
			if (min.val() < 1) {
				min.val(1);
			}
			if (max.val() < 1) {
				max.val(1);
				max.attr('min','1');
			}
			min.attr('min','1');
			min.attr('max', max.val());
		} else {
			//min.val(0);
			min.attr('min','0');
			max.attr('min','0');
			//min.attr('max', '0');
		}

		if (max.val() < min.val()) {
			min.val(max.val());
		}
		min.attr('max', max.val());
	}

	function showHideFields(){
        var  type = $('.an-dt-type :selected').val();

        $('.an-dt-text-select-var').hide();
        $('.an-dt-checkbox-variant').hide();
        $('.js-dtma-allow_select_all_accessories').hide();
        
        if (type == 'text'){
            $('.an-dt-text-select-var').show();    
        } else {
			$('.js-dtma-allow_select_all_accessories').show();
        	if ($('#show_qty_on:checked').length) {
				$('.an-dt-checkbox-variant').show();
			}
		}
    }
	function showHideQty(){
		if ($('#show_qty_on:checked').length) {
			$('#allow_for_customer_change_min_qty_on').parents('.form-group').show();
		} else {
			$('#allow_for_customer_change_min_qty_on').parents('.form-group').hide();
		}
	}
	
	$(document).ready(function() {

		$(".js-example-basic-single").select2({ });
		
		$('.js-an-dt-products-add').on('click', function(){
			
			var selectedId = $('.an-dt-select-products').val();
			var selectedText = $('.an-dt-select-products').find(":selected").text();

			if (selectedText != '-'){
				$('.an-dt-products-selected').append('<div class="an-dt-products-item" data-id="'+selectedId+'">'+selectedText+' <a class="an-dt-remove js-an-dt-remove">Remove</a><input type="hidden" name="related_products[]" value="'+selectedId+'" /></div>');
			}
			
		});
		
		
		$(document).on('click', '.js-an-dt-remove', function(){
			$(this).parent('.an-dt-products-item').remove();
		});		
		
		
		$(".js-an-dt-select-accessories").on("select2:select", function (e) {

            var wrapProductsClass = '.js-anpfs-productsAttributes';

			getData(
                $(wrapProductsClass).attr('data-searchProdutsController'),
                $(this).closest('.row').find('.js-an-dt-accessories-add'), 
                function(data){
				    $('.js-an-dt-comb').html(data.select);				
			    }, 
                $(this).val()
            );	

		});		



		$(document).on('click', '.js-an-dt-accessories-add', function(){
			var productId = $('.an-dt-select-accessories').val();
			var productName = $('.an-dt-select-accessories').find(":selected").text();
			var attributeId = $('.an-dt-select-accessories-attribute').val();
			var attributeName = $('.an-dt-select-accessories-attribute').find(":selected").text();
			
			var atttribute = '';
			if (attributeId){
				atttribute = ': <strong>'+attributeName+'</strong>';
			} else {
				attributeId = 0;
			}
			
			if (productId && productId != '0'){
				$('.an-dt-accessories-selected').append('<div class="an-dt-products-item" data-id="'+productId+'">'+productName+'' + atttribute+' <a class="an-dt-remove js-an-dt-remove">Remove</a><input type="hidden" name="products_accessories_selected[]" value="'+productId+'-'+attributeId+'" /></div>');
			}
		});		


	
	});
	
})(jQuery, window);	

function getData(controller, button, callback, productId){	
	$(button).button('loading');
	$.ajax({
		type: "POST",
		url: controller,
        data: '&productId=' + productId,
		dataType: 'json',
	}).done(function(data){
		callback(data);
		
	}).always(function() {
		$(button).button('reset');
	});

}