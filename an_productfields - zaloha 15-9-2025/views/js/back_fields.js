/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/
 
 (function ($, window, undefined) {
    'use strict';

	$(document).ready(function() {

		showHideFiels();
        togglePriceTaxFields();
        showHideFielsPricePercent();
		
		$(document).on('click', '.js-an_productfields_products-remove', function(){
			$(this).closest('.js-an_productfields_products-line').remove();
		});		

        $('.js-an-pf-type-price input').on('click', function(){
			showHideFielsPricePercent();
		});
		
		$('.an-sz-type-view input').on('click', function(){
			showHideFiels();
		});		

		$('.js-pf-field-type').on('click', function(){
			togglePriceTaxFields();
            showHideFielsPricePercent();
		});	        

        function togglePriceTaxFields(){

            $('.js-pf-price-tax').hide();
            $('.js-pf-validation').hide();
            $('.js-pf-placeholder').hide();
            $('.js-pf-descr').hide();
            $('.js-pf-max_length').hide();
            $('.js-pf-char_counter').hide();
            $('.js-pf-default-value').hide();
            $('.js-pf-num-minmax').hide();
            $('.js-pf-max_file_size').hide();
            $('.js-pf-allow_files_format').hide();
            $('.js-pf-file').hide();

            var type = $('.js-pf-field-type').val();

            if (!type || type == 'text' || type == 'number' || type == 'textarea' || type == 'date' || type == 'file'){
                $('.js-pf-price-tax').show();
                $('.js-pf-descr').show();
            }

            if (type == 'select' || type == 'radio'){
                $('.js-pf-default-value').show();
            }

            if (type == 'text' || type == 'textarea'){
                $('.js-pf-validation').show();
                $('.js-pf-placeholder').show();
                $('.js-pf-max_length').show();
                $('.js-pf-char_counter').show();
            }          
            
            if (type == 'number'){
                $('.js-pf-placeholder').show();
                $('.js-pf-num-minmax').show();
            }

            if (type == 'file'){
                $('.js-pf-max_file_size').show();
                $('.js-pf-allow_files_format').show();
            }

            if (type == 'range_slider'){
                $('.js-pf-file').show();
            }

            
        }

        function showHideFielsPricePercent(){
            var price_type = $('.js-an-pf-type-price input[name=type_price]:checked').val();
            var type = $('.js-pf-field-type').val();
           
            $('.js-pf-price').hide();
            $('.js-pf-price_percent').hide();
            $('.js-pf-type_show_price').hide();
            $('.js-pf-free_characters').hide();

            if (!type || type == 'text' || type == 'number' || type == 'textarea' || type == 'date' || type == 'file'){
                if (price_type == 0){
                    $('.js-pf-price').show();
                } else if (price_type == 1) {
                    $('.js-pf-price_percent').show();
                    $('.js-pf-type_show_price').show();
                } else {
                    $('.js-pf-price').show();
                }

                if (type != 'date' && type != 'number' != 'file'){
                    $('#typePricePerCharacter').removeAttr('disabled');
                }

                if (price_type == 2 && type != 'date' && type != 'number'&& type != 'file'){
                    $('.js-pf-free_characters').show();
                }

                if ((type == 'number' || type == 'date' || type == 'file') && price_type == 2){
                    $('#typePriceAmount').prop('checked', true);
                    $('.js-pf-type_show_price').hide();
                }

                if (type == 'number' || type == 'date' || type == 'file'){
                    $('#typePricePerCharacter').removeAttr('checked');
                    $('#typePricePerCharacter').attr('disabled', true);
                }
            }
            
        }
		
		function showHideFiels(){
            
            $('.js-an_productfields-productsAttributes').hide();
            $('.js-an_productfields-search-group').hide();
            $('.js-anpfields-groups').hide();
			$('#id_root_category').parents('.form-group').hide();
					
			var relation = $('.an-sz-type-view input[name=relation]:checked').val();
			
			switch (relation) {
				
				case "1":
                    $('#id_root_category').parents('.form-group').show();
                    break;
				
				case "2":
                    $('.js-an_productfields-search-group').show();
			    	break;			
                
                case "3":
                    $('.js-anpfields-groups').show();
                    break;      

                case "4":
                    $('.js-an_productfields-productsAttributes').show();
                    break;                
			}
		}

		//js-sz-block-products	an-sz-type-view
		
	});
	
})(jQuery, window);	
