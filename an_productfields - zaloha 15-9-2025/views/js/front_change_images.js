
function setImages(defaultCover, defaultMinis, miniature, customMiniatures) {
    let an_fields = $('.js-product-container .js-an-pf-fields-wrap');

    an_fields.each(function () {
        let imageList = [],
            itemJson,
            defaultImage,
            defaultMiniatures,
            miniatureClone,
            mainImage,
            mainCount = 0;

        field = $(this);

        field.find('.js-an_productfields-price[type="checkbox"]:checked').each(function() {
            if ($(this).parent().siblings('.anfield-json').length) {
                itemJson = jQuery.parseJSON($(this).parent().siblings('.anfield-json').html());
                for(var i = 0; i < itemJson.length; i++) {
                    if (itemJson[i].isFileImg || itemJson[i].format == 'webm' || itemJson[i].format == 'mp4' || itemJson[i].format == 'youtube') {
                        imageList.push(itemJson[i]);
                    }
                }
            }
        });
        field.find('.js-an_productfields-price[type="radio"]').each(function() {
            if ($(this).prop("checked")) {
                if ($(this).parent().siblings('.anfield-json').length) {
                    itemJson = jQuery.parseJSON($(this).parent().siblings('.anfield-json').html());
                    for(var i = 0; i < itemJson.length; i++) {
                        if (itemJson[i].isFileImg || itemJson[i].format == 'webm' || itemJson[i].format == 'mp4' || itemJson[i].format == 'youtube') {
                            imageList.push(itemJson[i]);
                        }
                    }
                }
            }
        });
        field.find('select.js-an_productfields-price option:selected').each(function() {
            if (typeof $(this).attr('data-json') !== "undefined") {
                itemJson = jQuery.parseJSON($(this).attr('data-json'));
                for(var i = 0; i < itemJson.length; i++) {
                    if (itemJson[i].isFileImg || itemJson[i].format == 'webm' || itemJson[i].format == 'mp4' || itemJson[i].format == 'youtube') {
                        imageList.push(itemJson[i]);
                    }
                }
            }
        });

        if (imageList.length) {
            $('.js-product-container .js-images-container .product-cover .layer').hide();

            $('.js-product-container .js-images-container .js-qv-product-images').html('');
            for(var i = 0; i < imageList.length; i++) {
                miniature.clone().appendTo('.js-product-container .js-images-container .js-qv-product-images');
            }
            $('.js-product-container .js-images-container .js-thumb-container').each(function (index) {
                $(this).find('img').attr('src', imageList[index].fileUrl);
                if (imageList[index].main) {
                    mainCount++;
                    $(this).find('img').addClass('cover');
                    mainImage = imageList[index];

                }
                if (imageList[index].format == 'webm' || imageList[index].format == 'mp4') {
                    $(this).addClass('video');
                    $(this).find('img').attr('src', $('.js-an-pf-fields-wrap').data('video-poster'));
                    $(this).find('img').attr('data-src', imageList[index].fileUrl);

                }
                if (imageList[index].format == 'youtube') {
                    $(this).addClass('youtube');
                    $(this).find('img').attr('src', $('.js-an-pf-fields-wrap').data('video-poster'));
                    $(this).find('img').attr('data-src', imageList[index].file);

                }
            });

            if (mainCount != 1) {
                $('.js-product-container .js-images-container .js-qv-product-images').find('.cover').removeClass('cover');
                mainImage = $('head meta[property="og:image"]').attr('content');
                miniature.clone().addClass('cover-wrap').prependTo('.js-product-container .js-images-container .js-qv-product-images');
                $('.js-product-container .js-images-container .js-thumb-container.cover-wrap').find('img').addClass('cover').attr('src', mainImage);
                $('.js-product-container .js-images-container .js-qv-product-cover').attr('src', mainImage);
            } else {
                if (mainImage.isFileImg) {
                    $('.js-product-container .js-images-container .js-qv-product-cover').attr('src', mainImage.fileUrl);
                    $('.js-product-container .js-images-container .js-qv-product-cover').show();
                    $('.js-product-container .js-images-container .product-cover video').remove();
                    $('.js-product-container .js-images-container .product-cover .youtube-video').remove();
                }
                if (mainImage.format == 'webm' || mainImage.format == 'mp4') {
                    $('.js-product-container .js-images-container .js-qv-product-cover').hide();
                    $('.js-product-container .js-images-container .product-cover .youtube-video').remove();
                    if ($('.js-product-container .js-images-container .product-cover video').length) {
                        $('.js-product-container .js-images-container .product-cover video').attr('src', mainImage.fileUrl);
                    } else {
                        $('.js-product-container .js-images-container .product-cover').append('<video src="' + mainImage.fileUrl + '" class="anfield-video" controls="" controlslist="nodownload" autoplay="" poster=""></video>');
                    }
                }
                if (mainImage.format == 'youtube') {
                    $('.js-product-container .js-images-container .js-qv-product-cover').hide();
                    $('.js-product-container .js-images-container .product-cover video').remove();

                    mainImage.file = mainImage.file.replace('https://www.youtube.com/watch?v=', 'https://www.youtube.com/embed/').replace('https://youtu.be/', 'https://www.youtube.com/embed/');
                    mainImage.file = mainImage.file.replace(mainImage.file.match(/\&.+/), '').replace('?t=', '?start=');

                    if ($('.js-product-container .js-images-container .product-cover .youtube-video').length) {
                        $('.js-product-container .js-images-container .product-cover .youtube-video iframe').attr('src', mainImage.file);
                    } else {
                        $('.js-product-container .js-images-container .product-cover').append('<div class="youtube-video"><iframe width="560" height="315" src="' + mainImage.file + '" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe></div>');
                    }
                }
            }

            customMiniatures.appendTo('.js-product-container .js-images-container .js-qv-product-images');

            $('.js-product-container .js-images-container .js-qv-product-images').find('img').removeClass('selected js-thumb-selected');
            $('.js-product-container .js-images-container .js-qv-product-images').find('.cover').addClass('selected js-thumb-selected');


            if (imageList.length > 4) {
                $('.js-product-container .js-images-container .js-qv-mask').addClass('scroll');
                $('.js-product-container .scroll-box-arrows').addClass('scroll');
            } else {
                $('.js-product-container .js-images-container .js-qv-mask').removeClass('scroll');
                $('.js-product-container .scroll-box-arrows').removeClass('scroll');
            }

        } else {
            $('.js-product-container .js-images-container .product-cover .layer').show();
            $('.js-product-container .js-images-container .product-cover').html(defaultCover);
            $('.js-product-container .js-images-container .js-qv-product-images').html(defaultMinis);
            if ($('.js-product-container .js-images-container .js-qv-mask .js-thumb-container:not(.js-thumb-container-holder)').length > 4) {
                $('.js-product-container .js-images-container .js-qv-mask').addClass('scroll');
                $('.js-product-container .scroll-box-arrows').addClass('scroll');
            } else {
                $('.js-product-container .js-images-container .js-qv-mask').removeClass('scroll');
                $('.js-product-container .scroll-box-arrows').removeClass('scroll');
            }
        }

    });
}


function setImagesModal(defaultCover, defaultMinis, miniature, customMiniatures) {

    let an_fields = $('.modal-body .js-an-pf-fields-wrap');

    an_fields.each(function () {
        let imageList = [],
            itemJson,
            defaultImage,
            defaultMiniatures,
            miniatureClone,
            mainImage,
            mainCount = 0;

        field = $(this);

        field.find('.js-an_productfields-price[type="checkbox"]:checked').each(function() {
            if ($(this).parent().siblings('.anfield-json').length) {
                itemJson = jQuery.parseJSON($(this).parent().siblings('.anfield-json').html());
                for(var i = 0; i < itemJson.length; i++) {
                    if (itemJson[i].isFileImg || itemJson[i].format == 'webm' || itemJson[i].format == 'mp4' || itemJson[i].format == 'youtube') {
                        imageList.push(itemJson[i]);
                    }
                }
            }
        });
        field.find('.js-an_productfields-price[type="radio"]').each(function() {
            if ($(this).prop("checked")) {
                if ($(this).parent().siblings('.anfield-json').length) {
                    itemJson = jQuery.parseJSON($(this).parent().siblings('.anfield-json').html());
                    for(var i = 0; i < itemJson.length; i++) {
                        if (itemJson[i].isFileImg || itemJson[i].format == 'webm' || itemJson[i].format == 'mp4' || itemJson[i].format == 'youtube') {
                            imageList.push(itemJson[i]);
                        }
                    }
                }
            }
        });
        field.find('select.js-an_productfields-price option:selected').each(function() {
            if (typeof $(this).attr('data-json') !== "undefined") {
                itemJson = jQuery.parseJSON($(this).attr('data-json'));
                for(var i = 0; i < itemJson.length; i++) {
                    if (itemJson[i].isFileImg || itemJson[i].format == 'webm' || itemJson[i].format == 'mp4' || itemJson[i].format == 'youtube') {
                        imageList.push(itemJson[i]);
                    }
                }
            }
        });

        if (imageList.length) {
            $('.modal-body .js-images-container .product-cover .layer').hide();

            $('.modal-body .js-images-container .js-qv-product-images').html('');
            for(var i = 0; i < imageList.length; i++) {
                miniature.clone().appendTo('.modal-body .js-images-container .js-qv-product-images');
            }
            $('.modal-body .js-images-container .js-thumb-container').each(function (index) {
                $(this).find('img').attr('src', imageList[index].fileUrl);
                if (imageList[index].main) {
                    mainCount++;
                    $(this).find('img').addClass('cover');
                    mainImage = imageList[index];
                }
                if (imageList[index].format == 'webm' || imageList[index].format == 'mp4') {
                    $(this).addClass('video');
                    $(this).find('img').attr('src', $('.js-an-pf-fields-wrap').data('video-poster'));
                    $(this).find('img').attr('data-src', imageList[index].fileUrl);
                }
                if (imageList[index].format == 'youtube') {
                    $(this).addClass('youtube');
                    $(this).find('img').attr('src', $('.js-an-pf-fields-wrap').data('video-poster'));
                    $(this).find('img').attr('data-src', imageList[index].file);
                }
            });

            if (mainCount != 1) {
                $('.modal-body .js-images-container .js-qv-product-images').find('.cover').removeClass('cover');
                mainImage = $('.modal-default-image').html();
                miniature.clone().addClass('cover-wrap').prependTo('.modal-body .js-images-container .js-qv-product-images');
                $('.modal-body .js-images-container .js-thumb-container.cover-wrap').find('img').addClass('cover').attr('src', mainImage);
                $('.modal-body .js-images-container .js-qv-product-cover').attr('src', mainImage);
            } else {
                if (mainImage.isFileImg) {
                    $('.modal-body .js-images-container .js-qv-product-cover').attr('src', mainImage.fileUrl);
                    $('.modal-body .js-images-container .js-qv-product-cover').show();
                    $('.modal-body .js-images-container .product-cover video').remove();
                    $('.modal-body .js-images-container .product-cover .youtube-video').remove();
                }
                if (mainImage.format == 'webm' || mainImage.format == 'mp4') {
                    $('.modal-body .js-images-container .js-qv-product-cover').hide();
                    $('.modal-body .js-images-container .product-cover .youtube-video').remove();
                    if ($('.modal-body .js-images-container .product-cover video').length) {
                        $('.modal-body .js-images-container .product-cover video').attr('src', mainImage.fileUrl);
                    } else {
                        $('.modal-body .js-images-container .product-cover').append('<video src="' + mainImage.fileUrl + '" class="anfield-video" controls="" controlslist="nodownload" autoplay="" poster=""></video>');
                    }
                }
                if (mainImage.format == 'youtube') {
                    $('.modal-body .js-images-container .js-qv-product-cover').hide();
                    $('.modal-body .js-images-container .product-cover video').remove();

                    mainImage.file = mainImage.file.replace('https://www.youtube.com/watch?v=', 'https://www.youtube.com/embed/').replace('https://youtu.be/', 'https://www.youtube.com/embed/');
                    mainImage.file = mainImage.file.replace(mainImage.file.match(/\&.+/), '').replace('?t=', '?start=');

                    if ($('.modal-body .js-images-container .product-cover .youtube-video').length) {
                        $('.modal-body .js-images-container .product-cover .youtube-video iframe').attr('src', mainImage.file);
                    } else {
                        $('.modal-body .js-images-container .product-cover').append('<div class="youtube-video"><iframe width="560" height="315" src="' + mainImage.file + '" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe></div>');
                    }
                }
            }

            customMiniatures.appendTo('.modal-body .js-images-container .js-qv-product-images');

            $('.modal-body .js-images-container .js-qv-product-images').find('img').removeClass('selected js-thumb-selected');
            $('.modal-body .js-images-container .js-qv-product-images').find('.cover').addClass('selected js-thumb-selected');


            if (imageList.length > 4) {
                $('.modal-body .js-images-container .js-qv-mask').addClass('scroll');
                $('.modal-body .js-arrows').addClass('scroll');
            } else {
                $('.modal-body .js-images-container .js-qv-mask').removeClass('scroll');
                $('.modal-body .js-arrows').removeClass('scroll');
            }

        } else {
            $('.modal-body .js-images-container .product-cover .layer').show();
            $('.modal-body .js-images-container .product-cover').html(defaultCover);
            $('.modal-body .js-images-container .js-qv-product-images').html(defaultMinis);
            if ($('.modal-body .js-images-container .js-qv-mask .js-thumb-container:not(.js-thumb-container-holder)').length > 4) {
                $('.modal-body .js-images-container .js-qv-mask').addClass('scroll');
                $('.modal-body .scroll-box-arrows').addClass('scroll');
            } else {
                $('.modal-body .js-images-container .js-qv-mask').removeClass('scroll');
                $('.modal-body .scroll-box-arrows').removeClass('scroll');
            }
        }

    });
}


function waitForModal(selector) {
    if (jQuery(selector).length && !jQuery(selector).hasClass('.js-complete')) {
        jQuery(selector).addClass('.js-complete');

        var modaldefaultCover = $('.modal-body .js-images-container .product-cover').html(),
            modaldefaultMinis = $('.modal-body .js-images-container .js-qv-product-images').html(),
            modaldefaultMiniature = $('.modal-body .js-images-container .js-thumb-container:first-child'),
            modalcustomMiniatures = $('.modal-body .js-images-container .hipv-thumb-container');

    }
    setTimeout(function() {
        waitForModal(selector);
    }, 100);
}

$(document).ready(function () {
    var videolink;

    $(document).on('click', '.js-product-container .js-images-container .js-qv-product-images .js-thumb-container', function (e) {
        $('.js-product-container .js-images-container .js-qv-product-images').find('img').removeClass('selected js-thumb-selected');
        $(this).find('img').addClass('selected js-thumb-selected');
        $('.js-product-container .js-images-container .js-qv-product-cover').attr('src', $(this).find('img').attr('src'));

        if ($(this).hasClass('video')) {
            $('.js-product-container .js-images-container .js-qv-product-cover').hide();
            $('.js-product-container .js-images-container .product-cover .youtube-video').remove();
            if ($('.js-product-container .js-images-container .product-cover video').length) {
                $('.js-product-container .js-images-container .product-cover video').attr('src', $(this).find('img').attr('data-src'));
            } else {
                $('.js-product-container .js-images-container .product-cover').append('<video src="' + $(this).find('img').attr('data-src') + '" class="anfield-video" controls="" controlslist="nodownload" autoplay="" poster=""></video>');
            }
        } else if ($(this).hasClass('youtube')) {
            $('.js-product-container .js-images-container .js-qv-product-cover').hide();
            $('.js-product-container .js-images-container .product-cover video').remove();

            videolink = $(this).find('img').attr('data-src').replace('https://www.youtube.com/watch?v=', 'https://www.youtube.com/embed/').replace('https://youtu.be/', 'https://www.youtube.com/embed/');
            videolink = videolink.replace(videolink.match(/\&.+/), '').replace('?t=', '?start=');

            if ($('.js-product-container .js-images-container .product-cover .youtube-video').length) {
                $('.js-product-container .js-images-container .product-cover .youtube-video iframe').attr('src', videolink);
            } else {
                $('.js-product-container .js-images-container .product-cover').append('<div class="youtube-video"><iframe width="560" height="315" src="' + videolink + '" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe></div>');
            }
        } else {
            $('.js-product-container .js-images-container .js-qv-product-cover').show();
            $('.js-product-container .js-images-container .product-cover video').remove();
            $('.js-product-container .js-images-container .product-cover .youtube-video').remove();
        }

    });


    $(document).on('click', '.modal-body .js-images-container .js-qv-product-images .js-thumb-container', function (e) {
        $('.modal-body .js-images-container .js-qv-product-images').find('img').removeClass('selected js-thumb-selected');
        $(this).find('img').addClass('selected js-thumb-selected');
        $('.modal-body .js-images-container .js-qv-product-cover').attr('src', $(this).find('img').attr('src'));

        if ($(this).hasClass('video')) {
            $('.modal-body .js-images-container .js-qv-product-cover').hide();
            $('.modal-body .js-images-container .product-cover .youtube-video').remove();
            if ($('.modal-body .js-images-container .product-cover video').length) {
                $('.modal-body .js-images-container .product-cover video').attr('src', $(this).find('img').attr('data-src'));
            } else {
                $('.modal-body .js-images-container .product-cover').append('<video src="' + $(this).find('img').attr('data-src') + '" class="anfield-video" controls="" controlslist="nodownload" autoplay="" poster=""></video>');
            }
        } else if ($(this).hasClass('youtube')) {
            $('.modal-body .js-images-container .js-qv-product-cover').hide();
            $('.modal-body .js-images-container .product-cover video').remove();
            videolink = $(this).find('img').attr('data-src').replace('https://www.youtube.com/watch?v=', 'https://www.youtube.com/embed/').replace('https://youtu.be/', 'https://www.youtube.com/embed/');
            videolink = videolink.replace(videolink.match(/\&.+/), '').replace('?t=', '?start=');
            if ($('.modal-body .js-images-container .product-cover .youtube-video').length) {
                $('.modal-body .js-images-container .product-cover .youtube-video iframe').attr('src', videolink);
            } else {
                $('.modal-body .js-images-container .product-cover').append('<div class="youtube-video"><iframe width="560" height="315" src="' + videolink + '" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe></div>');
            }
        } else {
            $('.modal-body .js-images-container .js-qv-product-cover').show();
            $('.modal-body .js-images-container .product-cover video').remove();
            $('.modal-body .js-images-container .product-cover .youtube-video').remove();
        }
    });


    var defaultCover = $('.js-product-container .js-images-container .product-cover').html(),
        defaultMinis = $('.js-product-container .js-images-container .js-qv-product-images').html(),
        defaultMiniature = $('.js-product-container .js-images-container .js-thumb-container:first-child'),
        customMiniatures = $('.js-product-container .js-images-container .hipv-thumb-container');


    setImages(defaultCover, defaultMinis, defaultMiniature, customMiniatures);

    $(document).on('slidestop', '.js-product-container .anfield-slider', function(e) {
        setTimeout(function () {
            setImages(defaultCover, defaultMinis, defaultMiniature, customMiniatures);
        }, 50)
    });

    $(document).on('change', '.js-product-container .js-an_productfields-price', function () {
        setImages(defaultCover, defaultMinis, defaultMiniature, customMiniatures);
    });
/*
    var modaldefaultCover = $('.modal-body .js-images-container .product-cover').html(),
        modaldefaultMinis = $('.modal-body .js-images-container .js-qv-product-images').html(),
        modaldefaultMiniature = $('.modal-body .js-images-container .js-thumb-container:first-child'),
        modalcustomMiniatures = $('.modal-body .js-images-container .hipv-thumb-container');
*/
    waitForModal('.modal.quickview');

    $(document).on('slidestop', '.modal-body .anfield-slider', function(e) {
        setTimeout(function () {
            setImagesModal(modaldefaultCover, modaldefaultMinis, modaldefaultMiniature, modalcustomMiniatures);
        }, 50)
    });

    $(document).on('change', '.modal-body .js-an_productfields-price', function () {
        setImagesModal(modaldefaultCover, modaldefaultMinis, modaldefaultMiniature, modalcustomMiniatures);
    });

    $(document).ajaxComplete(function(event, xhr, settings) {

        if ((xhr.responseJSON != undefined) && xhr.responseJSON.hasOwnProperty('quickview_html') && (xhr.responseJSON.quickview_html != undefined)) {

            setTimeout(function () {
                modaldefaultCover = $('.modal-body .js-images-container .product-cover').html(),
                modaldefaultMinis = $('.modal-body .js-images-container .js-qv-product-images').html(),
                modaldefaultMiniature = $('.modal-body .js-images-container .js-thumb-container:first-child'),
                modalcustomMiniatures = $('.modal-body .js-images-container .hipv-thumb-container');

                setImagesModal(modaldefaultCover, modaldefaultMinis, modaldefaultMiniature, modalcustomMiniatures);
            }, 250)
        }
        if ((xhr.responseJSON != undefined) && xhr.responseJSON.hasOwnProperty('id_product_attribute') && (xhr.responseJSON.id_product_attribute != undefined)) {
            defaultCover = $('.js-product-container .js-images-container .product-cover').html(),
            defaultMinis = $('.js-product-container .js-images-container .js-qv-product-images').html(),
            defaultMiniature = $('.js-product-container .js-images-container .js-thumb-container:first-child'),
            customMiniatures = $('.js-product-container .js-images-container .hipv-thumb-container');
            setTimeout(function () {
                setImages(defaultCover, defaultMinis, defaultMiniature, customMiniatures);
            }, 50)

        }
    });
});