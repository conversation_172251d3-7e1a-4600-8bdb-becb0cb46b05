/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/
 
 (function ($, window, undefined) {
    'use strict';

    function anproducts(wrapProductsClass) {

        var classProductLine = $(wrapProductsClass).attr('data-classProductLine');
        var classProductDelete = $(wrapProductsClass).attr('data-classProductDelete');
        var classInputName = $(wrapProductsClass).attr('data-inputName');
        var classSarchInput = $(wrapProductsClass).attr('data-classSarchInput');

        var loc = location.pathname.split('/');
        loc.pop();

        var ajax_get_products = function (q) {
            return $.get($(wrapProductsClass).attr('data-searchProdutsController')+'&q='+q);
        };

        var promise = $.when();
        var ptreeinput = $('.' + classSarchInput).closest('.form-group');
        ptreeinput.children('div').append($('<ul id="anSearchProducts-ajax-list"></ul>'));

        $('#anSearchProducts-ajax-list').on('click', 'li', function () {
			
			var name = $(this).find('.anSearchProducts-ajax-name').html();
			var cover = $(this).find('img').attr('src');
			var id = $(this).data('id');

            var newProduct = '<div class="'+classProductLine+'">' + 
            '<input type="hidden" name="'+classInputName+'" value="'+id+'" />' +
            '<div class="label">' + 
            '<span class="anSearchProducts-img"><img src="' + cover + '" /></span>' + 
            '<span>' + name + '</span>' + 
            '<i class="material-icons delete js-anSearchProducts-product-delete ' + classProductDelete + '">delete</i>' +
            '</div>' +
            '</div>';
			
			$(wrapProductsClass).append(newProduct);
			
			$('.'+classSarchInput).val('');
            $('#anSearchProducts-ajax-list').html('');
        });
        $('.'+classSarchInput).on('keyup', function () {
            var q = $(this).val();
            (function (value) {
                promise = promise.then(function () {
                    return ajax_get_products(q);
                }).then(function (response) {
                    if (!response) {
                        return false;
                    }
                    (function (products) {
                        ptreeinput.find('#anSearchProducts-ajax-list').html('');
                        $.each(products, function (i, product) {
                            ptreeinput.find('#anSearchProducts-ajax-list').append($('' +
                                '<li data-id="'+product.id+'">' +
                                '<div class="label">' +
                                '<span class="anSearchProducts-ajax-img"><img src="' + product.cover + '" /></span> ' +
                                '<span class="anSearchProducts-ajax-name">' + product.name + '</span>' +
                                '</div>' + 
                                '</li>').on('click', function () {
                            }));
                        });
                    })(JSON.parse(response));
                });
            })(q);
        });

    };
	$(document).ready(function() {

        var wrapProductsClass = '.js-an_productfields_products';    // It need to edit
        anproducts(wrapProductsClass);
		$(document).on('click', '.js-anSearchProducts-product-delete', function(){
			$(this).closest('.js-anSearchProducts-product').remove();
		});	
        
	});
	
})(jQuery, window);	
