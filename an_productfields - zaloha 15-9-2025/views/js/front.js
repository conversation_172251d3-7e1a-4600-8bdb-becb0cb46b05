/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/
!function(a){function f(a,b){if(!(a.originalEvent.touches.length>1)){a.preventDefault();var c=a.originalEvent.changedTouches[0],d=document.createEvent("MouseEvents");d.initMouseEvent(b,!0,!0,window,1,c.screenX,c.screenY,c.clientX,c.clientY,!1,!1,!1,!1,0,null),a.target.dispatchEvent(d)}}if(a.support.touch="ontouchend"in document,a.support.touch){var e,b=a.ui.mouse.prototype,c=b._mouseInit,d=b._mouseDestroy;b._touchStart=function(a){var b=this;!e&&b._mouseCapture(a.originalEvent.changedTouches[0])&&(e=!0,b._touchMoved=!1,f(a,"mouseover"),f(a,"mousemove"),f(a,"mousedown"))},b._touchMove=function(a){e&&(this._touchMoved=!0,f(a,"mousemove"))},b._touchEnd=function(a){e&&(f(a,"mouseup"),f(a,"mouseout"),this._touchMoved||f(a,"click"),e=!1)},b._mouseInit=function(){var b=this;b.element.bind({touchstart:a.proxy(b,"_touchStart"),touchmove:a.proxy(b,"_touchMove"),touchend:a.proxy(b,"_touchEnd")}),c.call(b)},b._mouseDestroy=function(){var b=this;b.element.unbind({touchstart:a.proxy(b,"_touchStart"),touchmove:a.proxy(b,"_touchMove"),touchend:a.proxy(b,"_touchEnd")}),d.call(b)}}}(jQuery);

function calcPrice() {
    let an_fields = $('.js-an-pf-fields-wrap');

    if (an_fields.data('calculationtotalfields') || an_fields.data('updateprices')) {
        an_fields.each(function () {
            let an_fields_total_wt = 0,
                an_fields_total = 0;
                priceForFormet = 0;

            field = $(this);
            field.find('.js-anpf-field:not(.an-pf-hide) .js-an_productfields-price[type="text"]').each(function() {
                let elem = $(this);
                if (elem[0].value != '') {
                    if (elem.data('type_price') == 2) {
                        let count = +elem.siblings('.anpf-field-counter-max_length').find('.js-anpf-field-counter').html();
                        if (count > elem.data('free_characters')) {
                            count = count - elem.data('free_characters');
                        } else {
                            count = 0;
                        }
                        an_fields_total_wt += ($(this).data('price_wt') * count);
                        an_fields_total += (+$(this).data('price') * count);
                    } else {
                        an_fields_total_wt += $(this).data('price_wt');
                        an_fields_total += +$(this).data('price');
                    }
                }
            });
            field.find('.js-anpf-field:not(.an-pf-hide) textarea.js-an_productfields-price').each(function() {
                let elem = $(this);
                if (elem[0].value != '') {
                    if (elem.data('type_price') == 2) {
                        let count = +elem.siblings('.anpf-field-counter-max_length').find('.js-anpf-field-counter').html();
                        if (count > elem.data('free_characters')) {
                            count = count - elem.data('free_characters');
                        } else {
                            count = 0;
                        }
                        an_fields_total_wt += ($(this).data('price_wt') * count);
                        an_fields_total += (+$(this).data('price') * count);
                    } else {
                        an_fields_total_wt += $(this).data('price_wt');
                        an_fields_total += +$(this).data('price');
                    }
                }
            });
            field.find('.js-anpf-field:not(.an-pf-hide) .js-an_productfields-price[type="checkbox"]:checked').each(function() {
                an_fields_total_wt += $(this).data('price_wt');
                an_fields_total += +$(this).data('price');
            });
            field.find('.js-anpf-field:not(.an-pf-hide) .js-an_productfields-price[type="radio"]').each(function() {
                if ($(this).prop("checked")) {
                    $(this).parents('.anfield-radio').siblings('.anfield-radio').find('input').removeAttr('checked');
                    $(this).attr('checked', 'checked');
                    an_fields_total_wt += $(this).data('price_wt');
                    an_fields_total += +$(this).data('price');
                }
            });
            field.find('.js-anpf-field:not(.an-pf-hide) select.js-an_productfields-price option:selected').each(function() {
                if ($(this).data('price_wt') != undefined) {
                    an_fields_total_wt += $(this).data('price_wt');
                    an_fields_total += +$(this).data('price');
                }
            });
            field.find('.js-anpf-field:not(.an-pf-hide) .js-an_productfields-price[type="number"]:not(.js-an_productfields-multiply)').each(function() {
                an_fields_total_wt += $(this).data('price_wt');
                an_fields_total += + $(this).data('price');
            });
            field.find('.js-anpf-field:not(.an-pf-hide) .js-an_productfields-multiply').each(function() {
                an_fields_total_wt += $(this).data('price_wt')*$(this).val();
                an_fields_total += + $(this).data('price')*$(this).val();
            });

            if (field.data('pricedisplaymethod')) {
                priceForFormet =an_fields_total_wt;
            } else {
                priceForFormet =an_fields_total;
            }


            if (an_fields.data('updateprices')) {
                var priceContainer, price, priceVar, priceContVar, fieldsContVar, regularPriceContainer, priceDefault;
                var regularPrice = 0;

                var priceVarData = $(this).data('classprice');
                var priceContVarData = $(this).data('classpricecontainer');
                var fieldsContVarData = $(this).data('classfieldscontainer');

                if ($(this).parents('.modal-body').length) {
                    regularPriceContainer = $(this).parents('.modal-body').find('.product-discount').find('.regular-price');
                    if (priceVarData) {
                        priceContainer = $(this).parents('.modal-body').find(priceVarData);
                    } else {
                        if ($(this).parents('.modal-body').find('span[itemprop=price]').length) {
                            priceContainer = $(this).parents('.modal-body').find('span[itemprop=price]');
                        } else {
                            priceContainer = $(this).parents('.modal-body').find('.current-price-value');
                        }
                    }
                    priceDefault = $(this).parents('.modal-body').find('.js-an-pf-original-prices');
                } else {
                    priceContVar = priceContVarData ? priceContVarData : '.product-prices';
                    if (fieldsContVarData) {
                        fieldsContVar = fieldsContVarData;
                        if (fieldsContVar == '.an-pf-fields-wrap') {
                            regularPriceContainer = $(this).siblings(priceContVar).find('.product-discount').find('.regular-price');
                        } else {
                            regularPriceContainer = $(this).parents(fieldsContVar).siblings(priceContVar).find('.product-discount').find('.regular-price');
                        }
                    } else {
                        fieldsContVar = '.product-information';
                        regularPriceContainer = $(this).parents(fieldsContVar).siblings(priceContVar).find('.product-discount').find('.regular-price');
                    }

                    if (priceVarData) {
                        priceVar = priceVarData;
                    } else {
                        if ($(this).parents(fieldsContVar).siblings(priceContVar).find('span[itemprop=price]').length) {
                            priceVar = 'span[itemprop=price]';
                        } else {
                            priceVar = '.current-price-value';
                        }
                    }

                    if (fieldsContVar == '.an-pf-fields-wrap') {
                        priceContainer = $(this).siblings(priceContVar).find(priceVar);
                    } else {
                        priceContainer = $(this).parents(fieldsContVar).siblings(priceContVar).find(priceVar);
                    }

                    priceDefault = $(this).parents(fieldsContVar).siblings(priceContVar).find('.js-an-pf-original-prices');
                }

                if (priceDefault.length) {
                    price = parseFloat((priceDefault).data('price_amount'));
                } else {
                    price = parseFloat(priceContainer.attr('content'));
                }

                if (regularPriceContainer.attr('data-original')){
                    regularPrice = regularPriceContainer.attr('data-original');
                    regularPrice = clearNum(regularPrice);
                }
                if (!regularPrice){
                    regularPrice = regularPriceContainer.html();
                    regularPriceContainer.attr('data-original', regularPrice);
                    regularPrice = clearNum(regularPrice);
                }
                regularPrice = regularPrice.replace(',', '.').replace(/[^\.\d]/g, '')
                var sum = price+priceForFormet;
                var sumRegular = +regularPrice+priceForFormet;

                getData(field.attr('data-url'), '&price[default]='+sum+'&price[regular]='+sumRegular+'&price[calc]='+priceForFormet+'&action=getPrices', field, function(data, anfield){
                    regularPriceContainer.html(data.prices.regular);
                    priceContainer.html(data.prices.default);
                    if (an_fields.data('calculationtotalfields')) {
                        anfield.find('.an-pf-fields-total .js-an-pf-fields-price').html(data.prices.calc);
                    }
                });
            }

            if (!an_fields.data('updateprices') && an_fields.data('calculationtotalfields')) {
                getData(field.attr('data-url'), '&price='+priceForFormet+'&action=getPrices', field, function(data, anfield){
                    anfield.find('.an-pf-fields-total .js-an-pf-fields-price').html(data.price);
                });
            }


            priceForFormet = 0;
            an_fields_total_wt = 0;
            an_fields_total = 0;
        });
    }
}

function clearNum(num){
    if (num){
        return num.replace(/[^0-9,.]/g, ' ');
    } else {
        return '0';
    }
}
function waitForEl(selector) {
    if (jQuery(selector).length && !jQuery(selector).hasClass('.js-moved')) {
        let modalAdditionalInfo = jQuery(selector).find('.modal-footer .js-product-additional-info');
        jQuery(selector).find('.modal-footer .js-product-additional-info').remove();
        jQuery(selector).find('.modal-body .product-actions form').prepend(modalAdditionalInfo);
        jQuery(selector).addClass('.js-moved');
        calcPrice();
    }
    setTimeout(function() {
        waitForEl(selector);
    }, 100);
}

function chacgedPositionAnFields(position = '.product-actions form', before){
    var productAdditionalInfo = $('.js-an-pf-fields-wrap:not(.js-an-pf-position)').addClass('js-an-pf-position');
    $('.js-an-pf-fields-wrap').parents('.elementor-widget-ps-widget-module').addClass('an-pf-hide');
    $('.js-an-pf-fields-wrap').parents('.elementor-widget-ps-widget-module').after(productAdditionalInfo);
    if (before) {
        $(position).before(productAdditionalInfo);
    } else {
        $(position).prepend(productAdditionalInfo);
    }
}

function changedPositionAnFieldsModal(position = '.product-actions form', before){
    if (!$('.modal-body .js-an-pf-fields-wrap.js-an-pf-position').length) {
        var field = $('.modal-body .js-an-pf-fields-wrap:not(.js-an-pf-position)').addClass('js-an-pf-position');
        if (before) {
            $('.modal-body').find(position).before(field);
        } else {
            $('.modal-body').find(position).prepend(field);
        }
    }
}
function initSomeElementsAnFields(){
    $('.js-an_productfields-date').datepicker();
    $("[data-toggle='tooltip']").tooltip();
    $('.js-an_productfields-price[type="radio"]').each(function() {
        if ($(this).attr("checked")) {
            $(this).prop('checked', true);
        } else {
            //$(this).removeAttr('checked');
        }
    });
}

function initRangeSlider(){
    $('.anfield-slider-block').each(function() {
        let wrapper = $(this),
            slider = $(this).find('.anfield-slider'),
            selectedel = wrapper.find('input:checked').parents('.anfield-slider-element').index()+1;

        slider.slider({
            min: 1,
            range: false,
            step: 1,
            max: slider.data('count'),
            value: selectedel,
            animate: "fast",
            orientation: "horizontal",
            stop: function (event, ui) {
                wrapper.find('.anfield-slider-element input').prop('checked', false).attr('checked', false);
                wrapper.find('.anfield-slider-element').eq(ui.value - 1).find('input').prop('checked', true).attr('checked', true);
                calcPrice();
            }
        });
        slider.draggable();
    });
}

function calcPercentages() {
    let productPrice,
        priceContainer,
        prices='';
    const pricesArr = new Map();
    $('[data-type_price="1"]').each(function () {
        if ($('span[itemprop=price]').length) {
            priceContainer = $('.product-prices span[itemprop=price]');
        } else {
            priceContainer = $('.product-prices .current-price-value');
        }
        if ($(this).parents('.modal-body').length) {
            if ($(this).parents('.modal-body').find('.js-an-pf-original-prices').length) {
                productPrice = $(this).parents('.modal-body').find('.js-an-pf-original-prices').data('price_amount');
            } else {
                productPrice = $(this).parents('.modal-body').find(priceContainer).attr('content');
            }
        } else {
            if ($(this).parents('.product-container').find('.js-an-pf-original-prices').length) {
                productPrice = $(this).parents('.product-container').find('.js-an-pf-original-prices').data('price_amount');
            } else {
                productPrice = $(this).parents('.product-container').find(priceContainer).attr('content');
            }
        }
        $(this).data('price', productPrice*$(this).data('price-percentage')/100);
        $(this).attr('data-price', productPrice*$(this).data('price-percentage')/100);
        $(this).data('price_wt', productPrice*$(this).data('price-percentage')/100);
        $(this).attr('data-price_wt', productPrice*$(this).data('price-percentage')/100);


        if ($(this).attr('data-percentage_type_show_price') == 'calculated'){
            let id = $(this).attr('id');
            if ($('.js-an-pf-fields-wrap').data('pricedisplaymethod')){
                if (typeof id != 'undefined' && $(this).attr('id') != false) {
                    prices+='&price['+id+']='+$(this).data('price_wt');
                } else {
                    prices+='&price['+$(this).attr('name')+']='+$(this).data('price_wt');
                }
            } else {
                if (typeof id !== 'undefined' && $(this).attr('id') !== false) {
                    prices+='&price['+id+']='+$(this).data('price');
                } else {
                    prices+='&price['+$(this).attr('name')+']='+$(this).data('price');
                }
            }
        }
    });
    let an_fields = $('.js-an-pf-fields-wrap');
    an_fields.each(function () {
        field = $(this);
        if (field.find('[data-type_price="1"][data-price-percentage]:not(".title-calculated")').length) {
            field.find('[data-type_price="1"][data-price-percentage]:not(".title-calculated")').addClass('title-calculated');
            getData(field.attr('data-url'), prices + '&action=getPrices', field, function (data, anfield) {
                $.each(data.prices, function(index,value){
                    if (field.find('[name="'+index+']"').length) {
                        field.find('[name="'+index+']"').parents('.js-anpf-field').find('.js-anpf-label-price').html(value);
                    } else {
                        field.find('[id="'+index+'"]').siblings('.label-price').find('span').html(value);
                    }
                });
            });
        }
    });
}

function getData(controller, dataUrl, anfield, callback){
    $.ajax({
        type: "POST",
        url: controller,
        data: dataUrl,
        dataType: 'json',
    }).done(function(data){
        callback(data, anfield);
    }).always(function() {

    });
}

class dynamicTimer {
    constructor(func, delay) {
        this.callback = func
        this.triggerTime = delay
        this.timer = 0
        this.updateTimer()
    }
    updateTimer() {
        clearTimeout(this.timer)
        let delay = this.triggerTime
        this.timer = setTimeout(this.callback, delay)
        return this
    }
    addTime(delay) {
        this.triggerTime = delay
        this.updateTimer()
        return this
    }
}


$(document).ready(function () {

    $(document).on('change', '.an-pf-field-type-file-wrap .filestyle', function () {
        $(this).parents('.an-pf-field-type-file-wrap').find('input[type="text"]').val($(this)[0].files[0].name);
    });

    $(document).on("click", '.an-pf-addtocart-button', function(e){

        e.preventDefault();

        var data = new FormData();

        var form_data = $('#add-to-cart-or-refresh').serializeArray();
        $.each(form_data, function (key, input) {
            data.append(input.name, input.value);
        });

        $('#add-to-cart-or-refresh input[type="file"]').each(function(){
            let elem = $(this)[0].files;
            if (elem.length) {
                //console.log(elem[0]);
                data.append($(this).attr('name'), elem[0]);
            }
        });

        data.append('add', parseInt($('#quantity_wanted').val()));
        data.append('action', 'update');

        $.ajax({
            url: $('#add-to-cart-or-refresh').attr('action'),
            headers: {
                Accept: 'application/json, text/javascript, */*; q=0.01'
            },
            method: "post",
            processData: false,
            contentType: false,
            data: data,
            success: function (data) {
            },
            complete: function(data,) {
                let ajax_error_text = '',
                    jsonData;

                try {
                    jsonData = JSON.parse(data.responseText)
                }
                catch (e) {
                    console.log("error: "+e);
                };

                if (jsonData.hasError) {
                    $.each(jsonData.errors, function (index, value) {
                        ajax_error_text += value + '<br>';
                    });
                    if ($('.product-add-to-cart .ajax-error').length) {
                        $('.product-add-to-cart .ajax-error').show().html(ajax_error_text);
                    } else {
                        $('.product-add-to-cart').append('<div class="alert alert-danger ajax-error">' + ajax_error_text + '</div>');
                    }
                } else {
                    $('.product-add-to-cart .ajax-error').hide();
                    var dataProduct = $('#product-details').data('product'),
                        dataSelected;
                    dataSelected = 'id_customization='+dataProduct.id_customization+'&id_product_attribute='+dataProduct.id_product_attribute+'&id_product='+dataProduct.id_product+'&action=add-to-cart';

                    $.post($('.blockcart').data('refresh-url'), dataSelected).then(function (resp) {
                        var html = $('<div />').append($.parseHTML(resp.preview));
                        $('.blockcart').replaceWith($(resp.preview).find('.blockcart'));
                        if (resp.modal) {
                            showModal(resp.modal);
                        }
                    }).fail(function (resp) {
                        prestashop.emit('handleError', { eventType: 'updateShoppingCart', resp: resp });
                    });
                }
            },
            error: function (e) {
            }
        });

        prestashop.blockcart = prestashop.blockcart || {};

        var showModal = prestashop.blockcart.showModal || function (modal) {
            var $body = $('body');
            $body.append(modal);
            $body.one('click', '#blockcart-modal', function (event) {
                if (event.target.id === 'blockcart-modal') {
                    $(event.target).remove();
                }
            });
        };

    });



    let timer = new dynamicTimer(function() {
        calcPrice();
    }, 500);

    $(document).on("input", '.js-anpf-counter', function(timeoutID) {
        let elem = $(this);
        if (elem.data('ignore_spaces')) {
            currentLength = elem.val().replace(/\s+/g,'').length;
        } else {
            currentLength = elem.val().length;
        }
        elem.closest('.js-anpf-field').find('.js-anpf-field-counter').html(currentLength);
        if (elem.data('type_price') == 2) {
            timer.addTime(500);
        }
    });

    if ($('.js-an-pf-fields-wrap').data('classfieldsposition')) {
        chacgedPositionAnFields($('.js-an-pf-fields-wrap').data('classfieldsposition'), 1);
    } else {
        chacgedPositionAnFields('.product-actions form', 0);
    }

    waitForEl('.modal.quickview');

    initRangeSlider();
    initSomeElementsAnFields();

    calcPercentages();
    calcPrice();

    $(document).on('change', '.js-an_productfields-price:not([type="text"])', function () {
        calcPercentages();
        calcPrice();

    });
    $(document).on('keyup', '.js-an_productfields-multiply', function () {
        calcPrice();
    });
    $(document).on('keyup', '.js-an_productfields-price[type="text"], textarea.js-an_productfields-price', function () {
        if ($(this).hasClass('calculated')) {
            if ($(this)[0].value == '') {
                $(this).removeClass('calculated');
                calcPrice();
            }
        } else {
            if ($(this)[0].value != '') {
                $(this).addClass('calculated')
                calcPrice();
            }
        }

    });

    $(document).ajaxComplete(function(event, xhr, settings) {

        if ($('.js-an-pf-fields-wrap').data('classfieldsposition')) {
            changedPositionAnFieldsModal($('.js-an-pf-fields-wrap').data('classfieldsposition'), 1);
        } else {
            changedPositionAnFieldsModal('.product-actions form', 0);
        }
        $('.elementor-element .js-an-pf-fields-wrap').not('.js-an-pf-position').remove();
        $('.product-information .js-an-pf-fields-wrap, .product-actions .js-an-pf-fields-wrap').not('.js-an-pf-position').remove();
        $('.js-product-additional-info .js-an-pf-fields-wrap').not('.js-an-pf-position').remove();
        initSomeElementsAnFields();
        calcPercentages();


        if ((xhr.responseJSON != undefined) && xhr.responseJSON.hasOwnProperty('quickview_html') && (xhr.responseJSON.quickview_html != undefined)) {

            setTimeout(function () {
                initRangeSlider();
            }, 150)
        }

        let ajax_url = settings.url,
            ajax_error_text = '';



        if ((xhr.responseJSON != undefined) && xhr.responseJSON.hasOwnProperty('quantity') && (xhr.responseJSON.quantity != undefined)) {


            if (xhr.responseJSON.hasError) {

                $.each(xhr.responseJSON.errors, function (index, value) {
                    ajax_error_text += value + '<br>';
                });
                if ($('.js-product-add-to-cart .ajax-error').length) {
                    $('.js-product-add-to-cart .ajax-error').show().html(ajax_error_text);
                } else {
                    $('.js-product-add-to-cart').append('<div class="alert alert-danger ajax-error">' + ajax_error_text + '</div>');
                }
            } else {
                $('.js-product-add-to-cart .ajax-error').hide();
            }
        }
        if (xhr.length && !xhr.responseJSON.price) {
            calcPrice();
        }

        if ((xhr.responseJSON != undefined) && xhr.responseJSON.hasOwnProperty('id_product_attribute') && (xhr.responseJSON.id_product_attribute != undefined)) {
            calcPrice();
        }

    });

    $('body').on('keyup', '.js-an-pf-number', function () {
        let input = $(this);
        if ( input.val() != '' && input.val() > +input.attr('max') ) {
            input.val(+input.attr('max'));
        }
        if ( input.val() != '' && input.val() < +input.attr('min') ) {
            input.val(+input.attr('min'));
        }
    });
    $('body').on('focusout', '.js-an-pf-number', function () {
        let input = $(this);
        if (input.val() > +input.attr('max') ) {
            input.val(+input.attr('max'));
        }
        if (input.val() < +input.attr('min') ) {
            input.val(+input.attr('min'));
        }
    });

    prestashop.on(
        'updateCart',
        function (event) {
            if (event && event.reason) {
                $('.an-pf-field-type-textarea .form-control, .an-pf-field-type-text .form-control, .an-pf-field-type-date .form-control, .an-pf-field-type-number .form-control').each(function () {
                    let elem = $(this);
                    elem.val(elem.attr('value'));
                });
                $('.an-pf-field-type-radio, .an-pf-field-type-range_slider').each(function () {
                    let field = $(this);
                    field.find('input[type="radio"]').each(function () {
                        let elem = $(this);
                        if (elem.data('defaultvalue')) {
                            elem.attr('checked', 'checked');
                            elem.prop('checked', true);

                        } else {
                            elem.removeAttr('checked');
                            elem.prop('checked', false);
                        }
                    });
                    if (field.find('.form-control-slider').length) {
                        field.find('.form-control-slider').slider('value', 1);
                    }
                });
                $('.an-pf-field-type-select .form-control').each(function () {
                    let field = $(this);
                    field.val(field.find('option[selected="selected"]').val());
                });
                $('.an-pf-field-type-multiselect .form-control').val('');
                $('.an-pf-field-type-checkbox').each(function () {
                    let field = $(this);
                    field.find('input[type="checkbox"]').each(function () {
                        let elem = $(this);
                        elem.removeAttr('checked');
                        elem.prop('checked', false);
                    });
                });

            }
        }
    );

});


