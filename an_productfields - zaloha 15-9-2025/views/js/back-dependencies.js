/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/
 
(function ($, window, undefined) {
    'use strict';

	$(document).ready(function() {

        excludeCurrentField();

		$('.js-pf-dependencies-fieldvalue').on('click', function(){
			excludeCurrentField();
		});	

        function excludeCurrentField()
        {
            var idValue = $('.js-pf-dependencies-fieldvalue').val();

            $('.js-pf-dependencies-fields input').each(function() {
                if ($(this).val() == an_pf_values_fields[idValue]){
                    $(this).removeAttr('checked');
                    $(this).attr('disabled', 'disabled');
                } else {
                    $(this).removeAttr('disabled');
                }
            });
        }
	});
	
})(jQuery, window);	
