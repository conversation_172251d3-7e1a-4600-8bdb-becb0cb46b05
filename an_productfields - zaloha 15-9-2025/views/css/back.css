/**
* 2024 Anvant<PERSON>
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

.an-pf-hide {
  display: none;
}


.form-group-image .an-image-radio {
  width: 30px;
}
.form-group-image .an-image-miniature {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: normal;
  line-height: 1.2;
  min-height: 40px;
}
.form-group-image .an-image-miniature-img:hover + span a {
  text-decoration: underline;
}
.form-group-image .an-image-miniature img {
  max-width: 30px;
  max-height: 30px;
}
.form-group-image .an-imagelink-input {
  height: 40px!important;
}
.form-group-image .an-image-button-wrap {
  width: calc(25% - 30px);
}
.form-group-image .an-image-deletebutton {
  padding: 10px 10px;
  line-height: 18px;
  min-width: 80px;
}
.form-group-image-hint {
  color: #959595;
  font-style: italic;
  margin-top: 20px;
  margin-bottom: 20px;
}