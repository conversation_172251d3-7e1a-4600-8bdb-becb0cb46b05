/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/
.an-pf-hide {
  display: none;
}

.an-pf-original-prices {
  display: none;
}
.an-pf-field-label-title {
  margin-right: 4px;
}
.an-pf-tooltip {
  display: flex;
  align-items: center;
  margin-left: 7px;
  margin-top: -2px;
  cursor: pointer;
  width: 18px;
  height: 18px;
}

/* Tooltip text */
.an-pf-tooltip svg {
  width: 18px;
  height: 18px;
}
.an-pf-tooltip .an-pf-tooltiptext {
  visibility: hidden;
  width: 120px;
  background-color: black;
  color: #fff;
  text-align: center;
  padding: 5px 0;
  border-radius: 6px;
  position: absolute;
  z-index: 1;
}

.an-pf-tooltip:hover .an-pf-tooltiptext {
  visibility: visible;
}

.anpf-field-counter-text {
  display: none;
}
.an-pf-field-type-text-wrap,
.an-pf-field-type-textarea-wrap {
  position: relative;
}
.an-pf-field-type-text-wrap .anpf-field-counter-max_length {
  margin-bottom: 0;
  position: absolute;
  top: 2px;
  bottom: 2px;
  right: 2px;
  font-size: 12px;
  line-height: 18px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 7px 0 5px;
}
.an-pf-field-type-textarea-wrap .anpf-field-counter-max_length {
  margin-bottom: 0;
  position: absolute;
  bottom: 2px;
  right: 2px;
  font-size: 12px;
  line-height: 18px;
  background: #fff;
  padding: 4px 6px 3px 5px;
}
.an-pf-fields-title {
  margin-bottom: 2rem;
}
.an-pf-fieldminiature_right {
  margin-left: 10px;
}
.an-pf-field-image-wrap {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  margin-right: 10px;
}
.an-pf-field-image {
  max-width: 30px;
  max-height: 30px;
}
.anfield-checkbox,
.anfield-radio {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.anfield-checkbox:last-child,
.anfield-radio:last-child {
  margin-bottom: 0;
}
.anfield-checkbox input,
.anfield-radio input {
  margin-right: 10px;
}
.anfield-checkbox label,
.anfield-radio label {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 0;
}
.an-pf-field-type-number .form-control,
.an-pf-field-type-text .form-control {
  margin-bottom: 10px;
}
.an-pf-field-descr {
  color: #7a7a7a;
  margin-top: 10px;
}

.an-pf-fields-total {
  font-weight: bold;
  padding-top: 20px;
  margin-bottom: 20px;
  border-top: 1px solid #dcdcdc;
}

.an-pf-field .control-label {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  font-weight: bold;
  margin-bottom: 10px;
}

.an-pf-fields-cart-line .value,
.an-pf-fields-cart-line .label {
  font-size: 12px;
}

.an-pf-fields-wrap {
  padding: 10px 0px;
}
 .an-pf-field {
   padding: 15px 0px;
}
.an-pf-field select {
  background-color: #fff;
}
.an-pf-field-type-date input,
.an-pf-field-type-textarea textarea,
.an-pf-field-type-text input,
.an-pf-field-type-number input {
  background: #fff;
}

.an-pf-field-red {
  color: red;
}

.elementor-section-wrap .an-pf-fields-wrap {
  padding: 10px;
}
.elementor-section-wrap .elementor-element .an-pf-fields-wrap {
  padding: 0;
  width: 100%;
}
.an-pf-hide {
  margin: 0!important;
  padding: 0!important;
}



.anfield-hidden-input {
  display: none;
}
.anfield-slider-block {
  display: flex;
  flex-direction: column;
}
.anfield-slider-head {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  order: 2;
}
.anfield-slider {
  width: calc(100% - 20px);
  margin: 8px auto;
  cursor: pointer;
  border: 0;
  background: transparent;
  height: 8px;
  position: relative;
}
.anfield-slider:before {
  display: block;
  content: "";
  position: absolute;
  top: 2px;
  right: -10px;
  bottom: 2px;
  left: -10px;
  background: #c5c5c5;
  border-radius: 4px;
  height: 4px;
}
.anfield-slider.ui-state-default,
.anfield-slider.ui-widget-content .ui-state-default {
  cursor: pointer;
  width: 20px;
  height: 20px;
  background: #c5c5c5;
  border-radius: 50%;
  border: 0!important;
  top: -6px;
  outline: none!important;
}
#ui-datepicker-div {
    z-index: 3!important;
}
.anfield-json {
  display: none;
}

.anfield-video {
  max-width: 100%;
  width: 100%;
  min-height: 255px;
}
.modal-default-image {
  display: none;
}

.youtube-video iframe {
  max-width: 100%;
}
.anpf-field-counter-max_length.hidden {
  display: none;
}

.anfield-slider-img {
  max-width: 100%;
}