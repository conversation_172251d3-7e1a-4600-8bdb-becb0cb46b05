/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/


#anSearchProducts-ajax-list { border: 1px solid #ccc; padding: 0;}
#anSearchProducts-ajax-list:empty { display: none; }

.anSearchProducts-product,
#anSearchProducts-ajax-list li {
  cursor: pointer;
  display: table;
  list-style: none;
  margin: 0;
  /* min-width: 300px; */
  width: 100%;
}
#anSearchProducts-ajax-list li {
  padding: 5px;
}

#anSearchProducts-ajax-list li:hover {
  background: #3ed2f0;
}

#anSearchProducts-ajax-list li img {
  width: 25px; display: inline-block; 
}
.anSearchProducts-ajax-img {
  width: 25px;
  height: 25px;
  margin-right: 7px;
}

.anSearchProducts-img {
  margin-right: 7px;
  width: 25px;
  height: 25px;
}
.anSearchProducts-list img {
  width: 25px; display: inline-block; 
}

.anSearchProducts-product .label,
#anSearchProducts-ajax-list li .label {
  color: #000;
  /* width: calc(100% - 50px); */
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  white-space: normal;
  text-align: left;
  vertical-align: middle;	
}


/*******************/
.anSearchProducts-wrap {
  padding: 0!important;
  max-width: 445px;
}


.anSearchProducts-product {
  padding: 5px 5px 5px 5px;
  border: 1px solid #bbcdd2;
}
.anSearchProducts-product span {
  line-height: 19px;
}
.anSearchProducts-product .material-icons {
  font-size: 17px;
}

.anSearchProducts-product .delete {
  margin-left: auto;
  padding-left: 5px;
}


.anSearchProducts-input-wrap {
  position: relative;
}
.anSearchProducts-input-wrap::after {
  display: inline-block;
  line-height: 1;
  content: "";
  top: 50%;
  right: 0.3125rem;
  margin-top: -0.6875rem;
  font-size: 1.375rem;
  font-family: "Material Icons", Arial, Verdana, Tahoma, sans-serif;
  font-style: normal;
  font-feature-settings: "liga";
  text-transform: none;
  letter-spacing: normal;
  overflow-wrap: normal;
  vertical-align: middle;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizelegibility;
  position: absolute;
  font-weight: 400;
  color: rgb(108, 134, 142);
  white-space: nowrap;
}
