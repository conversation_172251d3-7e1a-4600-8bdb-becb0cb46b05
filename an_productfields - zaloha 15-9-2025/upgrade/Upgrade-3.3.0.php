<?php
/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_3_3_0($object)
{
    $sql = [];

    $sql[] = '
    ALTER TABLE `' . _DB_PREFIX_ . 'an_productfields_fields`
      ADD `type_price` int(10) NOT NULL DEFAULT 0 AFTER `active`
    ';

    $sql[] = '
    ALTER TABLE `' . _DB_PREFIX_ . 'an_productfields_fields`
      ADD `price_percent` int(10) NOT NULL DEFAULT 0 AFTER `price`
    ';

    $sql[] = '
    ALTER TABLE `' . _DB_PREFIX_ . 'an_productfields_fields_values`
      ADD `type_price` int(10) NOT NULL DEFAULT 0 AFTER `id_field`
    ';

    $sql[] = '
    ALTER TABLE `' . _DB_PREFIX_ . 'an_productfields_fields_values`
      ADD `price_percent` int(10) NOT NULL DEFAULT 0 AFTER `price`
    ';


    $return = true;
    foreach ($sql as $_sql) {
        $return = Db::getInstance()->Execute($_sql);
		if (!$return){
			return false;
		}
    }
    
    return true;
}