<?php
/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_3_3_35($object)
{
    $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_files` (
        `id_file` int(10) unsigned NOT NULL AUTO_INCREMENT,
        `id_value` int(10) unsigned NOT NULL,
        `id_field` int(10) unsigned NOT NULL,    
        `file` varchar(50) NOT NULL,
        `external` varchar(50) NOT NULL,
        `label` varchar(150) NOT NULL,
        `position` int(10) NOT NULL,
        `main` tinyint(1) unsigned NOT NULL DEFAULT 0,
        `id_lang` int(10) unsigned NOT NULL,
        PRIMARY KEY(`id_file`)
    ) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';

    $return = true;
    foreach ($sql as $_sql) {
        $return = Db::getInstance()->Execute($_sql);
		if (!$return){
		//	return false;
		}
    }
    
    return true;
}