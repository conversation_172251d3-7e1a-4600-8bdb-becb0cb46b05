<?php
/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_3_3_25($object)
{
    $sql[] = 'ALTER TABLE `' . _DB_PREFIX_ . 'an_productfields_fields` ADD `file_display` varchar(25) NOT NULL DEFAULT "miniature_left" AFTER `position` ';
    $sql[] = 'ALTER TABLE `' . _DB_PREFIX_ . 'an_productfields_fields_lang` ADD `file_text_link` varchar(150) NOT NULL AFTER `tooltip` ';
    $sql[] = 'ALTER TABLE `' . _DB_PREFIX_ . 'an_productfields_fields_lang` ADD `file` varchar(50) NOT NULL AFTER `tooltip` ';

    $return = true;
    foreach ($sql as $_sql) {
        $return = Db::getInstance()->Execute($_sql);
		if (!$return){
		//	return false;
		}
    }
    
    return true;
}