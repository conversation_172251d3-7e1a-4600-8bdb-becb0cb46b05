<?php
/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_3_2_4($object)
{
    $_tab_id = Tab::getIdFromClassName('AdminAnproductfieldsFields');
    $_tab = new Tab($_tab_id);
    $_tab->id_parent = Tab::getIdFromClassName('AdminCatalog');
    $_tab->active = 1;
    $_tab->save();

    $_tab_id = Tab::getIdFromClassName('AdminAnproductfieldsSettings');
    $_tab = new Tab($_tab_id);
    $_tab->id_parent = Tab::getIdFromClassName('AdminCatalog');
    $_tab->save();
    
    return true;
}