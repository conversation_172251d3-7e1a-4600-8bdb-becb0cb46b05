<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_3_3_98($object)
{
    $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_customer_groups` (
        `id_group` int(10) NOT NULL,
        `id_data` int(10) NOT NULL,
        `type` int(10) NOT NULL DEFAULT 0
    ) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';

    $return = true;
    foreach ($sql as $_sql) {
        $return = Db::getInstance()->Execute($_sql);
		if (!$return){
		//	return false;
		}
    }
    
    return true;
}