<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_3_3_69($object)
{
    $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_fields_products` (
        `id_fp` int(11) NOT NULL AUTO_INCREMENT,
        `id_field` int(11) NOT NULL,
        `id_product` int(11) NOT NULL,
        `active` tinyint(1) unsigned NOT NULL DEFAULT 0,
      PRIMARY KEY (`id_fp`)
    ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

    $sql[] = 'ALTER TABLE `' . _DB_PREFIX_ . 'an_productfields_fields` ADD `not_global` tinyint(1) unsigned NOT NULL DEFAULT 0   AFTER `relation` ';


    $return = true;
    foreach ($sql as $_sql) {
        $return = Db::getInstance()->Execute($_sql);
    }

    $object->registerHook('displayBackOfficeHeader');
    $object->registerHook('displayAdminProductsExtra');

    return true;
}