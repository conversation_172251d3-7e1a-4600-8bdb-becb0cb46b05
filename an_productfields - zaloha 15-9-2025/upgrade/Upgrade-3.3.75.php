<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_3_3_75($object)
{
    $object->tabAdd(
        [  
            'class_name' => 'AdminPfCustomcssjs',
            'parent' => 'AdminParentModulesSf',
            'name' => 'CSS / JS',
            'active' => 0
        ], $object);

    return true;
}