<?php
/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_3_3_14($object)
{

    $object->registerHook('displayAdminOrderMain');
    Configuration::updateValue('an_pf_savePfOrder', 1);

    $sql = [];

    $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_order` (
        `id_pf_order` int(10) unsigned NOT NULL AUTO_INCREMENT,
        `id_field` int(10) NOT NULL,
        `id_cart` int(10) NOT NULL,
        `id_order` int(10) NOT NULL,
        `id_product` int(10) NOT NULL,
        `id_product_attribute` int(10) NOT NULL,
        `id_customization` int(10) NOT NULL,
        `qty` int(10) NOT NULL DEFAULT 1,
        `price` decimal(12,2) NOT NULL,
        `price_wt` decimal(12,2) NOT NULL,
        `date_add` datetime NOT NULL,
        PRIMARY KEY(`id_pf_order`)
    ) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';

    $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_order_lang` (
        `id_pf_order` int(10) unsigned NOT NULL,
        `title` varchar(255) NOT NULL,
        `id_lang` varchar(255) NOT NULL,
        PRIMARY KEY(`id_pf_order`, `id_lang`)
    ) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';

    $return = true;
    foreach ($sql as $_sql) {
        $return = Db::getInstance()->Execute($_sql);
		if (!$return){
			return false;
		}
    }
    
    return true;
}