<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_3_3_76($object)
{
    $sql[] = "ALTER TABLE `" . _DB_PREFIX_ . "an_productfields_fields_relations` ADD `id_product_attribute` INT NOT NULL DEFAULT '0' AFTER `id_type`;";

    $return = true;
    foreach ($sql as $_sql) {
        $return = Db::getInstance()->Execute($_sql);
    }

    return true;
}