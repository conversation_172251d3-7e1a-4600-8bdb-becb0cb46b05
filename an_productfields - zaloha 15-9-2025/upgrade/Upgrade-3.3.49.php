<?php
/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_3_3_49($object)
{
    $object->tabAdd(
        [  
            'class_name' => 'AdminAnproductfieldsGroups',
            'parent' => 'AdminParentModulesSf',
            'name' => 'Anvanto Product Fields: Groups',
            'active' => 0    
        ], $object);

        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_groups` (
            `id_group` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `active` tinyint(1) unsigned NOT NULL DEFAULT 1,
            `relation` int(10) NOT NULL,
            `position` int(10) NOT NULL,
            PRIMARY KEY(`id_group`)
        ) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';
        
        $sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'an_productfields_groups_shop` (
            `id_group` int(10) unsigned NOT NULL,
            `id_shop` int(10) unsigned NOT NULL,
            PRIMARY KEY (`id_group`, `id_shop`)
        ) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8;';
        
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_groups_lang` (
            `id_group` int(10) unsigned NOT NULL,
            `title_group` varchar(255) NOT NULL,
            `id_lang` int(10) unsigned NOT NULL,
            PRIMARY KEY(`id_group`, `id_lang`)
        ) ENGINE = ' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET = utf8';
        
        
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_groups_relations` (
            `id_relation` int(11) NOT NULL AUTO_INCREMENT,
            `type` tinyint(1) unsigned NOT NULL DEFAULT 0,
            `id_group` int(11) NOT NULL,
            `id_type` int(11) NOT NULL,
          PRIMARY KEY (`id_relation`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';
    
        $return = true;
        foreach ($sql as $_sql) {
            $return = Db::getInstance()->Execute($_sql);
        }

    return true;
}