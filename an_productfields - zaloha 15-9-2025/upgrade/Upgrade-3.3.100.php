<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_3_3_100($object)
{
    $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_dependencies` (
        `id_dependence` int(11) NOT NULL AUTO_INCREMENT,
        `id_field_dependence` int(11) NOT NULL,
        `id_value` int(11) NOT NULL,
      PRIMARY KEY (`id_dependence`)
    ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';
    
    $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'an_productfields_dependencies_fields` (
        `id_dpf` int(11) NOT NULL AUTO_INCREMENT,
        `id_dependence` int(11) NOT NULL,
        `id_field` int(11) NOT NULL,
      PRIMARY KEY (`id_dpf`)
    ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

    $return = true;
    foreach ($sql as $_sql) {
        $return = Db::getInstance()->Execute($_sql);
		if (!$return){
		//	return false;
		}
    }
    
    return true;
}