<?php
/**
 * Creative Elements - live Theme & Page Builder
 *
 * <AUTHOR>
 * @copyright 2019-2024 WebshopWorks.com
 * @license   One domain support license
 */
if (!defined('_PS_VERSION_')) {
    exit;
}

class CreativeElementsAjaxModuleFrontControllerOverride extends CreativeElementsAjaxModuleFrontController
{



    public function ajaxProcessAddToCartModal()
    {
        $cart = $this->cart_presenter->present($this->context->cart, true);
        $product = null;
        $id_product = (int) Tools::getValue('id_product');
        $id_product_attribute = (int) Tools::getValue('id_product_attribute');
        $id_customization = (int) Tools::getValue('id_customization');

        foreach ($cart['products'] as &$p) {
            if ($id_product === (int) $p['id_product'] && $id_product_attribute === (int) $p['id_product_attribute'] && $id_customization === (int) $p['id_customization']) {
                $product = $p;
    
            }
        }

        $this->context->smarty->assign([
            'configuration' => $this->getTemplateVarConfiguration(),
            'product' => $product,
            'cart' => $cart,
            'cart_url' => $this->context->link->getPageLink('cart', null, $this->context->language->id, [
                'action' => 'show',
            ], false, null, true),
        ]);

        $this->ajaxDie([
            'modal' => $this->context->smarty->fetch('module:ps_shoppingcart/modal.tpl'),
        ]);
    }

}
