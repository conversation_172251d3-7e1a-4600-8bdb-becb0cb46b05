<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

class OrderDetail extends OrderDetailCore
{
    protected function create(Order $order, Cart $cart, $product, $id_order_state, $id_order_invoice, $use_taxes = true, $id_warehouse = 0)
    {
        parent::create($order, $cart, $product, $id_order_state, $id_order_invoice, $use_taxes, $id_warehouse);
        if (Module::isEnabled('an_productfields')){
            Module::getInstanceByName('an_productfields')->saveOrderDetail(
                $cart,
                $this->id,
                $this->id_order,
                $product
            );
        }
    }
}
?>