<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}
class Cart extends CartCore
{
    public $_products;
    
    public function getTotalCalculationCartRules($type, $withShipping)
    {
        return parent::getTotalCalculationCartRules($type, $withShipping);
    }

    public function getOrderTotal(
        $withTaxes = true,
        $type = Cart::BOTH,
        $products = null,
        $id_carrier = null,
        $use_cache = false,
        bool $keepOrderPrices = false
    )
    {
        if (!$this->_products){
            $this->_products = $this->getProducts();
        }
        if (version_compare(_PS_VERSION_, '*******', '>')){
            $total = Module::getInstanceByName('an_productfields')->getOrderTotalWithFields(
                $this, $withTaxes, $type, $products, $id_carrier, $use_cache, $keepOrderPrices
            );
        }
        if (!version_compare(_PS_VERSION_, '*******', '>') || $total == 0){
            $total = parent::getOrderTotal($withTaxes, $type, $products, $id_carrier, $use_cache, $keepOrderPrices);
            if(!$keepOrderPrices && ($type== Cart::BOTH  || $type==Cart::ONLY_PRODUCTS) && Module::isEnabled('an_productfields')){
                $total += Module::getInstanceByName('an_productfields')->getFieldsOrderTotal($this->_products, $withTaxes);
            }
        }
        return $total;
    }

    public function getProducts($refresh = false, $id_product = false, $id_country = null, $fullInfos = true, bool $keepOrderPrices = false, $original = false)
    {
        $this->_products = parent::getProducts($refresh, $id_product, $id_country, $fullInfos, $keepOrderPrices);
        if(!$keepOrderPrices && Module::isEnabled('an_productfields') && !$original){
            $this->_products = Module::getInstanceByName('an_productfields')->prepareProductsFields($this, $this->_products);
        }
        return $this->_products;
    }

    public function duplicate()
    {
        $parent = parent::duplicate();
        if ($parent['success']) {
            Module::getInstanceByName('an_productfields')->duplicateCartGroupsValues($this->id, $parent['cart']->id);            
        }
        return $parent;
    }
}