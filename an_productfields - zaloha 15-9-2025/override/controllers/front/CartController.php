<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

class CartController extends CartControllerCore
{
    public function processChangeProductInCart()
    {
        if (Module::isEnabled('an_productfields')){
            Module::getInstanceByName('an_productfields')->validateBeforeAddToCartFields($this->errors,$this->id_product);
        }

        if (Module::isEnabled('an_productfields') && !$this->errors){
            if (Module::getInstanceByName('an_productfields')->checkOpDown(
                $this->id_product, 
                $this->id_product_attribute, 
                $this->customization_id
            )){
                return;
            }
        }

        parent::processChangeProductInCart();
        
        if (Module::isEnabled('an_productfields') && !$this->errors){
            Module::getInstanceByName('an_productfields')->addToCartFields(
                $this->id_product, 
                $this->id_product_attribute, 
                $this->qty, 
                $this->customization_id
            );
         }
    }

    public function productInCartMatchesCriteria($productInCart)
    {
        return (
            !isset($this->id_product_attribute) ||
            (
                $productInCart['id_product_attribute'] == $this->id_product_attribute &&
                $productInCart['id_customization'] == $this->customization_id
            )
        ) && 
            isset($this->id_product) && 
            $productInCart['id_product'] == $this->id_product && 
            (
                (Tools::getValue('an_group_id') && isset($productInCart['an_group_id']) && 
                $productInCart['an_group_id'] == (int)Tools::getValue('an_group_id')) ||
                !Tools::getValue('an_group_id') && !isset($productInCart['an_group_id'])
            );
    }

    public function displayAjaxRefresh()
    {
        if (Configuration::isCatalogMode()){
            return;
        }
        ob_end_clean();
        header('Content-Type: application/json');
        $this->ajaxRender(json_encode([
            'cart_detailed' =>$this->context->smarty->fetch(_PS_MODULE_DIR_.'an_productfields/views/templates/hook/checkout/cart-detailed.tpl'),
            'cart_detailed_totals' => $this->render('checkout/_partials/cart-detailed-totals'),
            'cart_summary_items_subtotal' => $this->render('checkout/_partials/cart-summary-items-subtotal'),
            'cart_summary_subtotals_container' => $this->render('checkout/_partials/cart-summary-subtotals'),
            'cart_summary_totals' => $this->render('checkout/_partials/cart-summary-totals'),
            'cart_detailed_actions' => $this->render('checkout/_partials/cart-detailed-actions'),
            'cart_voucher' => $this->render('checkout/_partials/cart-voucher'),
        ]));
    }
}