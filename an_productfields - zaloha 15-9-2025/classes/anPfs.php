<?php
/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

class anPfs
{
    protected static $mathParser = null;

    public static function getPriceDisplayMethod()
    {
        $context = Context::getContext();

        if (isset($context->customer->id_default_group)){
            $priceDisplayMethod = Group::getPriceDisplayMethod($context->customer->id_default_group);
        } else {
            $priceDisplayMethod = Group::getDefaultPriceDisplayMethod();
        }

        if ($priceDisplayMethod || !Configuration::get('an_pf_use_tax')){
            return 0;
        }

        return 1;
    }

    public static function formatPrice($price, $price_wt)
    {
        if (Configuration::get(an_productfields::PREFIX . 'hide_field_prices')){
            return '';
        }

        $context = Context::getContext();

        if (isset($context->customer->id_default_group)){
            $priceDisplayMethod = Group::getPriceDisplayMethod($context->customer->id_default_group);
        } else {
            $priceDisplayMethod = Group::getDefaultPriceDisplayMethod();
        }

        if ($priceDisplayMethod || !Configuration::get('an_pf_use_tax')){
            $priceFormatted = self::displayPrice($price, $context);
        } else {
            $priceFormatted = self::displayPrice($price_wt, $context);
        }
        
        return $priceFormatted;
    }

    public static function displayPrice($price, $context)
    {
        if (version_compare(_PS_VERSION_, '1.7.7.0', '>')){
            return Tools::getContextLocale($context)->formatPrice($price, $context->currency->iso_code);
        } else {
            return Tools::displayPrice($price);
        }
    }

    public static function applyReductionCustomerGroup($price, $id_product = false)
    {
        if (!Configuration::get('an_pf_applyReductionCustomerGroup')){
            return $price;
        }

        $reductionGroup = anPfs::getReductionCustomerGroup($id_product);

        if ($reductionGroup > 0 && $price > 0) {
            $price = $price - ($price / 100 * $reductionGroup);
        }

        return $price;
    }

    public static function getReductionCustomerGroup($id_product)
    {
        $context = Context::getContext();
        
        if (!$id_product || !isset($context->customer->id_default_group)){
            return 0;
        }

        $reductionGroup = GroupReduction::getValueForProduct($id_product, $context->customer->id_default_group);

        if ($reductionGroup){
            return $reductionGroup * 100;
        }

        return Group::getReduction($context->customer->id);
    }




    public static function formulaValidate($formula)
    {
        $formulaVariables = ['quantity', 'product'];   

        $formula = self::formulaPrepar($formula);
        
        if (is_numeric($formula)) {
            return (float)$formula;
        }
        
        foreach ($formulaVariables as $name) {
            self::getMathParser()->registerVariable($name, 1);
        }
        
        try {
            return self::getMathParser()->evaluate($formula);
        } catch (Exception $ex) {
            return $ex->getMessage();
        }
    }

    public static function getMathParser()
    {
        if (is_null(self::$mathParser)) {
            self::$mathParser = new \PHPMathParser\Math();
        }
 
        return self::$mathParser;
    }

    public static function formulaPrepar($formula)
    {
        $formula = Tools::strtolower($formula);
        $formula = str_replace(['[', ']'], [''], (string) $formula);    
    //    $formula = str_replace(['ceil', 'round', 'floor'], ['CEIL', 'ROUND', 'FLOOR'], (string) $formula);
        return $formula;
    }
}