<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

class anPfCustomCssJss 
{
    const pathCss = _PS_MODULE_DIR_ . 'an_productfields/views/css/';
    const pathJs = _PS_MODULE_DIR_ . 'an_productfields/views/js/';
    const pathFile = _PS_MODULE_DIR_ . 'an_productfields/views/';



    public static function deleteFile($pathFile)
    {   
        if(Tools::file_exists_no_cache($pathFile)){
            @unlink($pathFile);
        }
    }

    public static function generateFileName($format, $prefix = 'anfields')
    {
        return $prefix . '-' . md5(uniqid() . context::getContext()->shop->id) . '.' . $format;
    }

    public static function updateFile($code, $format)
    {
        if ($format != 'js' && $format != 'css'){
            return false;
        }

        $path = self::pathFile . $format . '/';
        $keyFileName = 'an_pf_code_' . $format . '_custom';
        $fileName = Configuration::get($keyFileName);

        anPfCustomCssJss::deleteFile($path . $fileName);

        if ($code == ''){
            Configuration::updateValue($keyFileName, false);
            return true;
        }

        $newFileName = anPfCustomCssJss::generateFileName($format);
        Configuration::updateValue($keyFileName, $newFileName);
        @file_put_contents($path . $newFileName, $code);

        return true;
    }


    public static function getFileContent($fileName)
    {
        $format = substr($fileName, strrpos($fileName, '.') + 1);
        $format = Tools::strtolower($format);
        
        if ($format != 'js' && $format != 'css'){
            return false;
        }

        $path = self::pathFile . $format . '/';

        return Tools::file_get_contents($path . $fileName);
    }


    
}