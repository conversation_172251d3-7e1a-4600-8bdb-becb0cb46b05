<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}



class anProductFieldsGroups extends ObjectModel
{
    /**
     * @var int
     */
    public $id_group;
    /**
     * @var int
     */
    public $id;
    /**
     * @var int
     */
    public $active = 1;
    
    public $relation = 0;
    public $position = 0;
    
    public $title_group;

    /**
     * @var array
     */
    public static $definition = [
        'table' => 'an_productfields_groups',
        'primary' => 'id_group',
        'multilang' => true,
        'fields' => [

            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'relation' => ['type' =>self::TYPE_INT ],     
            'position' => ['type' =>self::TYPE_INT ],
            'title_group' => ['type' =>self::TYPE_STRING,'lang' => true ],
        ],
    ];

    /**
     * Formula constructor.
     *
     * @param null $id
     */

    public function __construct($id = null, $id_lang = null, $short = false)
    {
        parent::__construct($id, $id_lang);
    }

    public function saveRelation()
    {
        switch (Tools::getValue('relation')){
                
            case 1:
                $this->updateCategoriesProducts(Tools::getValue('id_categories'), $this->id, 1);
                break;
                
            case 2:
                $this->updateCategoriesProducts(Tools::getValue('productIds'), $this->id, 2);
                break;    

            default:
                $this->updateCategoriesProducts([], $this->id, 0);
        }
    }

    public function updateCategoriesProducts($ids = [], $id_group = 0, $type = 0)
    {
        if (!$id_group){
            return false;
        }        
        
        Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute('DELETE FROM `'._DB_PREFIX_.'an_productfields_groups_relations` WHERE `id_group`='.(int) $id_group.' ');
        
        if (!$ids || count($ids) == 0) {
            $ids[] = 0;
        }
    
        $ids = array_unique($ids);
    
        foreach ($ids as $id) {                        
            $sql = 'INSERT INTO `'._DB_PREFIX_.'an_productfields_groups_relations`  (`id_group`, `id_type`, `type`) 
            VALUES ("'.(int) $id_group
            .'", "'.(int) $id.'", "'.(int) $type.'" )';
            Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute($sql);
        }
        
        return true;
    }   

    public static function getGroupsByIdProduct($id_product, $cats = [])
    {
        $context = Context::getContext();
        
        if (count($cats) < 1){
            if (!Configuration::get('an_pf_onlyCategoryDefault')){
                $cats = Product::getProductCategories($id_product);
            } else {
                $product = new Product($id_product);
                $cats[] = $product->id_category_default;
            }
        }

        $sql = '
        SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_groups_relations` szwr, `' . _DB_PREFIX_ . 'an_productfields_groups` sw
        LEFT JOIN `' . _DB_PREFIX_ . 'an_productfields_groups_lang` sl 
            ON (sw.`id_group` = sl.`id_group`
            AND sl.`id_lang` = ' . (int) $context->language->id . ')
        WHERE sw.`active`=1
            AND sw.`id_group` = szwr.`id_group` AND sw.`relation` = szwr.`type`
            AND (';

            if (count($cats) > 0){
                $sql .= ' (szwr.`type` = 1 AND szwr.`id_type` IN (' . implode(', ', $cats) . ')) OR ';
            }

            $sql .= '(szwr.`type` = 2 AND szwr.`id_type` = '.(int) $id_product.') OR (szwr.`type` = 0 AND szwr.`id_type` = 0)';

            $sql .= '
            )
        ';   
        
        if (Shop::isFeatureActive()) {
            $sql .= ' AND sw.`id_group` IN (
                SELECT sa.`id_group`
                FROM `' . _DB_PREFIX_ . 'an_productfields_groups_shop` sa
                WHERE sa.id_shop IN (' . implode(', ', Shop::getContextListShopID()) . ')
            )';
        }
       
        $sql .= ' GROUP BY sw.`id_group`';
        $sql .= ' ORDER BY sw.`position`';
       
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        
        if (!$result){
            return [];
        }

        return $result;
    }


    public static function getProducsByIdGroup($id_group = 0)
    {
        if (!$id_group){
            return [];
        }
        
        $sql = '
        SELECT  *, p.*
        FROM `' . _DB_PREFIX_ . 'an_productfields_groups_relations` awl
        
        LEFT JOIN `' . _DB_PREFIX_ . 'product` p
            ON (p.`id_product` = awl.`id_type`)
        
        LEFT JOIN `' . _DB_PREFIX_ . 'product_lang` pl
            ON (p.`id_product` = pl.`id_product`
            AND pl.`id_lang` = ' . (int) Context::getContext()->language->id . Shop::addSqlRestrictionOnLang('pl') . ')        
        
        WHERE awl.`id_group` = ' . (int) $id_group . '  AND awl.`type`="2" ';
        
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql, true, false);

		$products = Product::getProductsProperties(Context::getContext()->language->id, $result);

        foreach ($products as &$product){

            $coverPhoto = '';
            if (isset($product['cover_image_id'])){
                $coverPhoto = Context::getContext()->link->getImageLink(
                    $product['link_rewrite'],
                    $product['cover_image_id'], 
                    ImageType::getFormattedName('cart')
                );
            }
            
            $product['cover'] = $coverPhoto;
        }

        return $products;
    } 

    public static function getRelationCategories($id_group = 0)
    {
        if (!$id_group){
            return [];
        }
        
        $sql = '
        SELECT `id_type`
        FROM `' . _DB_PREFIX_ . 'an_productfields_groups_relations` awl        
        WHERE awl.`id_group` = ' . (int) $id_group . ' AND awl.`type`="1"  ';
        
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql, true, false);
        
        $cats = [];
        if ($result) {
            foreach ($result as $item){
                $cats[] = $item['id_type'];
            }
        }
        
        return $cats;        
    }  

    public static function getGroups()
    {
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_groups` aut
        LEFT JOIN `' . _DB_PREFIX_ . 'an_productfields_groups_lang` autl
        ON (autl.`id_group` = aut.`id_group` 
        AND autl.`id_lang` = '.(int) Context::getContext()->language->id.' )
        WHERE aut.active = 1'; 

        $sql .= ' ORDER BY aut.`position`';
        
        if (Shop::isFeatureActive()) {
			$sql .= ' AND aut.`id_group` IN (
				SELECT uts.`id_group`
				FROM `' . _DB_PREFIX_ . 'an_productfields_groups_shop` uts
				WHERE uts.id_shop IN (' . implode(', ', Shop::getContextListShopID()) . ')
			)';
		}

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        return $result;
    }

    public function delete()
    {        
        Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute('DELETE FROM `'._DB_PREFIX_.'an_productfields_groups_relations` WHERE `id_group`='.(int) $this->id.' ');        
        return parent::delete();
    }	
}