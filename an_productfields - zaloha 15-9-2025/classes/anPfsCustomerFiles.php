<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

class anPfsCustomerFiles 
{
    const imgDir = _PS_MODULE_DIR_ . 'an_productfields/img/';
    const imgUrl = __PS_BASE_URI__ . 'modules/an_productfields/img/';
    const moduleUrl = __PS_BASE_URI__ . 'modules/an_productfields/';

    public static $typesFiles = [
        'gif' => 'img', 
        'jpg' => 'img', 
        'jpeg' => 'img', 
        'jpe' => 'img', 
        'png' => 'img', 
        'webp' => 'img', 
        'svg' => 'img', 
        'pdf' => false,
        'csv' => false,
        'xlsx' => false,
        'docx' => false
    ];

    public static function uploadFiles(&$an_pf_name)
    {
        if (isset($_FILES['anpfFileUpload']) && isset($_FILES['anpfFileUpload']['tmp_name']) && !empty($_FILES['anpfFileUpload']['tmp_name'])){
            
            $anpfFiles = $_FILES['anpfFileUpload'];
            
            foreach ($anpfFiles['tmp_name'] as $key => $file){
                $fileName = anPfsCustomerFiles::generateFileName($anpfFiles['name'][$key]);
                $an_pf_name[$key] = $fileName;
                if (move_uploaded_file($file, anPfsCustomerFiles::imgDir . $fileName)) {

                }
            }
        }
    }

    public static function generateFileName($name)
    {   
        $ext = substr($name, strrpos($name, '.') + 1);
        $ext = Tools::strtolower($ext);
        return substr(md5(uniqid()), 0, 7) . '.'.$ext;
    }



    
}