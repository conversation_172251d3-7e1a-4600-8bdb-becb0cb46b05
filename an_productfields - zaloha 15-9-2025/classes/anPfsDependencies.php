<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

class anPfsDependencies extends ObjectModel
{
    public $id_dependence;
    public $id;
    
    public $id_field_dependence = 0;
    public $id_value = 0;

    public static $definition = [
        'table' => 'an_productfields_dependencies',
        'primary' => 'id_dependence',
        'multilang' => false,
        'fields' => [
            'id_field_dependence' => ['type' =>self::TYPE_INT ],
            'id_value' => ['type' =>self::TYPE_INT ]
        ],
    ];
    
    public const dependenciesJson = _PS_MODULE_DIR_ . 'an_productfields/dependencies.json';

    public function __construct($id = null, $id_lang = null, $short = false)
    {
        parent::__construct($id, $id_lang);
    }

    public static function getPfsFields()
    {        
        $context = Context::getContext();

        $sql = 'SELECT * FROM ';

        $sql .= '`' . _DB_PREFIX_ . 'an_productfields_fields` sw
            LEFT JOIN `' . _DB_PREFIX_ . 'an_productfields_fields_lang` sl 
                ON (sw.`id_field` = sl.`id_field`
                AND sl.`id_lang` = ' . (int) $context->language->id . ')
        ';
        //  $sql .= '    WHERE sw.`active`=1';
            
        if (Shop::isFeatureActive()) {
            $sql .= ' AND sw.`id_field` IN (
                SELECT sa.`id_field`
                FROM `' . _DB_PREFIX_ . 'an_productfields_fields_shop` sa
                WHERE sa.id_shop IN (' . implode(', ', Shop::getContextListShopID()) . ')
            )';
        }
    
        $sql .= ' GROUP BY sw.`id_field`';
        $sql .= ' ORDER BY sw.`position`';

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    public function getPfsFieldsForForm()
    {
        $fieldUse = [];
        $sql = 'SELECT * FROM  `' . _DB_PREFIX_ . 'an_productfields_dependencies_fields` WHERE `id_dependence`<> ' . (int) $this->id. '  ';                
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        foreach ($result as $item){
            $fieldUse[$item['id_field']] = $item['id_field'];
        }

        $onlyFields = self::getPfsFields();
        $formFieldsCheckbox = [];
        foreach ($onlyFields as $id => $field){
            if (!isset($fieldUse[$field['id_field']])){
                $formFieldsCheckbox[] = [
                    'id' => $field['id_field'],
                    'name' => $field['title']
                ];
            }

        }

        return $formFieldsCheckbox;
    }

    public static function getPfsFieldsForSearch()
    {
        $onlyFields = self::getPfsFields();
        $formFieldsCheckbox = [];
        foreach ($onlyFields as $id => $field){
            $formFieldsCheckbox[$field['id_field']] = $field['title'];
        }

        return $formFieldsCheckbox;
    }

    public static function getFieldsValues()
    {        
        $context = Context::getContext();

        $onlyFields = self::getPfsFields();

        $fields = [];
        foreach ($onlyFields as $id => $field){

            $field['isset_values'] = anProductFields::$typesFields[$field['type']]['values'];

            if ($field['isset_values'] ){

                $sql = 'SELECT * FROM ';
                $sql .= ' `' . _DB_PREFIX_ . 'an_productfields_fields_values` sw 
                LEFT JOIN `' . _DB_PREFIX_ . 'an_productfields_fields_values_lang` sl 
                    ON (sw.`id_value` = sl.`id_value`
                    AND sl.`id_lang` = ' . (int) $context->language->id . ')
                WHERE sw.`active`=1 ';                
                $sql .= ' AND `id_field` =  '.(int) $field['id_field'].' ';
        
                if (Shop::isFeatureActive()) {
                    $sql .= ' AND sw.`id_value` IN (
                        SELECT sa.`id_value`
                        FROM `' . _DB_PREFIX_ . 'an_productfields_fields_values_shop` sa
                        WHERE sa.id_shop IN (' . implode(', ', Shop::getContextListShopID()) . ')
                    )';
                }
        
                $sql .= ' ORDER BY sw.`position`';
        
                $field['values'] = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
                $fields[$field['id_field']] = $field;
            }
        }

        return $fields;
    }

    public static function getValuesFields()
    {        
        $sql = 'SELECT * FROM  `' . _DB_PREFIX_ . 'an_productfields_fields_values`';
        $values = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        $valuesField = [];
        foreach ($values as $value){
            $valuesField[$value['id_value']] = $value['id_field'];
        }

        return $valuesField;
    }
    
    public function getListFieldsValues()
    {   
        $valuesUse = [];
        $sql = 'SELECT * FROM  `' . _DB_PREFIX_ . 'an_productfields_dependencies`  WHERE `id_dependence` <> ' . (int) $this->id . '   ';                
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        foreach ($result as $item){
            $valuesUse[$item['id_value']] = $item['id_value'];
        }
        
        $fieldValues = self::getFieldsValues();

        foreach ($fieldValues as $fv){

            $options = [];
            foreach ($fv['values'] as $value){
                if (!isset($valuesUse[$value['id_value']])){
                    $options[] = [
                        'id' => $value['id_value'],
                        'name' => $value['title']
                    ];
                }
            }
            
            $forSelect[] = [
                'label'=> $fv['title'],
                'options' => $options
            ];
        }

        return $forSelect;
    }

    public function getDependenciesFieldsById($id_dependence = false)
    {
        if (!$id_dependence){
            $id_dependence = $this->id;
        }

        $sql = 'SELECT * FROM  `' . _DB_PREFIX_ . 'an_productfields_dependencies_fields` WHERE `id_dependence`=' . (int) $id_dependence . ' ';                
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        $dependenciesFields = [];
        if (is_array($result)){
            foreach ($result as $dependence){
                $dependenciesFields[$dependence['id_field']] = $dependence['id_field'];
            }
        }

        return $dependenciesFields;
    }

    public function saveDependenciesFields()
    {
        Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute('
            DELETE FROM `'._DB_PREFIX_.'an_productfields_dependencies_fields` 
            WHERE `id_dependence`=' . (int) $this->id . ' 
        ');

        $ids = Tools::getValue('dependencies_fields');

        if (!$ids || count($ids) == 0) {
            $ids[] = 0;
        }
    
        $ids = array_unique($ids);

        foreach ($ids as $id) {               
            $sql = 'INSERT INTO `'._DB_PREFIX_.'an_productfields_dependencies_fields`  (`id_dependence`, `id_field`) 
            VALUES ("'.(int) $this->id .'", "'.(int) $id.'" )';
            Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute($sql);
        
        }

    //    self::generateJson();
    }

    public function update($null_values = false)
    {        
        $result = parent::update($null_values = false);
        $this->saveDependenciesFields();
        return $result;

    }	

    public function add($null_values = false, $auto_date = true)
    {        
        $sql = 'SELECT `id_field` FROM  `' . _DB_PREFIX_ . 'an_productfields_fields_values` WHERE `id_value` = '.(int)$this->id_value.'  ';                
        $id_field = Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);

        $this->id_field_dependence = $id_field;
        
        $add = parent::add($null_values = false, $auto_date = true);
        $this->saveDependenciesFields();
        return $add;
    }	

    public function delete()
    {        
        Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute('
            DELETE FROM `'._DB_PREFIX_.'an_productfields_dependencies_fields` 
            WHERE `id_dependence`=' . (int) $this->id . ' 
        ');        

    //    self::generateJson();

        return parent::delete();
    }	

    public static function deleteAllByIdField($idField)
    {
        $sql = 'SELECT * FROM  `' . _DB_PREFIX_ . 'an_productfields_dependencies` WHERE `id_field_dependence` = ' . (int) $idField . ' ';                
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        foreach ($result as $dependence){
           $obj = new self($dependence['id_dependence']);
           $obj->delete();
        }

        Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute('DELETE FROM `'._DB_PREFIX_.'an_productfields_dependencies_fields` WHERE `id_field`='.(int) $idField.' ');
    }

    public static function deleteByIdValue($idValue)
    {
        $sql = 'SELECT * FROM  `' . _DB_PREFIX_ . 'an_productfields_dependencies` WHERE `id_value` = ' . (int) $idValue . ' ';                
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        foreach ($result as $dependence){
           $obj = new self($dependence['id_dependence']);
           $obj->delete();
        }
    }

    public static function getDependenciesFields()
    {
        $sql = 'SELECT * FROM  `' . _DB_PREFIX_ . 'an_productfields_dependencies_fields`  ';  
        $cacheId = 'anPfsDependencies ' . md5($sql);

        if (!Cache::isStored($cacheId)) {
            $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
            Cache::store($cacheId, $result);
        } else {
            $result = Cache::retrieve($cacheId);
        }

        return $result;
    }

    public static function getRelations()
    {
        $result = self::getDependenciesFields();

        $relation = [];
        foreach ($result as $item){
            $relation[$item['id_dependence']][] = $item['id_field'];
        }

        return $relation;
    }

    public static function getHidden()
    {
        $result = self::getDependenciesFields();      
        
        $hiddenFields = [];
        foreach ($result as $item){
            $hiddenFields[$item['id_field']] = $item['id_field'];
        }

        return $hiddenFields;
    }

    public static function getDependencies()
    {
        $sql = 'SELECT * FROM  `' . _DB_PREFIX_ . 'an_productfields_dependencies` ';                
        $cacheId = 'anPfsDependencies::getDependencies' . md5($sql);
        if (!Cache::isStored($cacheId)) {
            $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
            Cache::store($cacheId, $result);
        } else {
            $result = Cache::retrieve($cacheId);
        }            

        $relations = self::getRelations();

        $dependencies = [];
        foreach ($result as $value){
            $dependencies[$value['id_field_dependence']][$value['id_value']] =  $relations[$value['id_dependence']];
        }

        return $dependencies;
    }       

    public static function generateJson()
    {
        $hiddenDependencies['hidden'] = self::getHidden();
        $hiddenDependencies['dependencies'] = self::getDependencies();

        @file_put_contents(self::dependenciesJson, json_encode($hiddenDependencies(), JSON_PRETTY_PRINT));
    }

    //  For valid
    public static function getDependenciesDataReverse()
    {
        $sql = '
        SELECT * 
        FROM  `' . _DB_PREFIX_ . 'an_productfields_dependencies_fields` pfsdf, `' . _DB_PREFIX_ . 'an_productfields_dependencies` pfsd
        WHERE  pfsdf.`id_dependence` = pfsd.`id_dependence` ';                
        $values = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        $dependencies = [];
        foreach ($values as $value){
            //  dependent field and the field that, when selected, displays the dependency
            $dependencies[$value['id_field']]['id_field_dependence'] =  $value['id_field_dependence'];
            //  dependent field and value, when selected, the dependency is displayed
            $dependencies[$value['id_field']]['id_value'] =  $value['id_value'];
        }

        return $dependencies;
    } 
}