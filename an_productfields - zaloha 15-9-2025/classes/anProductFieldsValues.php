<?php
/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anProductFieldsTax.php';
require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anProductFieldsFiles.php';
require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anPfs.php';

class anProductFieldsValues extends ObjectModel
{

    public $id_value;

    public $id;

    public $id_field = 0;
    public $price = 0;
    public $type_price = 0;
    public $type_show_price;
    public $price_percent = 0;
    public $custom_tax = 0;
    public $apply_specific_price = 0;
    public $active = 1;
    public $position = 0;

    public $file_display = 'miniature_left';
    public $file;
    public $file_text_link;

    public $title;
    public $descr;

    /**
     * @var array
     */
    public static $definition = [
        'table' => 'an_productfields_fields_values',
        'primary' => 'id_value',
        'multilang' => true,
        'fields' => [
            'id_field' => ['type' =>self::TYPE_INT ],
            'price' => ['type' => self::TYPE_FLOAT],
            'type_price' => ['type' =>self::TYPE_INT ],
            'type_show_price' => array('type' =>self::TYPE_STRING ),
            'price_percent' => ['type' =>self::TYPE_INT ],
            'custom_tax' => ['type' =>self::TYPE_INT ],
            'apply_specific_price' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'position' => ['type' =>self::TYPE_INT ],
            
            
            'title' => ['type' =>self::TYPE_STRING, 'lang' => true ],
            'descr' => ['type' =>self::TYPE_HTML, 'lang' => true ],

            'file_display' => ['type' =>self::TYPE_STRING ],
            'file' => ['type' =>self::TYPE_STRING,'lang' => true ],
            'file_text_link' => ['type' =>self::TYPE_STRING,'lang' => true ],
        ],
    ];

    const imgDir = _PS_MODULE_DIR_ . 'an_productfields/img/';
    const imgUrl = __PS_BASE_URI__ . 'modules/an_productfields/img/';

    public static $typesFiles = [
        'gif' => true, 
        'jpg' => true, 
        'jpeg' => true, 
        'jpe' => true, 
        'png' => true, 
        'webp' => true, 
        'svg' => true, 
        'pdf' => false,
        'csv' => false
    ];



    public function __construct($id = null, $id_lang = null)
    {
        parent::__construct($id, $id_lang);
    }

    public static function getValuesByIdField($idField = false, $id_product = false, $prices = [], $specificPricesReduction = 0)
    {
        if (!$idField){
            return [];
        }

        $context = Context::getContext();

        if (isset($context->cart->id_currency)){
            $currency = new Currency($context->cart->id_currency);
        } else {
            $currency = new Currency(Configuration::get('PS_CURRENCY_DEFAULT'));
        }       

        $sql = '
        SELECT * FROM ';

        if (Configuration::get('an_pf_customer_groups')){
            $sql .= '`' . _DB_PREFIX_ . 'an_productfields_customer_groups` apfcg, ';
        }
        
        $sql .= ' `' . _DB_PREFIX_ . 'an_productfields_fields_values` sw 
        LEFT JOIN `' . _DB_PREFIX_ . 'an_productfields_fields_values_lang` sl 
            ON (sw.`id_value` = sl.`id_value`
            AND sl.`id_lang` = ' . (int) $context->language->id . ')
        WHERE sw.`active`=1 ';

        if (Configuration::get('an_pf_customer_groups') && isset($context->customer->id_default_group)){
            $sql .= '    AND apfcg.`id_group` = '.(int)$context->customer->id_default_group.' AND apfcg.`id_data` = sw.`id_value` AND apfcg.`type` = 1';
        }
        
        $sql .= ' AND `id_field` =  '.(int) $idField.' ';

        if (Shop::isFeatureActive()) {
            $sql .= ' AND sw.`id_value` IN (
                SELECT sa.`id_value`
                FROM `' . _DB_PREFIX_ . 'an_productfields_fields_values_shop` sa
                WHERE sa.id_shop IN (' . implode(', ', Shop::getContextListShopID()) . ')
            )';
        }

        $sql .= ' ORDER BY sw.`position`';

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        foreach ($result as $id => $item){

            $result[$id]['fileUrl'] = '';
            $result[$id]['isFileImg'] = false;
            $image = [];
            if ($item['file'] != '' ){
                $result[$id]['fileUrl'] = self::imgUrl . $item['file'];
                $extension = pathinfo(trim(strip_tags($item['file'])), PATHINFO_EXTENSION);
                $extension = Tools::strtolower($extension);
                $result[$id]['format'] = $extension;
                $result[$id]['isFileImg'] = self::$typesFiles[$result[$id]['format']];

                $image['fileUrl'] = $result[$id]['fileUrl'];
                $image['format'] = $result[$id]['format'];
                $image['isFileImg'] = $result[$id]['isFileImg'];
            }

            $result[$id]['images'] = anProductFieldsFiles::getFiles(['id_value' => $item['id_value'], 'id_lang' => $context->language->id]);
            $result[$id]['imagesJson'] = json_encode($result[$id]['images']);

            $result[$id]['price_wt'] = 0;
            $result[$id]['priceFormatted'] = '';

            if ($item['price_percent'] != 0 && (isset($item['type_price']) && $item['type_price'] == 1)){
                $result[$id]['price'] = $result[$id]['price_wt'] = $item['price_percent'];
                $result[$id]['priceFormatted'] = $item['price_percent'] . '%';
            }

            if ($item['price'] != 0 && (!isset($item['type_price']) | $item['type_price'] == 0)){

                $result[$id]['price'] = $item['price'] = anPfs::applyReductionCustomerGroup($item['price'], $id_product);

                if ($specificPricesReduction && $item['apply_specific_price']){
                    $item['price'] -= $item['price'] * $specificPricesReduction;
                    $item['price_without_reduction'] = $item['price'];
                    $item['reduction_percentage'] = $specificPricesReduction * 100;
                }

                $result[$id]['price'] = $item['price'] = Tools::convertPriceFull($item['price'], null, $currency);
                $result[$id]['price_wt'] = anProductFieldsTax::addTax($item['price'], $item['custom_tax'], $id_product);
                $result[$id]['priceFormatted'] = anPfs::formatPrice($item['price'], $result[$id]['price_wt']);
            }

            $result[$id]['priceImpact'] = 'no';
            if ($result[$id]['price'] < 0){
                $result[$id]['priceImpact'] = 'minus';
            } else if ($result[$id]['price'] > 0){
                $result[$id]['priceImpact'] = 'plus';
            }            
        }

        return $result;
    }
	
    public static function getValueById($id_value)
    {
        $sql = '
        SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_values` sw 
        LEFT JOIN `' . _DB_PREFIX_ . 'an_productfields_fields_values_lang` sl 
            ON (sw.`id_value` = sl.`id_value`
            AND sl.`id_lang` = ' . (int) Context::getContext()->language->id . ')
        WHERE sw.`id_value` =  '.(int) $id_value.' ';

        $result = Db::getInstance()->getRow($sql);

        if (!$result){
            return [];
        }

        return $result;
    }

    public static function getAllValues($id_field)
    {
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_values` sw WHERE sw.`id_field` = ' . (int) $id_field . '';

        if (Shop::isFeatureActive()) {
            $sql .= ' AND sw.`id_value` IN (
                SELECT sa.`id_value`
                FROM `' . _DB_PREFIX_ . 'an_productfields_fields_values_shop` sa
                WHERE sa.id_shop IN (' . implode(', ', Shop::getContextListShopID()) . ')
            )';
        }

        $values = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
       
        foreach ($values as $key => $value){
            $sql_lang = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_values_lang` WHERE `id_value` = ' . (int) $value['id_value'] . '';
            $res_lang = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql_lang);
            $langContent = [];
            foreach ($res_lang as $item){
                $item['iso_code'] = Language::getIsoById($item['id_lang']);
                $langContent[$item['iso_code']] = $item;
            }            
            $values[$key]['languages_values'] = $langContent;

            $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_values_shop` WHERE `id_value` = ' . (int) $value['id_value'] . '';
            $res = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
            $values[$key]['shops'] = $res;

        }

        return $values;
    }

    public function prepareSomeFields()
    {
        if ($this->type_price != 1){
            $this->type_show_price = '';
        }  
    }

    public function getCustomerGroups()
    {
        $sql = '
		SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_customer_groups` 
		WHERE `type` = 1 AND `id_data` = '.(int) $this->id.'';	
        $rowsGroups = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        $groups = [];
        foreach ($rowsGroups as $item){
            $groups[$item['id_group']] = $item['id_group'];
        }

        return $groups;
    }

    public function setCustomerGroups($customer_groups = [])
    {
        Db::getInstance(_PS_USE_SQL_SLAVE_)->execute('
        DELETE FROM `' . _DB_PREFIX_ . 'an_productfields_customer_groups` WHERE `type` = 1 AND `id_data` = ' . (int) $this->id);
        
       
        if (!$customer_groups) {
            return;
        }
        
        foreach ($customer_groups as $id){
            $sql = '
            INSERT INTO `'._DB_PREFIX_.'an_productfields_customer_groups`  (`id_group`, `id_data`, `type`) 
            VALUES ("'.(int) $id.'", "'.$this->id.'", "1" )'; 

            Db::getInstance(_PS_USE_SQL_SLAVE_)->execute($sql);
        }
       
     
    }

    public function save($null_values = false, $auto_date = true)
    {        
        $this->prepareSomeFields();
        return parent::save($null_values, $auto_date);
    } 

    public function add($auto_date = true, $null_values = false)
    {   
        $this->prepareSomeFields();
        return parent::add($null_values, $null_values);
    }

    public function update($null_values = false)
    {   
        $this->prepareSomeFields();
        return parent::update($null_values);
    }    
  
    public function delete()
    {       
        $files = anProductFieldsFiles::getFiles(['id_value' => $this->id]);

        foreach ($files as $fileLang){
            foreach ($fileLang as $file){
                $newFile = new anProductFieldsFiles($file['id_file']);
                $newFile->delete();
            }
        }

        if (isset($this->file) && is_array($this->file)){
            foreach ($this->file as $fileName){
                @unlink(self::imgDir . $fileName);
            }
        }

        Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute('
            DELETE FROM `'._DB_PREFIX_.'an_productfields_customer_groups` WHERE `type` = 1 AND `id_data`='.(int) $this->id.' 
        ');  

        anPfsDependencies::deleteByIdValue($this->id);

        return parent::delete();
    }    

    public static function duplicate($idValue)
    {
        $obj = new anProductFieldsValues($idValue);
        $newObj = $obj->duplicateObject();

        if ($newObj){
            $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_values_shop` sw WHERE sw.`id_value` = ' . (int) $idValue . '';
            $shops = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
            foreach ($shops as $shop){
                $sql = 'INSERT INTO `'._DB_PREFIX_.'an_productfields_fields_values_shop`  (`id_value`, `id_shop`) 
                VALUES ("'.(int) $newObj->id.'", "'.(int) $shop['id_shop'].'" )';
                Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute($sql);
            }            
        }
    }

    public function duplicateObject()
    {
        $newObj = parent::duplicateObject();
 
        foreach ($newObj->file as $key => $file) {

            $ext = substr($file, strrpos($file, '.') + 1);
            $ext = Tools::strtolower($ext);           
            $newFileName = md5(uniqid()) . '_' . $key . '.' . $ext;

            if (Tools::copy(anProductFieldsValues::imgDir . $file, anProductFieldsValues::imgDir . $newFileName)){
                $newObj->file[$key] = $newFileName;
            }
        }

        $newObj->save();

        return $newObj;
    }
}