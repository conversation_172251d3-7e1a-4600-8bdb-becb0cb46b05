<?php
/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

class anProductFieldsPsCart
{
    public static function prepareProductsFields(&$cart, $_products)
    {
        $products = [];        
        foreach ($_products as $key => $product){
  
            $prices['price'] = $product['price'];
            $prices['price_wt'] = '';
            if (isset($product['price_wt'])){
                $prices['price_wt'] = $product['price_wt'];
            }
            
            $groupsFields = anProductFieldsCart::getFieldsForCart(
                        $cart,
                        $product['id_product'],
                        $product['id_product_attribute'],
                        $product['id_customization'],
                        true,
                        $prices
            );

            if ($groupsFields && !isset($product['an_group_id'])){

                $cart_quantity = $product['cart_quantity'];
                
                foreach ($groupsFields as $group){

                    $produtAdd = $product;

                    $produtAdd['cart_quantity'] = $group['qty'];
                    $produtAdd['quantity'] = $group['qty'];
                    $produtAdd['an_group_id'] = $group['id_pf_cart'];
                    
                    $cart_quantity = $cart_quantity - $group['qty'];
    
                    if (isset($produtAdd['attributes']) && $produtAdd['attributes'] != '' && $group['valuesString'] !='' ){
                        $produtAdd['attributes'] .= ' - ';
                    } elseif (!isset($produtAdd['attributes'])) {
                        $produtAdd['attributes'] = '';
                    }

                    $produtAdd['attributes'] .= $group['valuesString'];

                    $produtAdd['field_total']  = $group['total'];
                    $produtAdd['total']  = $produtAdd['price'] * $group['qty'] + $group['total'];
                    //  round
                    if (Configuration::get(an_productfields::PREFIX . 'round_prices')){
                        $produtAdd['total'] = round($produtAdd['total']);
                    }      

                    if (isset($produtAdd['price_wt'])){
                       $produtAdd['total_wt']  = $produtAdd['price_wt'] * $group['qty'] + $group['total_wt'];
                       $produtAdd['field_total_wt']  = $group['total_wt'];
                        //  round
                        if (Configuration::get(an_productfields::PREFIX . 'round_prices')){
                            $produtAdd['total_wt'] = round($produtAdd['total_wt']);
                        }
                    }

                    //  Item product
                    if (Configuration::get(an_productfields::PREFIX . 'totalFieldsPlusProductInCart')){
                        $produtAdd['price'] += $group['total_one'];
                        if (isset($produtAdd['price_wt'])){
                            $produtAdd['price_wt'] += $group['total_wt_one'];
                        }

                        if (isset($produtAdd['price_with_reduction'])){
                            $produtAdd['price_with_reduction'] += $group['total_wt_one'];
                        }
                        if (isset($produtAdd['price_with_reduction_without_tax'])){
                            $produtAdd['price_with_reduction_without_tax'] += $group['total_one'];
                        }
                        if (isset($produtAdd['price_without_reduction'])){
                            $produtAdd['price_without_reduction'] += $group['total_wt_one'];
                        }
                        if (isset($produtAdd['price_without_reduction_without_tax'])){
                            $produtAdd['price_without_reduction_without_tax'] += $group['total_one'];
                        }
                    }

                    //  Weight
                    //$produtAdd['weight'] = 20;

                    $products[] = $produtAdd;
                }
                
                if ($cart_quantity > 0){

                    $product['total']  = $product['price'] * $cart_quantity;

                    if (isset($product['price_wt'])){
                       $product['total_wt']  = $product['price_wt'] * $cart_quantity;
                    }

                    $product['cart_quantity'] = $cart_quantity;
                    $product['quantity'] = $cart_quantity;
                    $products[] = $product;
                }

            } else {
                 $products[] = $product;
            }
        }

        return $products;
    }

    public static function getOrderTotalWithFields(
        $cart,
        $withTaxes = true,
        $type = Cart::BOTH,
        $products = null,
        $id_carrier = null,
        $use_cache = false,
        bool $keepOrderPrices = false
    )
    {
        $context = Context::getContext();

        if (!$cart){
            return 0;
        }        
        
        if ((int) $id_carrier <= 0) {
            $id_carrier = null;
        }

        // deprecated type
        if ($type == Cart::ONLY_PRODUCTS_WITHOUT_SHIPPING) {
            $type = Cart::ONLY_PRODUCTS;
        }

        // check type
        $type = (int) $type;
        $allowedTypes = [
            Cart::ONLY_PRODUCTS,
            Cart::ONLY_DISCOUNTS,
            Cart::BOTH,
            Cart::BOTH_WITHOUT_SHIPPING,
            Cart::ONLY_SHIPPING,
            Cart::ONLY_WRAPPING,
            Cart::ONLY_PHYSICAL_PRODUCTS_WITHOUT_SHIPPING,
        ];
        if (!in_array($type, $allowedTypes)) {
            throw new \Exception('Invalid calculation type: ' . $type);
        }

        // EARLY RETURNS

        // if cart rules are not used
        if ($type == Cart::ONLY_DISCOUNTS && !CartRule::isFeatureActive()) {
            return 0;
        }
        // no shipping cost if is a cart with only virtuals products
        $virtual = $cart->isVirtualCart();
        if ($virtual && $type == Cart::ONLY_SHIPPING) {
            return 0;
        }
        if ($virtual && $type == Cart::BOTH) {
            $type = Cart::BOTH_WITHOUT_SHIPPING;
        }

        // filter products
        if (null === $products) {
            $products = $cart->getProducts(false, false, null, true, $keepOrderPrices);
        }

        if ($type == Cart::ONLY_PHYSICAL_PRODUCTS_WITHOUT_SHIPPING) {
            foreach ($products as $key => $product) {
                if (!empty($product['is_virtual'])) {
                    unset($products[$key]);
                }
            }
            $type = Cart::ONLY_PRODUCTS;
        }

        if ($type == Cart::ONLY_PRODUCTS) {
            foreach ($products as $key => $product) {
                if (!empty($product['is_gift'])) {
                    unset($products[$key]);
                }
            }
        }

        if (Tax::excludeTaxeOption()) {
            $withTaxes = false;
        }

        // CART CALCULATION
        $cartRules = [];
        if (in_array($type, [Cart::BOTH, Cart::BOTH_WITHOUT_SHIPPING, Cart::ONLY_DISCOUNTS])) {
            $cartRules = $cart->getTotalCalculationCartRules($type, $type == Cart::BOTH);
           
        }

        if (version_compare(_PS_VERSION_, '1.7.7', '>=')){
            $computePrecision = Context::getContext()->getComputingPrecision();
        } else {
            $computePrecision = Configuration::get('_PS_PRICE_COMPUTE_PRECISION_');
        }
        
        if (!$computePrecision){
            $computePrecision = 2;
        }

        $calculator = $cart->newCalculator($products, $cartRules, $id_carrier, $computePrecision, $keepOrderPrices);
        
        switch ($type) {
            case Cart::ONLY_SHIPPING:
                $calculator->calculateRows();
                if (version_compare(_PS_VERSION_, '1.7.7', '>=')){
                    $calculator->calculateFees();
                } else {
                    $calculator->calculateFees($computePrecision);
                }                   
                $amount = $calculator->getFees()->getInitialShippingFees();

                break;
            case Cart::ONLY_WRAPPING:
                $calculator->calculateRows();
                if (version_compare(_PS_VERSION_, '1.7.7', '>=')){
                    $calculator->calculateFees();
                } else {
                    $calculator->calculateFees($computePrecision);
                }                
                $amount = $calculator->getFees()->getInitialWrappingFees();

                break;
            case Cart::BOTH:
                if (version_compare(_PS_VERSION_, '1.7.7', '>=')){
                    $calculator->processCalculation();
                } else {
                    $calculator->processCalculation($computePrecision);
                }
                $amount = $calculator->getTotal();

                /////////////////////////////////////////////////////////////////////////////
                $value = $withTaxes ? $amount->getTaxIncluded() : $amount->getTaxExcluded();
                $cartVouchersValues = anProductFieldsPsCart::getVauchersData($cart);

                $value = $value + anProductFieldsPsCart::getOrderTotalOnlyFields($products, $withTaxes);

                $amountDiscounts = $calculator->getDiscountTotal();
                $valueDiscounts = $withTaxes ? $amountDiscounts->getTaxIncluded() : $amountDiscounts->getTaxExcluded();
            
                if ($withTaxes){
                    $realDiscountsSum = $cartVouchersValues['value_real'] - $valueDiscounts;
                    $value = $value - $realDiscountsSum ;
                } else {
                    $realDiscountsSum = $cartVouchersValues['value_tax_exc'] - $valueDiscounts;
                    $value = $value - $realDiscountsSum ;
                }

                return Tools::ps_round($value, $computePrecision);
                /////////////////////////////////////////////////////////////////////////////

                break;
            case Cart::BOTH_WITHOUT_SHIPPING:
                $calculator->calculateRows();
                // dont process free shipping to avoid calculation loop (and maximum nested functions !)
                $calculator->calculateCartRulesWithoutFreeShipping();
                $amount = $calculator->getTotal(true);
                /////////////////////////////////////////////////////////////////////////////
                $value = $withTaxes ? $amount->getTaxIncluded() : $amount->getTaxExcluded();
                $value += anProductFieldsPsCart::getOrderTotalOnlyFields($products, $withTaxes);
                return Tools::ps_round($value, $computePrecision);
                /////////////////////////////////////////////////////////////////////////////                
                break;
            case Cart::ONLY_PRODUCTS:
                $calculator->calculateRows();
                $amount = $calculator->getRowTotal();
                /////////////////////////////////////////////////////////////////////////////
                $value = $withTaxes ? $amount->getTaxIncluded() : $amount->getTaxExcluded();
                $value += anProductFieldsPsCart::getOrderTotalOnlyFields($products, $withTaxes);
                return Tools::ps_round($value, $computePrecision);
                /////////////////////////////////////////////////////////////////////////////
                break;

            case Cart::ONLY_DISCOUNTS:
                if (version_compare(_PS_VERSION_, '1.7.7', '>=')){
                    $calculator->processCalculation();
                } else {
                    $calculator->processCalculation($computePrecision);
                }
                $amount = $calculator->getDiscountTotal();

                $cartVouchersValues = anProductFieldsPsCart::getVauchersData($cart);
                if ($withTaxes){
                    return Tools::ps_round($cartVouchersValues['value_real'], $computePrecision);
                } else {
                    return Tools::ps_round($cartVouchersValues['value_tax_exc'], $computePrecision);
                }
                break;

            default:
                throw new \Exception('unknown cart calculation type : ' . $type);
        }

        // TAXES ?
        $value = $withTaxes ? $amount->getTaxIncluded() : $amount->getTaxExcluded();

        // ROUND AND RETURN
        return Tools::ps_round($value, $computePrecision);
    }

    

    public static function getVauchersData(&$cart)
    {
        $cartVouchersValues['value_real'] = 0;
        $cartVouchersValues['value_tax_exc'] = 0;
        $cartVouchers = $cart->getCartRules();
        foreach ($cartVouchers as $vaucher){
            $cartVouchersValues['value_real'] += $vaucher['value_real'];
            $cartVouchersValues['value_tax_exc'] += $vaucher['value_tax_exc'];
        }      

        return $cartVouchersValues;
    }

    public static function getOrderTotalOnlyFields($_products, $withTaxes)
    {
        $field_total = 0;
        $field_total_wt = 0;
        
        foreach ($_products  as $key => $product){
            
            if (isset($product['field_total'])){
                $field_total += $product['field_total'];
            }            

            if (isset($product['field_total_wt'])){
               $field_total_wt += $product['field_total_wt'];
            }
        }

        //  round
        if (Configuration::get(an_productfields::PREFIX . 'round_prices')){
            $field_total_wt = round($field_total_wt);
            $field_total = round($field_total);
        }        
   
        if ($withTaxes){
            return $field_total_wt;
        } else {
            return $field_total;
        }
    }

    ////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////

    
    //////////////// Old function, Do not delete, sometimes necessary in older versions of Prestashop
    public static function getFieldsOrderTotal($_products, $withTaxes)
    {
        $field_total = 0;
        $field_total_wt = 0;
        
        foreach ($_products  as $key => $product){
            
            if (isset($product['field_total'])){
                $field_total += $product['field_total'];
            }            

            if (isset($product['field_total_wt'])){
               $field_total_wt += $product['field_total_wt'];
            }
        }

        //  round
        if (Configuration::get(an_productfields::PREFIX . 'round_prices')){
            $field_total_wt = round($field_total_wt);
            $field_total = round($field_total);
        }        
   
        if ($withTaxes){
            return $field_total_wt;
        } else {
            return $field_total;
        }        
    }
    /////////////////////////
}
?>