<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

class anPfsPA
{
    static public function getProductsQuery()
    {
		$collection = new Collection('Product', (int)Context::getContext()->language->id);
		$productsAll = $collection->getResults();
	
		$products[] = ['id' => 0, 'name' =>'-'];
		foreach ($productsAll as $id => $product){
			
			$products[$product->id] = [
				'id' => $product->id,
				'name' => $product->name,
			];
			
		}

		return $products;
	}

    static public function updateProductsAttributes($productsAttributesSelected, $id_field = 0)
    {
		if (!$id_field){
			return false;
		}		

		Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute('DELETE FROM `'._DB_PREFIX_.'an_productfields_fields_relations` WHERE `id_field`='.(int) $id_field.' ');
		
		if (!$productsAttributesSelected || count($productsAttributesSelected) == 0) {
			$productsAttributesSelected = [];
		}
	
		$productsAttributesSelected = array_unique($productsAttributesSelected);
	
		foreach ($productsAttributesSelected as $productAttr) {				
            $val = explode('-', $productAttr);
			$sql = 'INSERT INTO `'._DB_PREFIX_.'an_productfields_fields_relations`  (`id_field`, `id_type`, `type`, `id_product_attribute`) 
			VALUES ("'.(int) $id_field.'", "'.(int) $val['0'].'", "4", "'.(int) $val['1'].'" )';
			Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute($sql);
		}

		return true;
    }


    static public function getProductsAttributesQuery($id_field = 0)
	{   
        if (!$id_field){
			return [];
		}		

		$sql = '
		SELECT  *, p.*
		FROM `' . _DB_PREFIX_ . 'an_productfields_fields_relations` awl
		
        LEFT JOIN `' . _DB_PREFIX_ . 'product` p
            ON (p.`id_product` = awl.`id_type`)
		
		LEFT JOIN `' . _DB_PREFIX_ . 'product_lang` pl
            ON (p.`id_product` = pl.`id_product`
            AND pl.`id_lang` = ' . (int) Context::getContext()->language->id . Shop::addSqlRestrictionOnLang('pl') . ')		
		
		WHERE awl.`id_field` = ' . (int) $id_field . '  AND awl.`type`="4" ';
		
		
		$result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql, true, false);
        $products = Product::getProductsProperties(Context::getContext()->language->id, $result);

        foreach ($products as &$product){

            $coverPhoto = '';
            if (isset($product['cover_image_id'])){
                $coverPhoto = Context::getContext()->link->getImageLink(
                    $product['link_rewrite'],
                    $product['cover_image_id'], 
                    ImageType::getFormattedName('cart')
                );
            }
            
            $product['cover'] = $coverPhoto;

            $product['attrString'] = '';
            if (isset($product['attributes'])){
                foreach ($product['attributes'] as $attr){
                    if ($product['attrString'] !=''){
                        $product['attrString'] .= ', ';
                    }
                    $product['attrString'] .= $attr['group'] . ': ' . $attr['name'];
                }
            }
        }

        return (array) $products;
	}
}