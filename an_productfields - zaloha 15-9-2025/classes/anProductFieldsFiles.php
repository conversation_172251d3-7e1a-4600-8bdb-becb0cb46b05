<?php
/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

class anProductFieldsFiles extends ObjectModel
{
    public $id_file;

    public $id;

    public $id_value;
    public $id_field;
    public $file;
    public $external;
    public $label;
    public $position;
    public $main;
    public $id_lang;

    public static $definition = [
        'table' => 'an_productfields_fields_files',
        'primary' => 'id_file',
        'multilang' => false,
        'fields' => [
            'id_value' => ['type' =>self::TYPE_INT ],
            'id_field' => ['type' =>self::TYPE_INT ],

            'file' => ['type' =>self::TYPE_STRING ],
            'external' => ['type' =>self::TYPE_STRING ],
            'label' => ['type' =>self::TYPE_STRING ],
            'position' => ['type' =>self::TYPE_INT ],
            'main' => ['type' =>self::TYPE_INT ],
            'id_lang' => ['type' =>self::TYPE_INT ]
        ]
    ];

    const imgDir = _PS_MODULE_DIR_ . 'an_productfields/img/';
    const imgUrl = __PS_BASE_URI__ . 'modules/an_productfields/img/';
    const moduleUrl = __PS_BASE_URI__ . 'modules/an_productfields/';

    public static $typesFiles = [
        'gif' => 'img', 
        'jpg' => 'img', 
        'jpeg' => 'img', 
        'jpe' => 'img', 
        'png' => 'img', 
        'webp' => 'img', 
        'svg' => 'img', 
        'pdf' => false
    ];

    

    public function __construct($id = null, $id_lang = null)
    {
        parent::__construct($id, $id_lang);
    }

    public static function getFiles($params)
    {
        if (!isset($params['id_field'])){
            $params['id_field'] = 0;
        }

        if (!isset($params['id_value'])){
            $params['id_value'] = 0;
        }

        if ($params['id_value'] == 0){
            return [];
        }

        $sql_lang = '';
        if (isset($params['id_lang'])){
            $sql_lang = ' AND id_lang = '.(int) $params['id_lang'].'  ';    
        }

        $sql = '
        SELECT * 
        FROM `' . _DB_PREFIX_ . 'an_productfields_fields_files` 
        WHERE `id_field` = ' . (int) $params['id_field'] . ' AND `id_value` = ' . (int) $params['id_value'] . $sql_lang;
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        $files = [];
        foreach ($result as $file){
            
            $file['fileUrl'] = '';
            $file['youtubeId'] = '';
            if ($file['external'] == 'youtube'){
                $file['format'] = 'youtube';
                $file['fileUrl'] = $file['file'];  
                if (preg_match('#(?<=v=|v\/|vi=|vi\/|youtu.be\/)[a-zA-Z0-9_-]{11}#', $file['file'], $videoId)) {
                    $file['youtubeId'] = $videoId[0];
                }
            }
            
            if ($file['external'] != 'youtube'){
                $file['fileUrl'] = self::imgUrl . $file['file'];
                $extension = pathinfo(trim(strip_tags($file['file'])), PATHINFO_EXTENSION);
                $extension = Tools::strtolower($extension);
                $file['format'] = $extension;
            }

            $file['isFileImg'] = false;
            if (isset(self::$typesFiles[$file['format']]) && self::$typesFiles[$file['format']] == 'img'){
                $file['isFileImg'] = true;   
            }               

            $file['fileUrlIcon'] = '';
            if ($file['format'] == 'youtube' && $file['youtubeId'] != ''){
                $file['fileUrlIcon'] = 'https://img.youtube.com/vi/'.$file['youtubeId'].'/0.jpg';
            } else if ($file['format'] == 'youtube' && $file['youtubeId'] == ''){
                $file['fileUrlIcon'] = self::moduleUrl . 'views/img/' . $file['format'] . '.png';   
            } else if (!$file['isFileImg']){
                $file['fileUrlIcon'] = self::moduleUrl . 'views/img/fileIcon.png';
            }

            if (isset($params['id_lang'])){
                $files[] = $file;
            } else {
                $files[$file['id_lang']][] = $file;
            }
            
        }

        return $files; 
    }

    public static function uploadFiles($params)
    {
        $languages = Language::getLanguages(false);
        $errors = [];
        // $translator = Context::getContext()->getTranslator();

        if (!isset($params['id_field'])){
            $params['id_field'] = 0;
        }

        if (!isset($params['id_value'])){
            $params['id_value'] = 0;
        }

        if ($params['id_field'] == 0 && $params['id_value'] == 0){
            return;
        }
  
        foreach ($languages as $lang) {

            if (isset($_FILES['files_multi_'.$lang['id_lang']]) 
                && isset($_FILES['files_multi_'.$lang['id_lang']]['tmp_name']) 
                && !empty($_FILES['files_multi_'.$lang['id_lang']]['tmp_name'])) {

                $filesMulti = $_FILES['files_multi_'.$lang['id_lang']];

                foreach ($filesMulti['tmp_name'] as $key => $file){
     
                    $ext = substr($filesMulti['name'][$key], strrpos($filesMulti['name'][$key], '.') + 1);
                    $ext = Tools::strtolower($ext);
                    $fileName = md5(uniqid()) . '_' . $lang['id_lang'] . '.' . $ext;

                    if (!move_uploaded_file($file, self::imgDir . $fileName)) {
                    //    $errors[] = $translator->trans('An error occurred while attempting to upload the file.', [], 'Admin.Notifications.Error');
                    } else {
                        $newFile = new anProductFieldsFiles();
                        $newFile->id_value = $params['id_value'];
                        $newFile->id_field = $params['id_field'];
                        $newFile->file = $fileName;
                        $newFile->id_lang = $lang['id_lang'];
                        $newFile->save();
                    }

                }
            }
        }
    }

    public static function addYouTube($params, $nameField = 'youtube')
    {
        $languages = Language::getLanguages(false);
        if (!isset($params['id_field'])){
            $params['id_field'] = 0;
        }

        if (!isset($params['id_value'])){
            $params['id_value'] = 0;
        }

        if ($params['id_field'] == 0 && $params['id_value'] == 0){
            return;
        }

        foreach ($languages as $lang) {

            $youTube = Tools::getValue($nameField . '_' . $lang['id_lang']);

            if ($youTube && $youTube != ''){
                $newFile = new anProductFieldsFiles();
                $newFile->id_value = $params['id_value'];
                $newFile->id_field = $params['id_field'];
                $newFile->external = 'youtube';
                $newFile->file = $youTube;
                $newFile->id_lang = $lang['id_lang'];
                $newFile->save();
            }
        }



        
    }

    public static function validateUpload($file)
    {
        $maxFileSize = 8000000;
        $filesTypesString = '';
        $filesTypesForValid = [];
        foreach (self::$typesFiles as $key => $item){
            if ($filesTypesString != ''){
                $filesTypesString .= ', ';
            }
            $filesTypesString .= '.'.$key;

            $filesTypesForValid[] = $key;
        }

        if ((int) $maxFileSize > 0 && $file['size'] > (int) $maxFileSize) {
            return Context::getContext()->getTranslator()->trans('Image is too large (%1$d kB). Maximum allowed: %2$d kB', [$file['size'] / 1024, $maxFileSize / 1024], 'Admin.Notifications.Error');
        }

        if (!ImageManager::isCorrectImageFileExt($file['name'], $filesTypesForValid) || preg_match('/\%00/', $file['name'])) {
            return Context::getContext()->getTranslator()->trans('Image format not recognized, allowed formats are: ' . $filesTypesString, [], 'Admin.Notifications.Error');
        }  

        if ($file['error']) {
            return Context::getContext()->getTranslator()->trans('Error while uploading image; please change your server\'s settings. (Error code: %s)', [$file['error']], 'Admin.Notifications.Error');
        }   
        
        return false;
    } 

    public static function setMainFiles($files)
    {
        if (!$files){
            return; 
        }
        foreach ($files as $idLang => $file){
            $newFile = new anProductFieldsFiles($file);

            $sql = 'UPDATE `'._DB_PREFIX_.'an_productfields_fields_files` SET main=0 
            WHERE `id_value`='.(int) $newFile->id_value.' AND id_lang='.(int)$idLang.'  ';
            Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute($sql);

            $newFile->main = 1;
            $newFile->save();
        }
    }

    public static function deleteFiles($files)
    {
        if (!$files){
            return;
        }

        foreach ($files as $Idfile){
            $newFile = new anProductFieldsFiles($Idfile);
            $newFile->delete();
        }
    }

    public function delete()
    {        
        if ($this->external != 'youtube'){
            @unlink(self::imgDir . $this->file);
        }
        return parent::delete();
    } 


    // public static function duplicate($idField)
    // {
    //     $obj = new anProductFields($idField);
    //     $newObj = $obj->duplicateObject();

    //     if ($newObj){
    //         $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_values` sw WHERE sw.`id_field` = ' . (int) $idField . '';
    //         $values = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

    //         foreach ($values as $value){
    //             $newObjValue = new anProductFieldsValues($value['id_value']);
    //             $newObjValue->duplicateObject();
    //             $newObjValue->id_field = $newObj->id;
    //             $newObjValue->save();
    //         }

    //         $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_relations` sw WHERE sw.`id_field` = ' . (int) $idField . '';
    //         $relations = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    //         foreach ($relations as $relation){
    //             $sql = 'INSERT INTO `'._DB_PREFIX_.'an_productfields_fields_relations`  (`id_field`, `id_type`, `type`) 
    //             VALUES ("'.(int) $newObj->id.'", "'.(int) $relation['id_type'].'", "'.(int) $relation['type'].'" )';
    //             Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute($sql);
    //         }
    //     }
    // }

    // public function duplicateObject()
    // {
    //     $newObj = parent::duplicateObject();
        
    //     $languages = Language::getLanguages(false);
    //     foreach ($languages as $lang) {
    //         $newObj->title[$lang['id_lang']] = 'copy of ' . $newObj->title[$lang['id_lang']];
    //     }

    //     $newObj->save();

    //     return $newObj;
    // }
}