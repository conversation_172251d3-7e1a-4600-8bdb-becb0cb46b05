<?php
/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

class anPfsOrderDetails extends ObjectModel
{

    public $id_pf_order;

    public $id;

    public $id_field;
    public $id_cart;
    public $id_order;
    public $id_order_detail;
    public $id_product;
    public $id_product_attribute;
    public $id_customization;
    public $value;
    public $qty = 1;
    public $price = 0;
    public $price_wt = 0;
    public $title;
    public $title_value;
    public $date_add;

    public static $definition = [
        'table' => 'an_productfields_order',
        'primary' => 'id_pf_order',
        'multilang' => true,
        'fields' => [
            'id_field' => array('type' =>self::TYPE_INT ),
            'id_cart' => array('type' =>self::TYPE_INT ),
            'id_order' => array('type' =>self::TYPE_INT ),
            'id_order_detail' => array('type' =>self::TYPE_INT ),
            'id_product' => array('type' =>self::TYPE_INT ),
            'id_product_attribute' => array('type' =>self::TYPE_INT ),
            'id_customization' => array('type' =>self::TYPE_INT ),
            'value' => array('type' =>self::TYPE_STRING ),
            'qty' => array('type' =>self::TYPE_INT ),
            'price' => ['type' => self::TYPE_FLOAT],
            'price_wt' => ['type' => self::TYPE_FLOAT],
            'title' => ['type' =>self::TYPE_STRING,'lang' => true ],
            'title_value' => ['type' =>self::TYPE_STRING,'lang' => true ],
            'date_add' => array('type' => self::TYPE_DATE),
        ],
    ];

    public function __construct($id = null, $id_lang = null)
    {
        parent::__construct($id, $id_lang);
    }

    public static function saveOrderDetail(&$cart, $id_order_detail, $id_order, $product)
    {
        $productObj = new Product($product['id_product']);
        $prices = [];
        $prices['price'] = $productObj->price;
        $prices['price_wt'] = '';
        if (isset($productObj->price_wt)){
            $prices['price_wt'] = $productObj->price_wt;
        }
 
        $groupsFields = anProductFieldsCart::getFieldsForCart(
            $cart,
            $product['id_product'],
            $product['id_product_attribute'],
            $product['id_customization'],
            true,
            $prices
        );

        foreach ($groupsFields[$product['an_group_id']]['values'] as $value){
            $pfOrder = new anPfsOrderDetails();
            $pfOrder->id_field = $value['id_field'];
            $pfOrder->id_cart = $groupsFields[$product['an_group_id']]['id_cart'];
            $pfOrder->id_order = $id_order;
            $pfOrder->id_order_detail = $id_order_detail;
            $pfOrder->id_product = $groupsFields[$product['an_group_id']]['id_product'];
            $pfOrder->id_product_attribute = $groupsFields[$product['an_group_id']]['id_product_attribute'];
            $pfOrder->id_customization = $groupsFields[$product['an_group_id']]['id_customization'];
            $pfOrder->value = $value['value']; 
            $pfOrder->qty = $groupsFields[$product['an_group_id']]['qty'];
            $pfOrder->price = $value['price'];
            $pfOrder->price_wt = $value['price_wt'];
            $pfOrder->title = $value['field_title'];
            if ($value['isset_values']){
                $pfOrder->title_value = $value['title'];
            } else {
                $pfOrder->title_value = $value['value'];
            }
            $pfOrder->save();
        }
    }

    //  Replaced by a new feature in version 3.3.67. Should be removed in the future
    public static function saveOrder($params)
    {
        $order = $params['order'];
        $id_order = $order->id;

        $productsDetail = $order->getProductsDetail();
        $product_list = $order->product_list;

        foreach ($product_list as $product){

            $productObj = new Product($product['id_product']);
            $prices = [];
            $prices['price'] = $productObj->price;
            $prices['price_wt'] = '';
            if (isset($productObj->price_wt)){
                $prices['price_wt'] = $productObj->price_wt;
            }

            $groupsFields = anProductFieldsCart::getFieldsForCart(
                $product['id_product'],
                $product['id_product_attribute'],
                $product['id_customization'],
                true,
                $prices
            );

            $orderDetail = self::getOrderDetail($product, $productsDetail);
            $id_order_detail = 0;
            if (is_array($orderDetail) && isset($orderDetail['id_order_detail'])){
                $id_order_detail = $orderDetail['id_order_detail']; 
            }

            foreach ($groupsFields as $group){
                if ($product['an_group_id'] == $group['id_pf_cart']){
                    foreach ($group['values'] as $value){

                        $pfOrder = new anPfsOrderDetails();
                        $pfOrder->id_field = $value['id_field'];
                        $pfOrder->id_cart = $group['id_cart'];
                        $pfOrder->id_order = $id_order;
                        $pfOrder->id_order_detail = $id_order_detail;
                        $pfOrder->id_product = $group['id_product'];
                        $pfOrder->id_product_attribute = $group['id_product_attribute'];
                        $pfOrder->id_customization = $group['id_customization'];
                        $pfOrder->value = $value['value']; 
                        $pfOrder->qty = $group['qty'];
                        $pfOrder->price = $value['price'];
                        $pfOrder->price_wt = $value['price_wt'];
                        $pfOrder->title = $value['field_title'];
                        if ($value['isset_values']){
                            $pfOrder->title_value = $value['title'];
                        } else {
                            $pfOrder->title_value = $value['value'];
                        }
                        $pfOrder->save();
                    }
                }
            }

        }
    }
    //  Replaced by a new feature in version 3.3.67. Should be removed in the future
    public static function getOrderDetail($product, $productsDetail)
    {
        $returnOrderDetail = false;
        
        foreach ($productsDetail as $orderDetail){

            $product['price'] = round(floatval($product['price']), 6);
            $orderDetail['unit_price_tax_excl'] = round(floatval($orderDetail['unit_price_tax_excl']), 6);
            $orderDetail['product_price'] = round(floatval($orderDetail['product_price']), 6);
                        
            $comparePrices = false;
            if (($product['price'] == $orderDetail['unit_price_tax_excl']) || ($product['price'] == $orderDetail['product_price'])){
                $comparePrices = true;
            }

            if (
                intval($product['id_product']) == intval($orderDetail['id_product']) && 
                intval($product['id_product_attribute']) == intval($orderDetail['product_attribute_id']) && 
                intval($product['id_customization']) == intval($orderDetail['id_customization']) && 
                $comparePrices 
            //    && $product['quantity'] == $orderDetail['product_quantity']
            ){
                $returnOrderDetail = $orderDetail;
            }
        }
     
        return $returnOrderDetail;
    }


    public static function getFieldsOrderByIdOrder($id_order)
    {
        $context = Context::getContext();    
        
        if (isset($context->customer->id_default_group)){
            $priceDisplayMethod = Group::getPriceDisplayMethod($context->customer->id_default_group);
        } else {
            $priceDisplayMethod = Group::getDefaultPriceDisplayMethod();
        }
        
        $order = new Order($id_order);
        $productsDetail = $order->getProductsDetail();

        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_order` pfo WHERE pfo.`id_order` =  '.(int) $id_order.' ';
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        $fields = [];
        foreach ($result as $field){
            $pfOrder = (array) new anPfsOrderDetails($field['id_pf_order'], $context->language->id);

            $pfOrder['priceFormatted'] = '';
            if ($priceDisplayMethod || !Configuration::get('an_pf_use_tax')){
                $pfOrder['priceFormatted'] = Tools::displayPrice($pfOrder['price']);
            } else {
                $pfOrder['priceFormatted'] = Tools::displayPrice($pfOrder['price_wt']);
            }

            $objField = new anProductFields($field['id_field']);
            if ($objField->type == 'file'){
                $pfOrder['file'] = anPfsCustomerFiles::imgUrl . $pfOrder['value'];
            }               

            if (isset($fields[$field['id_order_detail']]['fields'][$field['id_field']])){
                $fields[$field['id_order_detail']]['fields'][$field['id_field']]['title_value'] .= ', ' . $pfOrder['title_value'];
            } else {
                $fields[$field['id_order_detail']]['fields'][$field['id_field']] = $pfOrder;
            }
            
            foreach ($productsDetail as $pd){
                if ($pd['id_order_detail'] == $field['id_order_detail']){
                    $pd['qty'] = $field['qty'];
                    $fields[$field['id_order_detail']]['productDetail'] = $pd;
                }
            }     
        }

       return $fields;
    }

}