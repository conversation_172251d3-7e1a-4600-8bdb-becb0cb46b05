<?php
/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

class anProductFieldsTax
{
    public static function addTax($price, $custom_tax = null, $id_product = null)
    {
        if (!Configuration::get('an_pf_use_tax')){
            return $price;
        }
                
        $context = Context::getContext();

        if ($custom_tax){
            $id_tax_rules_group = $custom_tax;
        } else {
            $product = new Product($id_product);
            $id_tax_rules_group = $product->id_tax_rules_group;
        }

        if (is_object($context->cart) && $context->cart->{Configuration::get('PS_TAX_ADDRESS_TYPE')} != null) {
            $id_address = $context->cart->{Configuration::get('PS_TAX_ADDRESS_TYPE')};
            $address = new Address($id_address);
        } else {
            $address = new Address();
        }
        $address = Address::initialize($address->id, true);

        $tax_manager = TaxManagerFactory::getManager($address, $id_tax_rules_group);
        $product_tax_calculator = $tax_manager->getTaxCalculator();
        
        $price_wt = $product_tax_calculator->addTaxes($price);

        if (Configuration::get('an_pf_price_wt_round')){
            $price_wt = Tools::ps_round($price_wt, 1);
        }

        return $price_wt;
    }


}