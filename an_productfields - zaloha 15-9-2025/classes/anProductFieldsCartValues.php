<?php
/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

//require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anProductFieldsValues.php';

require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anPfs.php';
require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anPfsCustomerFiles.php';

class anProductFieldsCartValues extends ObjectModel
{

    public $id_pf_cart_value;

    public $id;

    public $id_pf_cart;
    public $id_field;
    public $value;
    public $date_add;
    
    /**
     * @var array
     */
    public static $definition = [
        'table' => 'an_productfields_cart_values',
        'primary' => 'id_pf_cart_value',
        'multilang' => false,
        'fields' => [
            'id_pf_cart' => array('type' =>self::TYPE_INT ),
            'id_field' => array('type' =>self::TYPE_INT ),
            'value' => ['type' =>self::TYPE_STRING ],
            'date_add' => array('type' => self::TYPE_DATE),
        ],
    ];

    /**
     * Formula constructor.
     *
     * @param null $id
     */
    public function __construct($id = null, $id_lang = null)
    {
        parent::__construct($id, $id_lang);
    }

    public static function getCartValuesByCartField($cartField, $prices = [])
    {
        $context = Context::getContext();

        $productObj = new Product($cartField['id_product']); 
        $product['id_product_attribute'] = $cartField['id_product_attribute'];
        $product = (array)$productObj;
        $product['id_product'] = (int) $productObj->id;
        $productProperties = Product::getProductProperties($context->language->id, $product, $context);

        $specificPricesReduction = 0;
        if (isset($productProperties['specific_prices']) && isset($productProperties['specific_prices']['reduction_type']) && 
            $productProperties['specific_prices']['reduction_type'] == 'percentage' && 
            $productProperties['specific_prices']['id_product_attribute'] == 0)
            {
            $specificPricesReduction = $productProperties['specific_prices']['reduction'];
        }
        
        $currency = new Currency(
            $context->cart->id_currency ? $context->cart->id_currency : Configuration::get('PS_CURRENCY_DEFAULT')
        );           
        
        $sql = '
        SELECT *, pfl.`title` as field_title FROM 
            `' . _DB_PREFIX_ . 'an_productfields_cart_values`  pcv,
            `' . _DB_PREFIX_ . 'an_productfields_fields` pf
        LEFT JOIN `' . _DB_PREFIX_ . 'an_productfields_fields_lang` pfl 
            ON (pf.`id_field` = pfl.`id_field`
             AND pfl.`id_lang` = ' . (int) Context::getContext()->language->id . ')             
        WHERE 
            pcv.`id_field`=pf.`id_field` AND
            pcv.`id_pf_cart` = '.(int) $cartField['id_pf_cart'].' 
        
        ';

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
     
        $values = [];
        foreach ($result as $key => $cartValue){
            $cartValue['isset_values'] = anProductFields::$typesFields[$cartValue['type']]['values'];
            if ($cartValue['isset_values'] && $cartValue['value'] != ''){
                $value = anProductFieldsValues::getValueById($cartValue['value']);
                $cartValue = array_merge($cartValue, $value);
            }

            $cartValue['price_wt'] = 0;
            $cartValue['priceFormatted'] = '';
            
            if ($cartValue['price_percent'] != 0 && (isset($cartValue['type_price']) && $cartValue['type_price'] == 1)){

                if (isset($prices['price'])){
                    $cartValue['price'] = $prices['price'] / 100 * $cartValue['price_percent'];
                } else {
                    $cartValue['price'] = 0;
                }

                if ($cartValue['num_multiply_price']){
                    $cartValue['price'] = $cartValue['price'] * $cartValue['value'];
                }

                $cartValue['price_wt'] = anProductFieldsTax::addTax($cartValue['price'], $cartValue['custom_tax'], $cartField['id_product']);
                
                $cartValue['priceFormatted'] = anPfs::formatPrice($cartValue['price'], $cartValue['price_wt']);
                $cartValue['priceFormatted'] = $cartValue['price_percent'] . '% ('.$cartValue['priceFormatted'].')';
            }

            if ($cartValue['price'] != 0 && (!isset($cartValue['type_price']) | $cartValue['type_price'] == 0)){

                $cartValue['price'] = anPfs::applyReductionCustomerGroup($cartValue['price'], $cartField['id_product']);
                if ($specificPricesReduction && $cartValue['apply_specific_price']){
                    $cartValue['price'] -= $cartValue['price'] * $specificPricesReduction;
                    $cartValue['price_without_reduction'] = $cartValue['price'];
                    $cartValue['reduction_percentage'] = $specificPricesReduction * 100;
                }
           
                if ($cartValue['num_multiply_price']){
                    $cartValue['price'] = $cartValue['price'] * $cartValue['value'];
                }                
                
                $cartValue['price'] = Tools::convertPriceFull($cartValue['price'], null, $currency);
                $cartValue['price_wt'] = anProductFieldsTax::addTax($cartValue['price'], $cartValue['custom_tax'], $cartField['id_product']);

                $cartValue['priceFormatted'] = anPfs::formatPrice($cartValue['price'], $cartValue['price_wt']);
            }   

            if ($cartValue['price'] != 0 && (isset($cartValue['type_price']) && $cartValue['type_price'] == 2)){
                
                $cartValue['price'] = anPfs::applyReductionCustomerGroup($cartValue['price'], $cartField['id_product']);

                $valueCalculate = $cartValue['value'];
                if ($cartValue['ignore_spaces']){
                    $valueCalculate = str_replace(" ", "", $valueCalculate);
                    $valueCalculate = str_replace("\t", "", $valueCalculate);
                    $valueCalculate = str_replace("\r\n", "", $valueCalculate);
                }

                $length = Tools::strlen($valueCalculate) - $cartValue['free_characters'];

                $cartValue['price'] = $length * $cartValue['price'];

                $cartValue['price'] = Tools::convertPriceFull($cartValue['price'], null, $currency);
                $cartValue['price_wt'] = anProductFieldsTax::addTax($cartValue['price'], $cartValue['custom_tax'], $cartField['id_product']);

                $cartValue['priceFormatted'] = anPfs::formatPrice($cartValue['price'], $cartValue['price_wt']);
            }

            $values[] = $cartValue;

        }

        return $values;
    }

    public static function cartValuesTotals($values)
    {
        $total['values_total'] = 0;
        $total['values_total_wt'] = 0;

        foreach ($values as $value){
            $total['values_total'] += $value['price'];
            $total['values_total_wt'] += $value['price_wt'];
        }

        return $total;
    }

    public static function cartValuesArrayToString($values)
    {
        $newValues = [];

        foreach ($values as $value){

            $valueAdd = '';
            if ($value['priceFormatted'] !='' ){
                $value['priceFormatted'] = ' ('.$value['priceFormatted'].')';
            }
            if ($value['isset_values']){
                $valueAdd = $value['title'];
            } else {
                $valueAdd = $value['value'];
            }

            $valueAdd .= $value['priceFormatted'];


            if (isset($newValues[$value['id_field']])){
                $newValues[$value['id_field']]['value'] .= ', ' . $valueAdd;
            } else {
                $newValues[$value['id_field']]['value'] = $valueAdd;
                if ($value['field_title'] == ''){
                    $value['field_title'] = $value['id_field'];
                }
                $newValues[$value['id_field']]['title'] = $value['field_title'];
            }
        }

        $string = '';
        foreach ($newValues as $value){
            if ($string !=''){
                $string .= ' ' . Configuration::get('PS_ATTRIBUTE_ANCHOR_SEPARATOR') . ' ';
            }
            
            //  The character ":" is reserved and cannot be used in titles (names)
            $name = str_replace(':', '', $value['title']);   
            $string .= $name. ': ' . $value['value'];
            
        }
       
        return $string;
    }


    public static function getCartValuesByIdField($idGroup)
    {
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_cart_values`  pcv WHERE pcv.`id_pf_cart` = '.(int) $idGroup.' ';

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        if ($result){
            return $result;
        }

        return [];
    }

    public static function deleteByIdGroup($groupId)
    {
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_cart_values` WHERE id_pf_cart = '.(int) $groupId.'  ';
        $result = Db::getInstance()->executeS($sql);

        foreach ($result as $value){
            $obj = new self($value['id_pf_cart_value']);
            $field = new anProductFields($obj->id_field);
            if ($field->type == 'file'){
                @unlink(anPfsCustomerFiles::imgDir . $obj->value);
            }
            $obj->delete();
        }

    }
}