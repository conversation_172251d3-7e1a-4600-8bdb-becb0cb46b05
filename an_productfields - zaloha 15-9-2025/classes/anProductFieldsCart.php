<?php
/**
* 2023 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2023 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anPfsCustomerFiles.php';

class anProductFieldsCart extends ObjectModel
{

    public $id_pf_cart;

    public $id;

    public $id_cart;
    public $id_product;
    public $id_product_attribute;
    public $id_customization;

    public $hash;
    public $qty = 1;

    public $date_upd;
    public $date_add;
    

    /**
     * @var array
     */
    public static $definition = [
        'table' => 'an_productfields_cart',
        'primary' => 'id_pf_cart',
        'multilang' => false,
        'fields' => [
            'id_cart' => array('type' =>self::TYPE_INT ),
            'id_product' => array('type' =>self::TYPE_INT ),
            'id_product_attribute' => array('type' =>self::TYPE_INT ),
            'id_customization' => array('type' =>self::TYPE_INT ),

            'hash' => ['type' =>self::TYPE_STRING ],
            'qty' => array('type' =>self::TYPE_INT ),
            
            'date_upd' => array('type' => self::TYPE_DATE),
            'date_add' => array('type' => self::TYPE_DATE),
        ],
    ];

    public function __construct($id = null, $id_lang = null)
    {
        parent::__construct($id, $id_lang);
    }

    public static function checkOpDown($id_product, $id_product_attribute, $id_customization)
    {
        $an_group_id = Tools::getValue('an_group_id');
        $op = Tools::getValue('op');

        if ($an_group_id && $op == 'down'){
            $sql = '
            SELECT * 
            FROM `ps_an_productfields_cart` 
            WHERE 
                `id_cart` = '. (int) Context::getContext()->cart->id .' AND 
                `id_product` = '. (int) $id_product .' AND 
                `id_product_attribute` = '. (int) $id_product_attribute .' AND 
                `id_customization` = '. (int) $id_customization .' AND
                `id_pf_cart` = '. (int) $an_group_id .'
            ';
            $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
            
            if (count($result) < 1){
               return true;
            }
        }

        return false;
    }

    public static function addToCartFields($id_product, $id_product_attribute, $qty = 1, $customization_id = 0)
    {   
        if (!$id_product){
            return false;
        }

        $an_pf_name = Tools::getValue('an_pf_name', []);
        $an_group_id = Tools::getValue('an_group_id');
        $op = Tools::getValue('op');


        //  If there is a root field and if the root field does not have the desired value selected, the field is removed from the list for addition 
        $dependenciesReverse = anPfsDependencies::getDependenciesDataReverse();
        $forRemove = [];
        foreach ($an_pf_name as $fieldId => $fieldValue){

            if (isset($dependenciesReverse[$fieldId])){ 
                $idFieldDependence = $dependenciesReverse[$fieldId]['id_field_dependence'];
                $idValue = $dependenciesReverse[$fieldId]['id_value'];

                if (!isset($an_pf_name[$idFieldDependence])){
                    $an_pf_name[$idFieldDependence] = 0;
                }
            }

            if (isset($dependenciesReverse[$fieldId]) && isset($an_pf_name[$idFieldDependence]) && $an_pf_name[$idFieldDependence] != $idValue   ){
                $forRemove[] = $fieldId;
            }
        }

        foreach ($forRemove as $fieldId){
            unset($an_pf_name[$fieldId]);
        }



        anPfsCustomerFiles::uploadFiles($an_pf_name);
        
        if (count($an_pf_name) == 0 && !$an_group_id){
            return false;
        }

        $hash = anProductFieldsCart::createHash($an_pf_name, $id_product, $id_product_attribute, $customization_id);

        if ($an_group_id){
            $idPfCart = $an_group_id;
        } else if ($hash) {
            $idPfCart = anProductFieldsCart::geIdFieldCartByHash($hash);
        } else {
            $idPfCart = false;
        }

        $qty = (int) $qty;
        if ($qty < 1){
            $qty = 1;
        }

        if ($idPfCart){
            $fieldCartObj = new anProductFieldsCart($idPfCart);
            if ($op && $op == 'down'){
                $fieldCartObj->qty = $fieldCartObj->qty - $qty;
            } else {
                $fieldCartObj->qty = $fieldCartObj->qty + $qty;
            }
            if ($fieldCartObj->qty < 1){
                $fieldCartObj->qty = 1;
            }
        } else {
            $fieldCartObj = new anProductFieldsCart();
            $fieldCartObj->id_cart = Context::getContext()->cart->id;
            $fieldCartObj->id_product = $id_product;
            $fieldCartObj->id_product_attribute = $id_product_attribute;
            $fieldCartObj->id_customization = $customization_id;
            $fieldCartObj->qty = $qty;
            $fieldCartObj->hash = $hash;            
        }
        
        $fieldCartObj->save();
       
        if (!$idPfCart){
            foreach ($an_pf_name as $fieldId => $fieldValue){

                $field = new anProductFields($fieldId);

                if (!$field->id){
                    return;
                }

                if ($field->multi && is_array($fieldValue)){
                    foreach($fieldValue as $idValue){
                        $obj = new anProductFieldsCartValues();
                        $obj->id_pf_cart = $fieldCartObj->id;
                        $obj->id_field = $fieldId;
                        $obj->value = $idValue;
                        $obj->save();
                    }

                } else if ($field->type != 'select' && $fieldValue != '' || ($field->type == 'select' && $fieldValue != 0)) {
                    $obj = new anProductFieldsCartValues();
                    $obj->id_pf_cart = $fieldCartObj->id;
                    $obj->id_field = $fieldId;
                    $obj->value = $fieldValue;
                    $obj->save();
                }
            }
        }
    }

    public static function duplicateCartGroupsValues($oldIdCart, $newIdCart)
    {
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_order` WHERE `id_cart`= '.(int) $oldIdCart.'    ';
        $orderDetails = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        $newCart = [];
        $newCartValues = [];
        $an_pf_name = [];

        foreach ($orderDetails as $orderDetail){
            $newCarts[$orderDetail['id_order_detail']] = [
                'id_cart' => '',
                'id_order_detail' => $orderDetail['id_order_detail'],
                'id_product' => $orderDetail['id_product'],
                'id_product_attribute' => $orderDetail['id_product_attribute'],
                'id_customization' => $orderDetail['id_customization'],
                'qty' => $orderDetail['qty']
            ];

            $newCartValues[$orderDetail['id_order_detail']][] = [
                'id_pf_cart' => '',
                'id_order_detail' => $orderDetail['id_order_detail'],
                'id_field' => $orderDetail['id_field'],
                'value' => $orderDetail['value']
            ];

            $an_pf_name[$orderDetail['id_order_detail']][$orderDetail['id_field']] = $orderDetail['value'];
        }

        foreach ($newCarts as $newCart){
            $objCart = new anProductFieldsCart();
            $objCart->id_cart = $newIdCart;
            $objCart->id_product = $newCart['id_product'];
            $objCart->id_product_attribute = $newCart['id_product_attribute'];
            $objCart->id_customization = $newCart['id_customization'];
            $objCart->hash = anProductFieldsCart::createHash(
                $an_pf_name[$newCart['id_order_detail']], 
                $newCart['id_product'],
                $newCart['id_product_attribute'], 
                $newCart['id_customization']);
            $objCart->qty = $newCart['qty'];
            $objCart->save();

            foreach ($newCartValues[$newCart['id_order_detail']] as $newCartValue){
                $objValue = new anProductFieldsCartValues();
                $objValue->id_pf_cart = $objCart->id;
                $objValue->id_field = $newCartValue['id_field'];
                $objValue->value = $newCartValue['value'];
                $objValue->save();
            }
        }
    }

    public static function geIdFieldCartByHash($hash = '', $id_cart = false)
    {
        if ($hash == ''){
            return false;
        }

        if (!$id_cart){
            $id_cart = Context::getContext()->cart->id;
        }
        
        $sql = 'SELECT id_pf_cart FROM `' . _DB_PREFIX_ . 'an_productfields_cart` WHERE `id_cart` = '.(int) $id_cart.' AND `hash` = "'.pSQL($hash).'" ';

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }


    public static function getFieldsForCart(&$cart, $id_product, $id_product_attribute, $id_customization, bool $fullInfo = true, $prices = [])
    {      
        if (!$cart) {
            return;
        }
        
        $sql = '
        SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_cart` 
        WHERE 
        `id_cart` = '.(int) $cart->id.' AND 
        `id_product` = '.(int) $id_product.' AND
        `id_product_attribute` = '.(int) $id_product_attribute.' AND 
        `id_customization` = '.(int) $id_customization.'
        ';

        $action = Tools::getValue('action');
        $controller = Tools::getValue('controller');
        if ($action == 'add-to-cart' && $controller == 'ajax'){
            $sql .= 'ORDER BY date_upd DESC';
        }

        $cache_id = 'anProductFieldsCart::getFieldsForCart ' . md5($sql);

     //   if (false === Cache::isStored($cache_id)) {

            $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
            
            $groupsFields = [];
            foreach ($result as $key => $cartField){
                $groupsFields[$cartField['id_pf_cart']] = $cartField;
                
                if ($fullInfo){
                    $groupsFields[$cartField['id_pf_cart']]['values'] = anProductFieldsCartValues::getCartValuesByCartField($cartField, $prices);
                    $groupsFields[$cartField['id_pf_cart']]['valuesString'] = anProductFieldsCartValues::cartValuesArrayToString($groupsFields[$cartField['id_pf_cart']]['values']);
                    
                    $total = anProductFieldsCartValues::cartValuesTotals($groupsFields[$cartField['id_pf_cart']]['values']);
                    $groupsFields[$cartField['id_pf_cart']]['total'] = $total['values_total'] * $cartField['qty'];
                    $groupsFields[$cartField['id_pf_cart']]['total_wt'] = $total['values_total_wt'] * $cartField['qty'];
                    $groupsFields[$cartField['id_pf_cart']]['total_one'] = $total['values_total'];
                    $groupsFields[$cartField['id_pf_cart']]['total_wt_one'] = $total['values_total_wt'];
                }
            }
       
        //    Cache::store($cache_id, $groupsFields);

            return $groupsFields;
      //  }

        return Cache::retrieve($cache_id);
    }

    public static function getFieldsByIdCart($id_cart)
    {
        $sql = '
        SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_cart` 
        WHERE `id_cart` = '.(int) $id_cart.' ';

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        if ($result){
            return $result;
        }

        return [];
    }

    public static function deleteFieldsCartAndValues($id_cart, $id_product, $id_product_attribute, $id_customization)
    {
        $group = new self(Tools::getValue('an_group_id'));
        $groups = self::getFieldsForCart(Context::getContext()->cart, $id_product, $id_product_attribute, $id_customization, true);
        $productQuantity = Context::getContext()->cart->getProductQuantity($id_product, $id_product_attribute, $id_customization);

        //  Product without fields, but
        if (count($groups) && !isset($group->id)){
            $qty = 0;
            foreach ($groups as $item){
                $qty += $item['qty'];
            }

            $qtyDown = 0;
            if (isset($productQuantity['quantity']) && $productQuantity['quantity'] > 0){
                $qtyDown = $productQuantity['quantity'] - $qty;
                if ($qtyDown < 1){
                    $qtyDown = 1;
                }
                Context::getContext()->cart->updateQty($qtyDown, $id_product, $id_product_attribute, $id_customization, 'down');
            }
            
            Cache::clear();
            return true;
        }

        if (!isset($group->id)){
            return false;
        }

        $group->delete();

        if (count($groups) > 1 || isset($productQuantity['quantity']) && $productQuantity['quantity'] > 1){
            Context::getContext()->cart->updateQty($group->qty, $id_product, $id_product_attribute, $id_customization, 'down');
        } else {
            return false;
        }

        return true;
    }

    public function delete()
    {        
        anProductFieldsCartValues::deleteByIdGroup($this->id);
        Cache::clear();
        return parent::delete();
    }

    public static function deleteFieldsCartValueByIdCart($id_cart)
    {
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_cart` WHERE `id_cart` = '.(int) $id_cart.' ';
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        
        foreach ($result as $cartField){
            $group = new self($cartField['id_pf_cart']);
            $group->delete();
        }
    }





    public static function createHash($an_pf_name, $id_product, $id_product_attribute, $id_customization)
    {        
        $stringFieldValue = $id_product.'|'.$id_product_attribute.'|'.$id_customization;

        $empty = true;

        foreach ($an_pf_name as $fieldId => $fieldValue){
            if (is_array($fieldValue)){
                $fieldValue = json_encode($fieldValue);
                $empty = false;
            }
            $stringFieldValue .= '|'.$fieldId.':'.$fieldValue;
            if ($fieldValue != ''){
                $empty = false;
            }
        }

        if ($empty){
         //   return false;
        }

        return md5($stringFieldValue);
    }











    //  experement
    public static function getProductsFieldsForCart($cart, $refresh, $id_product, $id_country, $fullInfos, $keepOrderPrices)
    {
        if (!$cart->id) {
            return [];
        }
        // Product cache must be strictly compared to NULL, or else an empty cart will add dozens of queries
        if ($cart->_products !== null && !$refresh) {
            // Return product row with specified ID if it exists
            if (is_int($id_product)) {
                foreach ($cart->_products as $product) {
                    if ($product['id_product'] == $id_product) {
                        return [$product];
                    }
                }

                return [];
            }

            return $cart->_products;
        }

        // Build query
        $sql = new DbQuery();

        // Build SELECT
        $sql->select('cp.`id_product_attribute`, cp.`id_product`, cp.`quantity` AS cart_quantity, cp.id_shop, cp.`id_customization`, pl.`name`, p.`is_virtual`,
                        pl.`description_short`, pl.`available_now`, pl.`available_later`, product_shop.`id_category_default`, p.`id_supplier`,
                        p.`id_manufacturer`, m.`name` AS manufacturer_name, product_shop.`on_sale`, product_shop.`ecotax`, product_shop.`additional_shipping_cost`,
                        product_shop.`available_for_order`, product_shop.`show_price`, product_shop.`price`, product_shop.`active`, product_shop.`unity`, product_shop.`unit_price_ratio`,
                        stock.`quantity` AS quantity_available, p.`width`, p.`height`, p.`depth`, stock.`out_of_stock`, p.`weight`,
                        p.`available_date`, p.`date_add`, p.`date_upd`, IFNULL(stock.quantity, 0) as quantity, pl.`link_rewrite`, cl.`link_rewrite` AS category,
                        CONCAT(LPAD(cp.`id_product`, 10, 0), LPAD(IFNULL(cp.`id_product_attribute`, 0), 10, 0), IFNULL(cp.`id_address_delivery`, 0), IFNULL(cp.`id_customization`, 0)) AS unique_id, cp.id_address_delivery,
                        product_shop.advanced_stock_management, ps.product_supplier_reference supplier_reference');

        // Build FROM
        $sql->from('cart_product', 'cp');

        // Build JOIN
        $sql->leftJoin('product', 'p', 'p.`id_product` = cp.`id_product`');
        $sql->innerJoin('product_shop', 'product_shop', '(product_shop.`id_shop` = cp.`id_shop` AND product_shop.`id_product` = p.`id_product`)');
        $sql->leftJoin(
            'product_lang',
            'pl',
            'p.`id_product` = pl.`id_product`
            AND pl.`id_lang` = ' . (int) $cart->id_lang . Shop::addSqlRestrictionOnLang('pl', 'cp.id_shop')
        );

        $sql->leftJoin(
            'category_lang',
            'cl',
            'product_shop.`id_category_default` = cl.`id_category`
            AND cl.`id_lang` = ' . (int) $cart->id_lang . Shop::addSqlRestrictionOnLang('cl', 'cp.id_shop')
        );

        $sql->leftJoin('product_supplier', 'ps', 'ps.`id_product` = cp.`id_product` AND ps.`id_product_attribute` = cp.`id_product_attribute` AND ps.`id_supplier` = p.`id_supplier`');
        $sql->leftJoin('manufacturer', 'm', 'm.`id_manufacturer` = p.`id_manufacturer`');

        // @todo test if everything is ok, then refactorise call of this method
        $sql->join(Product::sqlStock('cp', 'cp'));

        // Build WHERE clauses
        $sql->where('cp.`id_cart` = ' . (int) $cart->id);
        if ($id_product) {
            $sql->where('cp.`id_product` = ' . (int) $id_product);
        }
        $sql->where('p.`id_product` IS NOT NULL');

        // Build ORDER BY
        $sql->orderBy('cp.`date_add`, cp.`id_product`, cp.`id_product_attribute` ASC');

        if (Customization::isFeatureActive()) {
            $sql->select('cu.`id_customization`, cu.`quantity` AS customization_quantity');
            $sql->leftJoin(
                'customization',
                'cu',
                'p.`id_product` = cu.`id_product` AND cp.`id_product_attribute` = cu.`id_product_attribute` AND cp.`id_customization` = cu.`id_customization` AND cu.`id_cart` = ' . (int) $cart->id
            );
            $sql->groupBy('cp.`id_product_attribute`, cp.`id_product`, cp.`id_shop`, cp.`id_customization`');
        } else {
            $sql->select('NULL AS customization_quantity, NULL AS id_customization');
        }

        if (Combination::isFeatureActive()) {
            $sql->select('
                product_attribute_shop.`price` AS price_attribute, product_attribute_shop.`ecotax` AS ecotax_attr,
                IF (IFNULL(pa.`reference`, \'\') = \'\', p.`reference`, pa.`reference`) AS reference,
                (p.`weight`+ pa.`weight`) weight_attribute,
                IF (IFNULL(pa.`ean13`, \'\') = \'\', p.`ean13`, pa.`ean13`) AS ean13,
                IF (IFNULL(pa.`isbn`, \'\') = \'\', p.`isbn`, pa.`isbn`) AS isbn,
                IF (IFNULL(pa.`upc`, \'\') = \'\', p.`upc`, pa.`upc`) AS upc,
                IF (IFNULL(pa.`mpn`, \'\') = \'\', p.`mpn`, pa.`mpn`) AS mpn,
                IFNULL(product_attribute_shop.`minimal_quantity`, product_shop.`minimal_quantity`) as minimal_quantity,
                IF(product_attribute_shop.wholesale_price > 0,  product_attribute_shop.wholesale_price, product_shop.`wholesale_price`) wholesale_price
            ');

            $sql->leftJoin('product_attribute', 'pa', 'pa.`id_product_attribute` = cp.`id_product_attribute`');
            $sql->leftJoin('product_attribute_shop', 'product_attribute_shop', '(product_attribute_shop.`id_shop` = cp.`id_shop` AND product_attribute_shop.`id_product_attribute` = pa.`id_product_attribute`)');
        } else {
            $sql->select(
                'p.`reference` AS reference, p.`ean13`, p.`isbn`,
                p.`upc` AS upc, p.`mpn` AS mpn, product_shop.`minimal_quantity` AS minimal_quantity, product_shop.`wholesale_price` wholesale_price'
            );
        }

        $sql->select('image_shop.`id_image` id_image, il.`legend`');
        $sql->leftJoin('image_shop', 'image_shop', 'image_shop.`id_product` = p.`id_product` AND image_shop.cover=1 AND image_shop.id_shop=' . (int) $cart->id_shop);
        $sql->leftJoin('image_lang', 'il', 'il.`id_image` = image_shop.`id_image` AND il.`id_lang` = ' . (int) $cart->id_lang);

        $result = Db::getInstance()->executeS($sql);

        // Reset the cache before the following return, or else an empty cart will add dozens of queries
        $products_ids = [];
        $pa_ids = [];
        if ($result) {
            foreach ($result as $key => $row) {
                $products_ids[] = $row['id_product'];
                $pa_ids[] = $row['id_product_attribute'];
                $specific_price = SpecificPrice::getSpecificPrice($row['id_product'], $cart->id_shop, $cart->id_currency, $id_country, $cart->id_shop_group, $row['cart_quantity'], $row['id_product_attribute'], $cart->id_customer, $cart->id);
                if ($specific_price) {
                    $reduction_type_row = ['reduction_type' => $specific_price['reduction_type']];
                } else {
                    $reduction_type_row = ['reduction_type' => 0];
                }

                $result[$key] = array_merge($row, $reduction_type_row);
            }
        }
        // Thus you can avoid one query per product, because there will be only one query for all the products of the cart
        Product::cacheProductsFeatures($products_ids);
        Cart::cacheSomeAttributesLists($pa_ids, (int) $cart->id_lang);

        if (empty($result)) {
            $cart->_products = [];

            return [];
        }

        if ($fullInfos) {
            $cart_shop_context = Context::getContext()->cloneContext();

            $givenAwayProductsIds = [];

            $cart->_products = [];

            foreach ($result as &$row) {
                if (!array_key_exists('is_gift', $row)) {
                    $row['is_gift'] = false;
                }

                $additionalRow = Product::getProductProperties((int) $cart->id_lang, $row);
                $row['reduction'] = $additionalRow['reduction'];
                $row['reduction_without_tax'] = $additionalRow['reduction_without_tax'];
                $row['price_without_reduction'] = $additionalRow['price_without_reduction'];
                $row['specific_prices'] = $additionalRow['specific_prices'];
                unset($additionalRow);

                $givenAwayQuantity = 0;
                $giftIndex = $row['id_product'] . '-' . $row['id_product_attribute'];
                if ($row['is_gift'] && array_key_exists($giftIndex, $givenAwayProductsIds)) {
                    $givenAwayQuantity = $givenAwayProductsIds[$giftIndex];
                }

                if (!$row['is_gift'] || (int) $row['cart_quantity'] === $givenAwayQuantity) {
                    $row = $cart->applyProductCalculations($row, $cart_shop_context, null, $keepOrderPrices);
                } else {
                    // Separate products given away from those manually added to cart
                    $cart->_products[] = $cart->applyProductCalculations($row, $cart_shop_context, $givenAwayQuantity, $keepOrderPrices);
                    unset($row['is_gift']);
                    $row = $cart->applyProductCalculations(
                        $row,
                        $cart_shop_context,
                        $row['cart_quantity'] - $givenAwayQuantity,
                        $keepOrderPrices
                    );
                }

                $cart->_products[] = $row;
            }
        } else {
            $cart->_products = $result;
        }

        return $cart->_products;
    }
}