<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

class anPfsCustomerGroups
{
    public static function setGroups()
    {
        $allGroups = Group::getGroups(Context::getContext()->language->id);

        foreach ($allGroups as $group){
            $groups[$group['id_group']] = $group['id_group'];
        }

        return $groups;
    }
}