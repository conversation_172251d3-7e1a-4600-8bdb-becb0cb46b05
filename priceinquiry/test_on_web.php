<?php
/**
 * Test modulu přímo na webu
 * Simuluje návštěvu stránky produktu
 */

// Načteme PrestaShop
require_once(dirname(__FILE__) . '/prestashop_loader.php');

echo "<h1>Test modulu na webu</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px; }
    .button.success { background: #28a745; }
    iframe { width: 100%; height: 600px; border: 1px solid #ddd; }
</style>";

$productId = 15336;
$productUrl = "https://czimg-dev1.www2.peterman.cz/klobouky/{$productId}-braz-klobouk.html";

// 1. Základní informace
echo "<div class='section'>";
echo "<h2>1. Test produktu</h2>";

echo "<strong>Testovací produkt:</strong><br>";
echo "<strong>ID:</strong> {$productId}<br>";
echo "<strong>URL:</strong> <a href='{$productUrl}' target='_blank'>{$productUrl}</a><br>";

// Načteme produkt pro kontrolu
$product = new Product($productId);
if (Validate::isLoadedObject($product)) {
    $productName = is_array($product->name) ? $product->name[1] : $product->name;
    echo "<strong>Název:</strong> {$productName}<br>";
    
    $price = Product::getPriceStatic($product->id);
    echo "<strong>Cena:</strong> {$price} Kč<br>";
    
    if ($price == 0) {
        echo "<span class='ok'>✅ Cena je nulová - modul by se měl zobrazit</span><br>";
    } else {
        echo "<span class='warning'>⚠️ Cena není nulová - modul se nemusí zobrazit</span><br>";
    }
} else {
    echo "<span class='error'>❌ Produkt se nepodařilo načíst</span><br>";
}

echo "</div>";

// 2. Test hooks na této stránce
echo "<div class='section'>";
echo "<h2>2. Test hooks na této stránce</h2>";

$module = Module::getInstanceByName('priceinquiry');
if ($module) {
    // Nastavíme kontext pro product stránku
    Context::getContext()->controller->php_self = 'product';
    Context::getContext()->smarty->assign('product', $product);
    
    echo "<h3>Test displayProductActions:</h3>";
    try {
        $params = ['product' => $product];
        $hookOutput = $module->hookDisplayProductActions($params);
        
        if ($hookOutput && strlen(trim($hookOutput)) > 0) {
            echo "<span class='ok'>✅ Hook vrací obsah (" . strlen($hookOutput) . " znaků)</span><br>";
            
            // Zkontrolujeme klíčové části
            if (strpos($hookOutput, 'price-inquiry-block-detail') !== false) {
                echo "<span class='ok'>✅ Obsahuje hlavní CSS třídu</span><br>";
            }
            if (strpos($hookOutput, 'Cena na dotaz') !== false) {
                echo "<span class='ok'>✅ Obsahuje text 'Cena na dotaz'</span><br>";
            }
            if (strpos($hookOutput, 'Zjistit cenu') !== false) {
                echo "<span class='ok'>✅ Obsahuje tlačítko 'Zjistit cenu'</span><br>";
            }
            
            echo "<details><summary>Zobrazit HTML výstup</summary><pre>" . htmlspecialchars($hookOutput) . "</pre></details>";
        } else {
            echo "<span class='error'>❌ Hook nevrací obsah</span><br>";
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ Chyba: " . $e->getMessage() . "</span><br>";
    }
    
    echo "<h3>Test Hook::exec:</h3>";
    try {
        $execOutput = Hook::exec('displayProductActions', ['product' => $product]);
        
        if ($execOutput && strpos($execOutput, 'price-inquiry') !== false) {
            echo "<span class='ok'>✅ Hook::exec obsahuje náš modul</span><br>";
        } else {
            echo "<span class='error'>❌ Hook::exec neobsahuje náš modul</span><br>";
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ Chyba Hook::exec: " . $e->getMessage() . "</span><br>";
    }
    
} else {
    echo "<span class='error'>❌ Modul nebyl nalezen</span><br>";
}

echo "</div>";

// 3. Kontrola CSS a JS
echo "<div class='section'>";
echo "<h2>3. Kontrola CSS a JavaScript</h2>";

$cssPath = _PS_MODULE_DIR_ . 'priceinquiry/views/css/front.css';
$jsPath = _PS_MODULE_DIR_ . 'priceinquiry/views/js/front.js';

echo "<strong>CSS soubor:</strong><br>";
if (file_exists($cssPath)) {
    echo "<span class='ok'>✅ {$cssPath} existuje (" . filesize($cssPath) . " bytů)</span><br>";
} else {
    echo "<span class='error'>❌ CSS soubor neexistuje</span><br>";
}

echo "<strong>JavaScript soubor:</strong><br>";
if (file_exists($jsPath)) {
    echo "<span class='ok'>✅ {$jsPath} existuje (" . filesize($jsPath) . " bytů)</span><br>";
} else {
    echo "<span class='error'>❌ JavaScript soubor neexistuje</span><br>";
}

// Test displayHeader hook
echo "<h3>Test displayHeader hook:</h3>";
try {
    $headerOutput = $module->hookDisplayHeader([]);
    if ($headerOutput && strlen(trim($headerOutput)) > 0) {
        echo "<span class='ok'>✅ displayHeader vrací obsah (" . strlen($headerOutput) . " znaků)</span><br>";
        
        if (strpos($headerOutput, 'front.css') !== false) {
            echo "<span class='ok'>✅ Obsahuje CSS</span><br>";
        }
        if (strpos($headerOutput, 'front.js') !== false) {
            echo "<span class='ok'>✅ Obsahuje JavaScript</span><br>";
        }
    } else {
        echo "<span class='error'>❌ displayHeader nevrací obsah</span><br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>❌ Chyba displayHeader: " . $e->getMessage() . "</span><br>";
}

echo "</div>";

// 4. Simulace stránky produktu
echo "<div class='section'>";
echo "<h2>4. Simulace stránky produktu</h2>";

echo "<p>Toto je simulace toho, co by se mělo zobrazit na stránce produktu:</p>";

if ($module && Validate::isLoadedObject($product)) {
    // Nastavíme kontext
    Context::getContext()->controller->php_self = 'product';
    Context::getContext()->smarty->assign('product', $product);
    
    // Získáme výstup hooks
    $headerOutput = $module->hookDisplayHeader([]);
    $productActionsOutput = $module->hookDisplayProductActions(['product' => $product]);
    $footerOutput = $module->hookDisplayFooter([]);
    
    echo "<div style='border: 2px solid #007cba; padding: 20px; background: #f9f9f9;'>";
    echo "<h3>Simulovaná stránka produktu:</h3>";
    
    // Header (CSS/JS)
    if ($headerOutput) {
        echo "<!-- CSS a JavaScript z displayHeader -->";
        echo $headerOutput;
    }
    
    echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; background: white;'>";
    echo "<h4>Informace o produktu:</h4>";
    echo "<strong>Název:</strong> " . (is_array($product->name) ? $product->name[1] : $product->name) . "<br>";
    echo "<strong>Cena:</strong> " . Product::getPriceStatic($product->id) . " Kč<br>";
    echo "</div>";
    
    // Product Actions (náš modul)
    if ($productActionsOutput) {
        echo "<div style='border: 2px solid #28a745; padding: 15px; margin: 10px 0; background: #d4edda;'>";
        echo "<h4>Výstup modulu 'Cena na dotaz':</h4>";
        echo $productActionsOutput;
        echo "</div>";
    } else {
        echo "<div style='border: 2px solid #dc3545; padding: 15px; margin: 10px 0; background: #f8d7da;'>";
        echo "<h4>❌ Modul se nezobrazuje</h4>";
        echo "<p>Hook displayProductActions nevrací žádný obsah.</p>";
        echo "</div>";
    }
    
    // Footer (modal)
    if ($footerOutput) {
        echo "<!-- Modal z displayFooter -->";
        echo $footerOutput;
    }
    
    echo "</div>";
}

echo "</div>";

// 5. Odkazy na test
echo "<div class='section'>";
echo "<h2>5. Test na skutečném webu</h2>";

echo "<p><strong>Pro finální test otevřete stránku produktu:</strong></p>";
echo "<p><a href='{$productUrl}' target='_blank' class='button success'>Otevřít stránku produktu</a></p>";

echo "<p><strong>Co hledat na stránce:</strong></p>";
echo "<ul>";
echo "<li>Text 'Cena na dotaz' místo ceny</li>";
echo "<li>Tlačítko 'Zjistit cenu'</li>";
echo "<li>Po kliknutí na tlačítko se otevře modal s formulářem</li>";
echo "</ul>";

echo "<p><strong>Pokud se modul nezobrazuje:</strong></p>";
echo "<ul>";
echo "<li>Zkontrolujte, že má produkt nulovou cenu</li>";
echo "<li>Zkontrolujte konzoli prohlížeče na JavaScript chyby</li>";
echo "<li>Zkontrolujte, že jsou hooks registrovány</li>";
echo "</ul>";

echo "</div>";

echo "<hr>";
echo "<p><a href='diagnostic_tools.php'>← Zpět na diagnostické nástroje</a></p>";
echo "<p><small>Test dokončen: " . date('Y-m-d H:i:s') . "</small></p>";
?>
