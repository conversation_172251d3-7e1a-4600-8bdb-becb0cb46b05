<?php
/**
 * Kompletní reset cache a objektů PrestaShop
 * Vyčistí všechny druhy cache včetně objektů
 */

// Načteme PrestaShop
require_once(dirname(__FILE__) . '/prestashop_loader.php');

echo "<h1>Kompletní reset cache</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

echo "<div class='section'>";
echo "<h2>Vyčištění cache</h2>";

$cleared = [];
$errors = [];

// 1. Smarty cache
try {
    Tools::clearSmartyCache();
    $cleared[] = "✅ Smarty cache";
} catch (Exception $e) {
    $errors[] = "❌ Smarty cache: " . $e->getMessage();
}

// 2. XML cache
try {
    Tools::clearXMLCache();
    $cleared[] = "✅ XML cache";
} catch (Exception $e) {
    $errors[] = "❌ XML cache: " . $e->getMessage();
}

// 3. Cache složky
$cacheDirs = [
    _PS_CACHE_DIR_ => 'Hlavní cache',
    _PS_CACHE_DIR_ . 'smarty/' => 'Smarty cache',
    _PS_CACHE_DIR_ . 'tcpdf/' => 'TCPDF cache',
    _PS_CACHE_DIR_ . 'cachefs/' => 'CacheFS',
    _PS_CACHE_DIR_ . 'purifier/' => 'HTML Purifier',
];

foreach ($cacheDirs as $dir => $name) {
    if (is_dir($dir)) {
        $files = glob($dir . '*');
        $deletedCount = 0;
        
        foreach ($files as $file) {
            if (is_file($file)) {
                if (unlink($file)) {
                    $deletedCount++;
                }
            }
        }
        
        $cleared[] = "✅ {$name} ({$deletedCount} souborů)";
    } else {
        $cleared[] = "ℹ️ {$name} (složka neexistuje)";
    }
}

// 4. Vyčištění objektů z paměti
try {
    // Vyčistíme cache modulů
    Module::clearCache();
    $cleared[] = "✅ Module cache";
} catch (Exception $e) {
    $errors[] = "❌ Module cache: " . $e->getMessage();
}

// 5. Vyčištění hook cache
try {
    Hook::clearCache();
    $cleared[] = "✅ Hook cache";
} catch (Exception $e) {
    $errors[] = "❌ Hook cache: " . $e->getMessage();
}

// 6. Vyčištění konfigurace cache
try {
    Configuration::clearCache();
    $cleared[] = "✅ Configuration cache";
} catch (Exception $e) {
    $errors[] = "❌ Configuration cache: " . $e->getMessage();
}

// 7. Vyčištění class cache
$classIndexFile = _PS_CACHE_DIR_ . 'class_index.php';
if (file_exists($classIndexFile)) {
    if (unlink($classIndexFile)) {
        $cleared[] = "✅ Class index cache";
    } else {
        $errors[] = "❌ Class index cache";
    }
}

// 8. Vyčištění autoload cache
if (function_exists('opcache_reset')) {
    opcache_reset();
    $cleared[] = "✅ OPCache reset";
}

// 9. Restart PHP session
if (session_status() === PHP_SESSION_ACTIVE) {
    session_destroy();
    $cleared[] = "✅ PHP session reset";
}

echo "<h3>Úspěšně vyčištěno:</h3>";
foreach ($cleared as $item) {
    echo $item . "<br>";
}

if (!empty($errors)) {
    echo "<h3>Chyby:</h3>";
    foreach ($errors as $error) {
        echo $error . "<br>";
    }
}

echo "</div>";

// Test modulu po vyčištění
echo "<div class='section'>";
echo "<h2>Test modulu po vyčištění</h2>";

$module = Module::getInstanceByName('priceinquiry');
if ($module) {
    echo "<span class='ok'>✅ Modul načten</span><br>";
    
    // Test hooks
    $hooks = ['displayProductActions', 'displayHeader', 'displayFooter'];
    foreach ($hooks as $hookName) {
        $hookId = Hook::getIdByName($hookName);
        if ($hookId) {
            $isRegistered = Hook::isModuleRegisteredOnHook($module, $hookId, Context::getContext()->shop->id);
            if ($isRegistered) {
                echo "<span class='ok'>✅ Hook {$hookName} registrován</span><br>";
            } else {
                echo "<span class='error'>❌ Hook {$hookName} NENÍ registrován</span><br>";
            }
        }
    }
    
    // Test konfigurace
    $enabled = Configuration::get('PRICE_INQUIRY_ENABLED');
    echo "<strong>Modul povolen:</strong> " . ($enabled ? "<span class='ok'>Ano</span>" : "<span class='error'>Ne</span>") . "<br>";
    
} else {
    echo "<span class='error'>❌ Modul nebyl nalezen</span><br>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>Doporučené další kroky</h2>";
echo "<ol>";
echo "<li><a href='debug_script.php'>Spustit diagnostiku</a> - ověřit stav modulu</li>";
echo "<li><a href='check_display.php'>Zkontrolovat zobrazení</a> - test výstupu</li>";
echo "<li>Pokud hooks stále nejsou registrovány: <a href='reinstall_module.php'>Reinstalovat modul</a></li>";
echo "<li><a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank'>Test na webu</a></li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><a href='diagnostic_tools.php'>← Zpět na diagnostické nástroje</a></p>";
echo "<p><small>Cache reset dokončen: " . date('Y-m-d H:i:s') . "</small></p>";
?>
