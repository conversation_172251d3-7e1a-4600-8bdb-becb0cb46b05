<?php
/**
 * <PERSON><PERSON><PERSON>ný reset všech cache a objektů
 * Pokusí se vyřešit problém s hooks API
 */

// Načteme PrestaShop
require_once(dirname(__FILE__) . '/prestashop_loader.php');

echo "<h1><PERSON><PERSON><PERSON><PERSON><PERSON> reset cache a objektů</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

$moduleName = 'priceinquiry';
$shopId = Context::getContext()->shop->id;

echo "<div class='section'>";
echo "<h2>Probíhá reset...</h2>";

$steps = [];
$errors = [];

// 1. Vyčištění všech cache
echo "<h3>1. Vyčištění cache</h3>";

try {
    Tools::clearSmartyCache();
    $steps[] = "✅ Smarty cache";
} catch (Exception $e) {
    $errors[] = "❌ Smarty cache: " . $e->getMessage();
}

try {
    Tools::clearXMLCache();
    $steps[] = "✅ XML cache";
} catch (Exception $e) {
    $errors[] = "❌ XML cache: " . $e->getMessage();
}

// Hook cache - zkusíme různé způsoby
try {
    // Vyčistíme cache soubory místo volání statické metody
    $cacheFiles = glob(_PS_CACHE_DIR_ . 'class_index.php');
    foreach ($cacheFiles as $file) {
        if (file_exists($file)) {
            unlink($file);
        }
    }
    $steps[] = "✅ Hook cache (soubory)";
} catch (Exception $e) {
    $errors[] = "❌ Hook cache: " . $e->getMessage();
}

// Module cache
try {
    // Vyčistíme module cache soubory
    $moduleCache = _PS_CACHE_DIR_ . 'modules/';
    if (is_dir($moduleCache)) {
        $files = glob($moduleCache . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
    }
    $steps[] = "✅ Module cache (soubory)";
} catch (Exception $e) {
    $errors[] = "❌ Module cache: " . $e->getMessage();
}

// Configuration cache
try {
    // Vyčistíme configuration cache soubory
    $configCache = _PS_CACHE_DIR_ . 'config/';
    if (is_dir($configCache)) {
        $files = glob($configCache . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
    }
    $steps[] = "✅ Configuration cache (soubory)";
} catch (Exception $e) {
    $errors[] = "❌ Configuration cache: " . $e->getMessage();
}

// 2. Vyčištění cache souborů
echo "<h3>2. Vyčištění cache souborů</h3>";

$cacheDirs = [
    _PS_CACHE_DIR_ => 'Hlavní cache',
    _PS_CACHE_DIR_ . 'smarty/' => 'Smarty templates',
    _PS_CACHE_DIR_ . 'tcpdf/' => 'TCPDF cache',
    _PS_CACHE_DIR_ . 'cachefs/' => 'CacheFS',
    _PS_CACHE_DIR_ . 'purifier/' => 'HTML Purifier',
];

foreach ($cacheDirs as $dir => $name) {
    if (is_dir($dir)) {
        $files = glob($dir . '*');
        $deletedCount = 0;
        
        foreach ($files as $file) {
            if (is_file($file)) {
                if (unlink($file)) {
                    $deletedCount++;
                }
            }
        }
        
        $steps[] = "✅ {$name} ({$deletedCount} souborů)";
    }
}

// 3. Reset PHP cache
echo "<h3>3. Reset PHP cache</h3>";

if (function_exists('opcache_reset')) {
    try {
        opcache_reset();
        $steps[] = "✅ OPcache reset";
    } catch (Exception $e) {
        $errors[] = "❌ OPcache: " . $e->getMessage();
    }
} else {
    $steps[] = "ℹ️ OPcache není dostupný";
}

if (function_exists('apc_clear_cache')) {
    apc_clear_cache();
    $steps[] = "✅ APC cache";
}

// 4. Reset session
echo "<h3>4. Reset session</h3>";

if (session_status() === PHP_SESSION_ACTIVE) {
    session_destroy();
    $steps[] = "✅ PHP session reset";
}

// 5. Vynucené znovu načtení modulu
echo "<h3>5. Vynucené znovu načtení modulu</h3>";

// Smazat modul z paměti pokud je tam
if (isset($GLOBALS['_MODULES'][$moduleName])) {
    unset($GLOBALS['_MODULES'][$moduleName]);
    $steps[] = "✅ Modul odstraněn z globální cache";
}

// Znovu načíst modul
$module = Module::getInstanceByName($moduleName);
if ($module) {
    $steps[] = "✅ Modul znovu načten";
} else {
    $errors[] = "❌ Modul se nepodařilo znovu načíst";
}

// 6. Test hooks po resetu
echo "<h3>6. Test hooks po resetu</h3>";

if ($module) {
    $testHooks = ['displayProductActions', 'displayHeader'];
    $workingHooks = 0;
    
    foreach ($testHooks as $hookName) {
        $hookId = Hook::getIdByName($hookName);
        try {
            if ($hookId && Hook::isModuleRegisteredOnHook($module, $hookId, $shopId)) {
                $steps[] = "✅ Hook {$hookName} funguje";
                $workingHooks++;
            } else {
                $errors[] = "❌ Hook {$hookName} nefunguje";
            }
        } catch (Exception $e) {
            $errors[] = "❌ Hook {$hookName} chyba: " . $e->getMessage();
        }
    }
    
    if ($workingHooks === count($testHooks)) {
        $steps[] = "🎉 Všechny testované hooks fungují!";
    }
}

// 7. Výsledky
echo "<h3>7. Výsledky</h3>";

echo "<h4>Úspěšné kroky:</h4>";
foreach ($steps as $step) {
    echo $step . "<br>";
}

if (!empty($errors)) {
    echo "<h4>Chyby:</h4>";
    foreach ($errors as $error) {
        echo $error . "<br>";
    }
}

echo "</div>";

// 8. Finální test
echo "<div class='section'>";
echo "<h2>Finální test modulu</h2>";

if ($module) {
    echo "<strong>Modul:</strong> " . $module->displayName . " (ID: {$module->id})<br>";
    echo "<strong>Aktivní:</strong> " . (Module::isEnabled($moduleName) ? 'Ano' : 'Ne') . "<br>";
    
    // Test konkrétního hook
    $testHookId = Hook::getIdByName('displayProductActions');
    try {
        $isRegistered = Hook::isModuleRegisteredOnHook($module, $testHookId, $shopId);
        echo "<strong>Hook displayProductActions:</strong> " . ($isRegistered ? "<span class='ok'>✅ Registrován</span>" : "<span class='error'>❌ Není registrován</span>") . "<br>";
    } catch (Exception $e) {
        echo "<strong>Hook displayProductActions:</strong> <span class='error'>❌ Chyba: " . $e->getMessage() . "</span><br>";
        $isRegistered = false;
    }
    
    // Test výstupu hook
    try {
        $product = new Product(15336);
        if (Validate::isLoadedObject($product)) {
            Context::getContext()->smarty->assign('product', $product);
            $params = ['product' => $product];
            $hookOutput = $module->hookDisplayProductActions($params);
            
            if ($hookOutput && strlen($hookOutput) > 100) {
                echo "<strong>Hook výstup:</strong> <span class='ok'>✅ Vrací obsah (" . strlen($hookOutput) . " znaků)</span><br>";
            } else {
                echo "<strong>Hook výstup:</strong> <span class='error'>❌ Nevrací obsah nebo je prázdný</span><br>";
            }
        }
    } catch (Exception $e) {
        echo "<strong>Hook test:</strong> <span class='error'>❌ Chyba: " . $e->getMessage() . "</span><br>";
    }
    
    if ($isRegistered) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
        echo "<h3>🎉 Reset úspěšný!</h3>";
        echo "<p>Hooks nyní fungují. Zkuste test na webu.</p>";
        echo "<p><a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>Test na webu</a></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
        echo "<h3>⚠️ Hooks stále nefungují</h3>";
        echo "<p>Možná je potřeba restart webového serveru nebo je problém s PrestaShop core.</p>";
        echo "<p><a href='debug_hooks_api.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>Detailní debug</a></p>";
        echo "</div>";
    }
    
} else {
    echo "<span class='error'>❌ Modul nebyl nalezen</span><br>";
}

echo "</div>";

echo "<hr>";
echo "<p><a href='diagnostic_tools.php'>← Zpět na diagnostické nástroje</a></p>";
echo "<p><small>Reset dokončen: " . date('Y-m-d H:i:s') . "</small></p>";
?>
