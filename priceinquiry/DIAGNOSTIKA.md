# Diagnostické nástroje pro modul "Cena na dotaz"

## Problém
Po optimalizaci modulu se přestala zobrazovat možnost "cena na dotaz" na produktech s nulovou cenou.

## Rychlé řešení

### 1. Spusťte diagnostické nástroje
Otevřete v prohlížeči: `/modules/priceinquiry/diagnostic_tools.php`

### 2. Postupujte podle návodu
1. **Úplná diagnostika** - zjistíte přesný stav modulu
2. **Oprava modulu** - automatická oprava základních problémů
3. **Nastavení testovací ceny** - pro test nastavte cenu produktu na 0 Kč
4. **Kontrola zobrazení** - ov<PERSON><PERSON><PERSON>, že modul vrací správný výstup
5. **Test na webu** - navštivte stránku produktu

## Dostupné nástroje

### 🔍 debug_script.php
- Kompletní diagnostika modulu
- <PERSON><PERSON><PERSON><PERSON> konfigurace, hooks, souborů
- Analýza testovacího produktu
- Doporučení pro opravu

### 👁️ check_display.php
- Rychlá kontrola zobrazení modulu
- Simulace návštěvy stránky produktu
- Analýza výstupu hooks
- Kontrola CSS a JavaScript

### 🎯 test_product.php
- Detailní test konkrétního produktu (ID 15336)
- Analýza cen variant
- Test všech hooks modulu

### 🔧 fix_module.php
- Automatická oprava základních problémů
- Zapnutí modulu v konfiguraci
- Registrace hooks
- Vyčištění cache

### 💰 set_zero_price.php
- Nastavení nulové ceny pro testování
- Možnost obnovení původní ceny
- Kontrola variant produktu

### 🛠️ diagnostic_tools.php
- Hlavní rozcestník všech nástrojů
- Rychlý přehled stavu modulu
- Návod k řešení problémů

## Testovací produkt
- **URL:** https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html
- **ID:** 15336
- **Název:** Bráz klobouk

## Nejčastější problémy a řešení

### ❌ Modul se nezobrazuje
**Příčiny:**
- Modul není povolen v konfiguraci
- Produkt nemá nulovou cenu
- Hooks nejsou správně zaregistrovány
- Chyba v template nebo JavaScript

**Řešení:**
1. Spusťte `fix_module.php`
2. Nastavte nulovou cenu pomocí `set_zero_price.php`
3. Zkontrolujte zobrazení pomocí `check_display.php`

### ⚠️ Modul se zobrazuje, ale nefunguje
**Příčiny:**
- Chyba v JavaScript
- Problém s CSS
- Chybějící soubory

**Řešení:**
1. Zkontrolujte konzoli prohlížeče na chyby
2. Ověřte existenci souborů pomocí `debug_script.php`
3. Zapněte debug mód: `PriceInquiryJS.debugMode = true`

### 🔄 Modul funguje jen někdy
**Příčiny:**
- Cache problém
- Konflikt s jinými moduly
- Problém s variantami produktu

**Řešení:**
1. Vyčistěte cache pomocí `fix_module.php`
2. Zkontrolujte varianty pomocí `test_product.php`
3. Otestujte na různých produktech

## Debug mód JavaScript

Pro zapnutí debug módu v konzoli prohlížeče:
```javascript
PriceInquiryJS.debugMode = true;  // zapnout
PriceInquiryJS.debugMode = false; // vypnout
```

## Kontrola v administraci

1. **Moduly → Vlastní moduly → Cena na dotaz**
2. Zkontrolujte, že je modul:
   - ✅ Nainstalován
   - ✅ Aktivní
   - ✅ Správně nakonfigurován

## Kontakt
Pro další pomoc kontaktujte vývojáře modulu.

---
*Diagnostické nástroje vytvořeny pro rychlé řešení problémů s modulem "Cena na dotaz"*
