<?php
/**
 * Debug problému s hooks API
 * Analyzuje proč PrestaShop API nevidí hooks registrovány v databázi
 */

// Načteme PrestaShop
require_once(dirname(__FILE__) . '/prestashop_loader.php');

echo "<h1>Debug hooks API problému</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; font-size: 12px; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

$moduleName = 'priceinquiry';
$shopId = Context::getContext()->shop->id;

// 1. Základní informace
echo "<div class='section'>";
echo "<h2>1. Základní informace</h2>";

$module = Module::getInstanceByName($moduleName);
if ($module) {
    echo "<strong>Modul ID:</strong> {$module->id}<br>";
    echo "<strong>Modul název:</strong> {$module->name}<br>";
    echo "<strong>Shop ID:</strong> {$shopId}<br>";
    echo "<strong>Modul instance:</strong> " . get_class($module) . "<br>";
} else {
    echo "<span class='error'>❌ Modul nebyl nalezen!</span><br>";
    exit;
}

echo "</div>";

// 2. Kontrola databáze vs API
echo "<div class='section'>";
echo "<h2>2. Porovnání databáze vs API</h2>";

$hooks = ['displayProductPriceBlock', 'displayProductActions', 'displayProductAdditionalInfo', 'displayReassurance', 'displayHeader', 'displayFooter'];

echo "<table>";
echo "<tr><th>Hook</th><th>Hook ID</th><th>V databázi</th><th>API výsledek</th><th>Pozice</th><th>Debug info</th></tr>";

foreach ($hooks as $hookName) {
    $hookId = Hook::getIdByName($hookName);
    
    // Kontrola v databázi
    $dbSql = 'SELECT * FROM `' . _DB_PREFIX_ . 'hook_module` 
              WHERE id_module = ' . (int)$module->id . ' 
              AND id_hook = ' . (int)$hookId . '
              AND id_shop = ' . (int)$shopId;
    
    $dbResult = Db::getInstance()->getRow($dbSql);
    
    // Kontrola přes API
    $apiResult = Hook::isModuleRegisteredOnHook($module, $hookId, $shopId);
    
    // Debug informace
    $debugInfo = [];
    
    // Zkusíme různé způsoby kontroly
    try {
        $hookObj = new Hook($hookId);
        $debugInfo[] = "Hook objekt: OK";
    } catch (Exception $e) {
        $debugInfo[] = "Hook objekt: CHYBA - " . $e->getMessage();
    }
    
    // Zkusíme přímý dotaz
    $directCheck = Db::getInstance()->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'hook_module` WHERE id_module = ' . (int)$module->id . ' AND id_hook = ' . (int)$hookId);
    $debugInfo[] = "Přímý dotaz: {$directCheck}";
    
    echo "<tr>";
    echo "<td>{$hookName}</td>";
    echo "<td>{$hookId}</td>";
    echo "<td>" . ($dbResult ? "<span class='ok'>✅</span>" : "<span class='error'>❌</span>") . "</td>";
    echo "<td>" . ($apiResult ? "<span class='ok'>✅</span>" : "<span class='error'>❌</span>") . "</td>";
    echo "<td>" . ($dbResult ? $dbResult['position'] : '-') . "</td>";
    echo "<td>" . implode('; ', $debugInfo) . "</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// 3. Test Hook::isModuleRegisteredOnHook s debug
echo "<div class='section'>";
echo "<h2>3. Debug Hook::isModuleRegisteredOnHook</h2>";

$testHookName = 'displayProductActions';
$testHookId = Hook::getIdByName($testHookName);

echo "<h3>Test hook: {$testHookName} (ID: {$testHookId})</h3>";

// Zkusíme různé parametry
$tests = [];

try {
    $tests['Základní test'] = Hook::isModuleRegisteredOnHook($module, $testHookId, $shopId);
} catch (Exception $e) {
    $tests['Základní test'] = "CHYBA: " . $e->getMessage();
}

try {
    $tests['String hook ID'] = Hook::isModuleRegisteredOnHook($module, (string)$testHookId, $shopId);
} catch (Exception $e) {
    $tests['String hook ID'] = "CHYBA: " . $e->getMessage();
}

try {
    // Zkusíme s module ID místo objektu
    $tests['Module ID místo objektu'] = Hook::isModuleRegisteredOnHook($module->id, $testHookId, $shopId);
} catch (Exception $e) {
    $tests['Module ID místo objektu'] = "CHYBA: " . $e->getMessage();
}

foreach ($tests as $testName => $result) {
    if (is_string($result) && strpos($result, 'CHYBA:') === 0) {
        echo "<strong>{$testName}:</strong> <span class='error'>{$result}</span><br>";
    } else {
        echo "<strong>{$testName}:</strong> " . ($result ? "<span class='ok'>✅ TRUE</span>" : "<span class='error'>❌ FALSE</span>") . "<br>";
    }
}

// Zkusíme načíst hooks pro modul
echo "<h3>Všechny hooks pro modul:</h3>";
try {
    // Zkusíme přímý SQL dotaz
    $sql = 'SELECT hm.*, h.name as hook_name
            FROM `' . _DB_PREFIX_ . 'hook_module` hm
            LEFT JOIN `' . _DB_PREFIX_ . 'hook` h ON h.id_hook = hm.id_hook
            WHERE hm.id_module = ' . (int)$module->id . '
            AND hm.id_shop = ' . (int)$shopId . '
            ORDER BY h.name';

    $moduleHooks = Db::getInstance()->executeS($sql);
    if ($moduleHooks) {
        echo "<table>";
        echo "<tr><th>Hook Name</th><th>Hook ID</th><th>Position</th></tr>";
        foreach ($moduleHooks as $hook) {
            echo "<tr><td>{$hook['hook_name']}</td><td>{$hook['id_hook']}</td><td>{$hook['position']}</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<span class='error'>❌ Žádné hooks nenalezeny</span><br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>❌ Chyba: " . $e->getMessage() . "</span><br>";
}

echo "</div>";

// 4. Test cache
echo "<div class='section'>";
echo "<h2>4. Test cache problémů</h2>";

// Zkusíme vyčistit cache a znovu testovat
echo "<h3>Před vyčištěním cache:</h3>";
try {
    $beforeCache = Hook::isModuleRegisteredOnHook($module, $testHookId, $shopId);
    echo "<strong>API výsledek:</strong> " . ($beforeCache ? "<span class='ok'>✅ TRUE</span>" : "<span class='error'>❌ FALSE</span>") . "<br>";
} catch (Exception $e) {
    echo "<strong>API výsledek:</strong> <span class='error'>❌ CHYBA: " . $e->getMessage() . "</span><br>";
    $beforeCache = false;
}

// Vyčistíme cache
try {
    Tools::clearSmartyCache();
    Tools::clearXMLCache();
    echo "<span class='ok'>✅ Cache vyčištěna</span><br>";
} catch (Exception $e) {
    echo "<span class='error'>❌ Chyba při čištění cache: " . $e->getMessage() . "</span><br>";
}

echo "<h3>Po vyčištění cache:</h3>";
try {
    $afterCache = Hook::isModuleRegisteredOnHook($module, $testHookId, $shopId);
    echo "<strong>API výsledek:</strong> " . ($afterCache ? "<span class='ok'>✅ TRUE</span>" : "<span class='error'>❌ FALSE</span>") . "<br>";
} catch (Exception $e) {
    echo "<strong>API výsledek:</strong> <span class='error'>❌ CHYBA: " . $e->getMessage() . "</span><br>";
    $afterCache = false;
}

echo "</div>";

// 5. Test přímého volání hook
echo "<div class='section'>";
echo "<h2>5. Test přímého volání hook</h2>";

echo "<h3>Test displayProductActions hook:</h3>";

try {
    // Simulujeme produkt
    $product = new Product(15336);
    if (Validate::isLoadedObject($product)) {
        echo "<span class='ok'>✅ Produkt načten</span><br>";
        
        // Nastavíme kontext
        Context::getContext()->smarty->assign('product', $product);
        
        // Test hook volání
        $params = ['product' => $product];
        $hookOutput = $module->hookDisplayProductActions($params);
        
        if ($hookOutput) {
            echo "<span class='ok'>✅ Hook vrací obsah (" . strlen($hookOutput) . " znaků)</span><br>";
            echo "<details><summary>Zobrazit výstup</summary><pre>" . htmlspecialchars($hookOutput) . "</pre></details>";
        } else {
            echo "<span class='error'>❌ Hook nevrací obsah</span><br>";
        }
        
    } else {
        echo "<span class='error'>❌ Produkt se nepodařilo načíst</span><br>";
    }
    
} catch (Exception $e) {
    echo "<span class='error'>❌ Chyba při volání hook: " . $e->getMessage() . "</span><br>";
}

echo "</div>";

// 6. Test Hook::exec
echo "<div class='section'>";
echo "<h2>6. Test Hook::exec</h2>";

echo "<h3>Test Hook::exec pro displayProductActions:</h3>";

try {
    $product = new Product(15336);
    $params = ['product' => $product];
    
    $execResult = Hook::exec('displayProductActions', $params);
    
    if ($execResult) {
        echo "<span class='ok'>✅ Hook::exec vrací obsah (" . strlen($execResult) . " znaků)</span><br>";
        
        // Zkontrolujeme, jestli obsahuje náš modul
        if (strpos($execResult, 'price-inquiry') !== false) {
            echo "<span class='ok'>✅ Výstup obsahuje náš modul</span><br>";
        } else {
            echo "<span class='error'>❌ Výstup neobsahuje náš modul</span><br>";
        }
        
        echo "<details><summary>Zobrazit výstup Hook::exec</summary><pre>" . htmlspecialchars($execResult) . "</pre></details>";
    } else {
        echo "<span class='error'>❌ Hook::exec nevrací obsah</span><br>";
    }
    
} catch (Exception $e) {
    echo "<span class='error'>❌ Chyba při Hook::exec: " . $e->getMessage() . "</span><br>";
}

echo "</div>";

// 7. Doporučení
echo "<div class='section'>";
echo "<h2>7. Doporučení</h2>";

if ($afterCache) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎉 Hooks fungují po vyčištění cache!</h3>";
    echo "<p>Problém byl v cache. Zkuste nyní test na webu.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>⚠️ Hooks stále nefungují</h3>";
    echo "<p>Možná je problém s PrestaShop core nebo s modulem samotným.</p>";
    echo "<p><strong>Možná řešení:</strong></p>";
    echo "<ul>";
    echo "<li>Restart webového serveru (Apache/Nginx)</li>";
    echo "<li>Kontrola PHP OPcache nastavení</li>";
    echo "<li>Kontrola oprávnění souborů</li>";
    echo "<li>Test na jiném produktu</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<p><strong>Další kroky:</strong></p>";
echo "<ul>";
echo "<li><a href='check_display.php'>Test zobrazení modulu</a></li>";
echo "<li><a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank'>Test na webu</a></li>";
echo "<li><a href='diagnostic_tools.php'>Zpět na nástroje</a></li>";
echo "</ul>";

echo "</div>";

echo "<hr>";
echo "<p><small>Debug dokončen: " . date('Y-m-d H:i:s') . "</small></p>";
?>
