<?php
/**
 * Oprava modulu "Cena na dotaz"
 * Opravuje základní problémy s konfigurac<PERSON> a hooks
 */

// Nastavení pro debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Načteme PrestaShop - zkusíme různé cesty
$prestashop_paths = [
    dirname(__FILE__) . '/../../config/config.inc.php',
    dirname(__FILE__) . '/../../../config/config.inc.php',
    dirname(dirname(dirname(__FILE__))) . '/config/config.inc.php'
];

$config_loaded = false;
foreach ($prestashop_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        require_once(dirname($path) . '/init.php');
        $config_loaded = true;
        break;
    }
}

if (!$config_loaded) {
    die("Chyba: Nepodařilo se načíst PrestaShop konfiguraci. Zkontrolujte cestu k modulu.");
}

echo "<h1>Oprava modulu 'Cena na dotaz'</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

$fixes = [];
$errors = [];

// 1. Kontrola a oprava základní konfigurace
echo "<div class='section'>";
echo "<h2>1. Oprava konfigurace</h2>";

$module = Module::getInstanceByName('priceinquiry');
if (!$module) {
    $errors[] = "Modul 'priceinquiry' nebyl nalezen!";
    echo "<span class='error'>❌ Modul nenalezen!</span><br>";
} else {
    echo "<span class='ok'>✅ Modul nalezen</span><br>";
    
    // Zapneme modul pokud není zapnutý
    if (!Configuration::get('PRICE_INQUIRY_ENABLED')) {
        Configuration::updateValue('PRICE_INQUIRY_ENABLED', 1);
        $fixes[] = "✅ Modul byl zapnut";
        echo "<span class='ok'>✅ Modul zapnut</span><br>";
    } else {
        echo "<span class='info'>ℹ️ Modul už je zapnutý</span><br>";
    }
    
    // Nastavíme výchozí hodnoty pokud chybí
    $defaultConfigs = [
        'PRICE_INQUIRY_EMAIL' => Configuration::get('PS_SHOP_EMAIL'),
        'PRICE_INQUIRY_BUTTON_TEXT' => 'Zjistit cenu',
        'PRICE_INQUIRY_PRICE_TEXT' => 'Cena na dotaz',
        'PRICE_INQUIRY_SEND_ADMIN_EMAIL' => 1,
        'PRICE_INQUIRY_SEND_CUSTOMER_EMAIL' => 1
    ];
    
    foreach ($defaultConfigs as $key => $defaultValue) {
        $currentValue = Configuration::get($key);
        if (empty($currentValue)) {
            Configuration::updateValue($key, $defaultValue);
            $fixes[] = "✅ Nastavena výchozí hodnota pro {$key}";
            echo "<span class='ok'>✅ Nastavena výchozí hodnota pro {$key}</span><br>";
        }
    }
}

echo "</div>";

// 2. Oprava hooks
echo "<div class='section'>";
echo "<h2>2. Oprava hooks</h2>";

if ($module) {
    $hooks = [
        'displayProductPriceBlock',
        'displayProductActions', 
        'displayProductAdditionalInfo',
        'displayReassurance',
        'displayHeader',
        'displayFooter'
    ];
    
    foreach ($hooks as $hookName) {
        $hookId = Hook::getIdByName($hookName);
        if ($hookId) {
            $isRegistered = Hook::isModuleRegisteredOnHook($module, $hookId, Context::getContext()->shop->id);
            if (!$isRegistered) {
                if ($module->registerHook($hookName)) {
                    $fixes[] = "✅ Hook {$hookName} byl zaregistrován";
                    echo "<span class='ok'>✅ Hook {$hookName} zaregistrován</span><br>";
                } else {
                    $errors[] = "❌ Nepodařilo se zaregistrovat hook {$hookName}";
                    echo "<span class='error'>❌ Chyba při registraci hook {$hookName}</span><br>";
                }
            } else {
                echo "<span class='info'>ℹ️ Hook {$hookName} už je zaregistrován</span><br>";
            }
        }
    }
}

echo "</div>";

// 3. Vyčištění cache
echo "<div class='section'>";
echo "<h2>3. Vyčištění cache</h2>";

try {
    // Vyčistíme Smarty cache
    Tools::clearSmartyCache();
    $fixes[] = "✅ Smarty cache vyčištěna";
    echo "<span class='ok'>✅ Smarty cache vyčištěna</span><br>";
    
    // Vyčistíme XML cache
    Tools::clearXMLCache();
    $fixes[] = "✅ XML cache vyčištěna";
    echo "<span class='ok'>✅ XML cache vyčištěna</span><br>";
    
} catch (Exception $e) {
    $errors[] = "❌ Chyba při čištění cache: " . $e->getMessage();
    echo "<span class='error'>❌ Chyba při čištění cache: " . $e->getMessage() . "</span><br>";
}

echo "</div>";

// 4. Test produktu
echo "<div class='section'>";
echo "<h2>4. Test produktu</h2>";

$productId = 15336;
$product = new Product($productId, false, Context::getContext()->language->id);

if (Validate::isLoadedObject($product)) {
    $price = Product::getPriceStatic($productId, true, null, 2, null, false, true);
    echo "<strong>Produkt:</strong> " . htmlspecialchars($product->name) . "<br>";
    echo "<strong>Aktuální cena:</strong> " . number_format($price, 2, ',', ' ') . " Kč<br>";
    
    if ($price > 0) {
        echo "<span class='warning'>⚠️ Produkt má nenulovou cenu - pro test nastavte cenu na 0 Kč</span><br>";
    } else {
        echo "<span class='ok'>✅ Produkt má nulovou cenu - modul by se měl zobrazit</span><br>";
    }
} else {
    echo "<span class='error'>❌ Testovací produkt s ID {$productId} nebyl nalezen</span><br>";
}

echo "</div>";

// 5. Shrnutí
echo "<div class='section'>";
echo "<h2>5. Shrnutí</h2>";

if (!empty($fixes)) {
    echo "<h3>Provedené opravy:</h3>";
    echo "<ul>";
    foreach ($fixes as $fix) {
        echo "<li>{$fix}</li>";
    }
    echo "</ul>";
}

if (!empty($errors)) {
    echo "<h3>Chyby:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>{$error}</li>";
    }
    echo "</ul>";
}

if (empty($errors)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎉 Oprava dokončena!</h3>";
    echo "<p>Modul by nyní měl fungovat správně.</p>";
    echo "</div>";
}

echo "</div>";

echo "<hr>";
echo "<p><a href='debug_script.php'>Spustit diagnostiku</a></p>";
echo "<p><small>Oprava dokončena: " . date('Y-m-d H:i:s') . "</small></p>";
?>
