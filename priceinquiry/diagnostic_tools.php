<?php
/**
 * Diagnostické nástroje pro modul "Cena na dotaz"
 * Rozcestník pro všechny diagnostické a opravné skripty
 */

// Nastavení pro debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Načteme PrestaShop - zkusíme různé cesty
$prestashop_paths = [
    dirname(__FILE__) . '/../../config/config.inc.php',
    dirname(__FILE__) . '/../../../config/config.inc.php',
    dirname(dirname(dirname(__FILE__))) . '/config/config.inc.php'
];

$config_loaded = false;
foreach ($prestashop_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        require_once(dirname($path) . '/init.php');
        $config_loaded = true;
        break;
    }
}

if (!$config_loaded) {
    die("Chyba: Nepodařilo se načíst PrestaShop konfiguraci. Zkontrolujte cestu k modulu.");
}

echo "<h1>Diagnostické nástroje - Modul 'Cena na dotaz'</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
    .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f8f9fa; }
    .tool-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
    .tool-card { background: white; border: 1px solid #ddd; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
    .tool-card h3 { margin-top: 0; color: #333; }
    .tool-card p { color: #666; margin: 10px 0; }
    .button { background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px 5px 5px 0; font-weight: bold; }
    .button:hover { background: #005a87; color: white; text-decoration: none; }
    .button.success { background: #28a745; }
    .button.success:hover { background: #1e7e34; }
    .button.warning { background: #ffc107; color: #212529; }
    .button.warning:hover { background: #e0a800; }
    .button.danger { background: #dc3545; }
    .button.danger:hover { background: #c82333; }
    .status-bar { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
    .status-item { display: inline-block; margin-right: 20px; }
</style>";

echo "<div class='container'>";

// Rychlý status check
echo "<div class='section'>";
echo "<h2>🔍 Rychlý přehled stavu</h2>";

$module = Module::getInstanceByName('priceinquiry');
$enabled = Configuration::get('PRICE_INQUIRY_ENABLED');
$productId = 15336;
$product = new Product($productId, false, Context::getContext()->language->id);
$price = Validate::isLoadedObject($product) ? Product::getPriceStatic($productId, true, null, 2, null, false, true) : -1;

echo "<div class='status-bar'>";
echo "<div class='status-item'>";
echo "<strong>Modul:</strong> " . ($module ? "<span class='ok'>✅ Nalezen</span>" : "<span class='error'>❌ Nenalezen</span>");
echo "</div>";

echo "<div class='status-item'>";
echo "<strong>Povolen:</strong> " . ($enabled ? "<span class='ok'>✅ Ano</span>" : "<span class='error'>❌ Ne</span>");
echo "</div>";

echo "<div class='status-item'>";
echo "<strong>Test produkt:</strong> " . (Validate::isLoadedObject($product) ? "<span class='ok'>✅ Nalezen</span>" : "<span class='error'>❌ Nenalezen</span>");
echo "</div>";

if (Validate::isLoadedObject($product)) {
    echo "<div class='status-item'>";
    echo "<strong>Cena:</strong> " . ($price <= 0 ? "<span class='ok'>✅ Nulová ({$price} Kč)</span>" : "<span class='warning'>⚠️ Nenulová ({$price} Kč)</span>");
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Nástroje
echo "<div class='section'>";
echo "<h2>🛠️ Diagnostické nástroje</h2>";

echo "<div class='tool-grid'>";

// Úplná diagnostika
echo "<div class='tool-card'>";
echo "<h3>🔍 Úplná diagnostika</h3>";
echo "<p>Kompletní kontrola modulu, konfigurace, hooks, souborů a testovacího produktu.</p>";
echo "<a href='debug_script.php' class='button'>Spustit diagnostiku</a>";
echo "</div>";

// Rychlá kontrola zobrazení
echo "<div class='tool-card'>";
echo "<h3>👁️ Kontrola zobrazení</h3>";
echo "<p>Rychlá kontrola, jestli se modul zobrazuje na stránce produktu. Simuluje návštěvu stránky.</p>";
echo "<a href='check_display.php' class='button success'>Zkontrolovat zobrazení</a>";
echo "</div>";

// Test konkrétního produktu
echo "<div class='tool-card'>";
echo "<h3>🎯 Test produktu</h3>";
echo "<p>Detailní analýza konkrétního produktu (ID 15336) včetně cen variant a výstupu hooks.</p>";
echo "<a href='test_product.php' class='button'>Test produktu</a>";
echo "</div>";

// Oprava modulu
echo "<div class='tool-card'>";
echo "<h3>🔧 Oprava modulu</h3>";
echo "<p>Automatická oprava základních problémů - konfigurace, hooks, cache.</p>";
echo "<a href='fix_module.php' class='button warning'>Opravit modul</a>";
echo "</div>";

// Nastavení testovací ceny
echo "<div class='tool-card'>";
echo "<h3>💰 Nastavení testovací ceny</h3>";
echo "<p>Nastavení nulové ceny pro testovací produkt. Umožňuje rychle otestovat funkčnost modulu.</p>";
echo "<a href='set_zero_price.php' class='button danger'>Nastavit testovací cenu</a>";
echo "</div>";

// Přímý test na webu
echo "<div class='tool-card'>";
echo "<h3>🌐 Test na webu</h3>";
echo "<p>Přímý odkaz na testovací produkt na webu. Otevře se v novém okně.</p>";
echo "<a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank' class='button'>Otevřít produkt</a>";
echo "</div>";

echo "</div>";
echo "</div>";

// Rychlé akce
echo "<div class='section'>";
echo "<h2>⚡ Rychlé akce</h2>";

echo "<p>Nejčastější akce pro rychlé řešení problémů:</p>";

if (!$enabled) {
    echo "<a href='fix_module.php' class='button warning'>🔧 Zapnout modul</a>";
}

if ($price > 0) {
    echo "<a href='set_zero_price.php' class='button danger'>💰 Nastavit nulovou cenu</a>";
}

echo "<a href='check_display.php' class='button success'>👁️ Zkontrolovat zobrazení</a>";
echo "<a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank' class='button'>🌐 Test na webu</a>";

echo "</div>";

// Návod
echo "<div class='section'>";
echo "<h2>📋 Návod k řešení problémů</h2>";

echo "<ol>";
echo "<li><strong>Spusťte úplnou diagnostiku</strong> - zjistíte přesný stav modulu</li>";
echo "<li><strong>Opravte základní problémy</strong> - použijte script pro opravu modulu</li>";
echo "<li><strong>Nastavte testovací cenu</strong> - pro test nastavte cenu produktu na 0 Kč</li>";
echo "<li><strong>Zkontrolujte zobrazení</strong> - ověřte, že modul vrací správný výstup</li>";
echo "<li><strong>Otestujte na webu</strong> - navštivte stránku produktu a ověřte funkčnost</li>";
echo "</ol>";

echo "<p><strong>Poznámka:</strong> Pokud problémy přetrvávají, zkontrolujte také:</p>";
echo "<ul>";
echo "<li>Zda je modul správně nainstalován v administraci</li>";
echo "<li>Zda nejsou konflikty s jinými moduly</li>";
echo "<li>Zda jsou správně nastavené oprávnění souborů</li>";
echo "<li>Zda není problém s cache nebo CDN</li>";
echo "</ul>";

echo "</div>";

echo "</div>";

echo "<hr>";
echo "<p><small>Diagnostické nástroje vytvořeny: " . date('Y-m-d H:i:s') . "</small></p>";
?>
