<?php
/**
 * <PERSON>ag<PERSON><PERSON><PERSON> script pro modul "Cena na dotaz"
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, proč se možnost "cena na dotaz" nezobrazuje
 */

// Nastavení pro debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Načteme PrestaShop
require_once(dirname(__FILE__) . '/../../config/config.inc.php');
require_once(dirname(__FILE__) . '/../../init.php');

echo "<h1>Diagnostika modulu 'Cena na dotaz'</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

// 1. Kontrola existence a instalace modulu
echo "<div class='section'>";
echo "<h2>1. Kontrola modulu</h2>";

$module = Module::getInstanceByName('priceinquiry');
if (!$module) {
    echo "<span class='error'>❌ Modul 'priceinquiry' nebyl nalezen!</span><br>";
    exit;
} else {
    echo "<span class='ok'>✅ Modul nalezen</span><br>";
}

if (!Module::isInstalled('priceinquiry')) {
    echo "<span class='error'>❌ Modul není nainstalován!</span><br>";
    exit;
} else {
    echo "<span class='ok'>✅ Modul je nainstalován</span><br>";
}

if (!Module::isEnabled('priceinquiry')) {
    echo "<span class='error'>❌ Modul není aktivní!</span><br>";
} else {
    echo "<span class='ok'>✅ Modul je aktivní</span><br>";
}

echo "<strong>Verze modulu:</strong> " . $module->version . "<br>";
echo "</div>";

// 2. Kontrola konfigurace
echo "<div class='section'>";
echo "<h2>2. Konfigurace modulu</h2>";

$configs = [
    'PRICE_INQUIRY_ENABLED' => 'Modul povolen',
    'PRICE_INQUIRY_EMAIL' => 'E-mail pro dotazy',
    'PRICE_INQUIRY_BUTTON_TEXT' => 'Text tlačítka',
    'PRICE_INQUIRY_PRICE_TEXT' => 'Text místo ceny',
    'PRICE_INQUIRY_SEND_ADMIN_EMAIL' => 'Poslat admin e-mail',
    'PRICE_INQUIRY_SEND_CUSTOMER_EMAIL' => 'Poslat zákazník e-mail'
];

echo "<table>";
echo "<tr><th>Konfigurace</th><th>Hodnota</th><th>Status</th></tr>";

foreach ($configs as $key => $label) {
    $value = Configuration::get($key);
    $status = '';
    
    if ($key === 'PRICE_INQUIRY_ENABLED') {
        $status = $value ? "<span class='ok'>✅ Zapnuto</span>" : "<span class='error'>❌ Vypnuto</span>";
    } else {
        $status = !empty($value) ? "<span class='ok'>✅ OK</span>" : "<span class='warning'>⚠️ Prázdné</span>";
    }
    
    echo "<tr>";
    echo "<td>{$label}</td>";
    echo "<td>" . htmlspecialchars($value) . "</td>";
    echo "<td>{$status}</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// 3. Kontrola hooks
echo "<div class='section'>";
echo "<h2>3. Kontrola hooks</h2>";

$hooks = [
    'displayProductPriceBlock',
    'displayProductActions',
    'displayProductAdditionalInfo',
    'displayReassurance',
    'displayHeader',
    'displayFooter'
];

echo "<table>";
echo "<tr><th>Hook</th><th>Status</th></tr>";

foreach ($hooks as $hookName) {
    $hook = Hook::getIdByName($hookName);
    if ($hook) {
        $isRegistered = Hook::isModuleRegisteredOnHook($module, $hook, Context::getContext()->shop->id);
        $status = $isRegistered ? "<span class='ok'>✅ Registrován</span>" : "<span class='error'>❌ Není registrován</span>";
    } else {
        $status = "<span class='error'>❌ Hook neexistuje</span>";
    }
    
    echo "<tr>";
    echo "<td>{$hookName}</td>";
    echo "<td>{$status}</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// 4. Kontrola testovacího produktu
echo "<div class='section'>";
echo "<h2>4. Test produktu s ID 15336</h2>";

$productId = 15336;
$product = new Product($productId, false, Context::getContext()->language->id);

if (!Validate::isLoadedObject($product)) {
    echo "<span class='error'>❌ Produkt s ID {$productId} nebyl nalezen!</span><br>";
} else {
    echo "<span class='ok'>✅ Produkt nalezen: " . htmlspecialchars($product->name) . "</span><br>";
    
    // Získáme cenu produktu
    $price = Product::getPriceStatic($productId, true, null, 2, null, false, true);
    echo "<strong>Cena produktu:</strong> " . number_format($price, 2, ',', ' ') . " Kč<br>";
    
    if ($price <= 0) {
        echo "<span class='ok'>✅ Cena je nulová - modul by se měl zobrazit</span><br>";
    } else {
        echo "<span class='warning'>⚠️ Cena není nulová - modul se nezobrazí</span><br>";
    }
    
    // Zkontrolujeme varianty
    $attributes = Product::getProductAttributesIds($productId);
    if ($attributes && count($attributes) > 0) {
        echo "<strong>Varianty produktu:</strong><br>";
        echo "<table>";
        echo "<tr><th>ID varianty</th><th>Cena</th><th>Status</th></tr>";
        
        foreach ($attributes as $attr) {
            $attrPrice = Product::getPriceStatic($productId, true, $attr['id_product_attribute'], 2, null, false, true);
            $status = $attrPrice <= 0 ? "<span class='ok'>✅ Nulová cena</span>" : "<span class='info'>Má cenu</span>";
            
            echo "<tr>";
            echo "<td>{$attr['id_product_attribute']}</td>";
            echo "<td>" . number_format($attrPrice, 2, ',', ' ') . " Kč</td>";
            echo "<td>{$status}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<strong>Produkt nemá varianty</strong><br>";
    }
}

echo "</div>";

// 5. Kontrola souborů modulu
echo "<div class='section'>";
echo "<h2>5. Kontrola souborů</h2>";

$files = [
    'priceinquiry.php' => 'Hlavní soubor modulu',
    'views/js/front.js' => 'JavaScript frontend',
    'views/css/front.css' => 'CSS frontend',
    'views/templates/front/inquiry_block_detail.tpl' => 'Template pro detail produktu',
    'views/templates/front/inquiry_modal.tpl' => 'Template pro modal',
    'controllers/front/inquiry.php' => 'Frontend controller'
];

echo "<table>";
echo "<tr><th>Soubor</th><th>Status</th><th>Velikost</th></tr>";

foreach ($files as $file => $description) {
    $fullPath = _PS_MODULE_DIR_ . 'priceinquiry/' . $file;
    if (file_exists($fullPath)) {
        $size = filesize($fullPath);
        $status = "<span class='ok'>✅ Existuje</span>";
    } else {
        $size = 0;
        $status = "<span class='error'>❌ Neexistuje</span>";
    }
    
    echo "<tr>";
    echo "<td>{$file}<br><small>{$description}</small></td>";
    echo "<td>{$status}</td>";
    echo "<td>" . number_format($size) . " B</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// 6. Test hook výstupu
echo "<div class='section'>";
echo "<h2>6. Test hook výstupu</h2>";

if (Validate::isLoadedObject($product)) {
    // Simulujeme kontext produktu
    Context::getContext()->controller = new FrontController();
    Context::getContext()->controller->php_self = 'product';
    
    // Test displayProductActions hook
    $params = ['product' => $product];
    $hookOutput = $module->hookDisplayProductActions($params);
    
    echo "<h3>Výstup hookDisplayProductActions:</h3>";
    if (!empty($hookOutput)) {
        echo "<span class='ok'>✅ Hook vrací obsah</span><br>";
        echo "<pre>" . htmlspecialchars($hookOutput) . "</pre>";
    } else {
        echo "<span class='error'>❌ Hook nevrací žádný obsah</span><br>";
        
        // Debug informace
        echo "<strong>Debug informace:</strong><br>";
        echo "- php_self: " . Context::getContext()->controller->php_self . "<br>";
        echo "- PRICE_INQUIRY_ENABLED: " . (Configuration::get('PRICE_INQUIRY_ENABLED') ? 'true' : 'false') . "<br>";
        echo "- Cena produktu: " . $price . "<br>";
    }
}

echo "</div>";

// 7. Doporučení
echo "<div class='section'>";
echo "<h2>7. Doporučení a řešení</h2>";

$recommendations = [];

if (!Configuration::get('PRICE_INQUIRY_ENABLED')) {
    $recommendations[] = "❌ <strong>Zapněte modul</strong> v konfiguraci (PRICE_INQUIRY_ENABLED = 1)";
}

if ($price > 0) {
    $recommendations[] = "⚠️ <strong>Testovací produkt má nenulovou cenu</strong> - nastavte cenu na 0 Kč pro test";
}

if (!Hook::isModuleRegisteredOnHook($module, Hook::getIdByName('displayProductActions'), Context::getContext()->shop->id)) {
    $recommendations[] = "❌ <strong>Znovu zaregistrujte hook displayProductActions</strong>";
}

if (empty($recommendations)) {
    echo "<span class='ok'>✅ Vše vypadá v pořádku!</span>";
} else {
    echo "<ul>";
    foreach ($recommendations as $rec) {
        echo "<li>{$rec}</li>";
    }
    echo "</ul>";
}

echo "</div>";

// 8. Rychlé opravy
echo "<div class='section'>";
echo "<h2>8. Rychlé opravy</h2>";

echo "<p>Pokud chcete rychle opravit problémy, můžete spustit tyto skripty:</p>";
echo "<ul>";
echo "<li><a href='quick_fix.php' target='_blank'>quick_fix.php</a> - Rychlá oprava základních problémů</li>";
echo "<li><a href='complete_fix.php' target='_blank'>complete_fix.php</a> - Kompletní reinstalace modulu</li>";
echo "</ul>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>9. Test URL</h2>";
echo "<p>Pro test navštivte: <a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank'>https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html</a></p>";
echo "</div>";

echo "<hr>";
echo "<p><small>Diagnostika dokončena: " . date('Y-m-d H:i:s') . "</small></p>";
?>
