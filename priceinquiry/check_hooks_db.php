<?php
/**
 * Kontrola hooks v databázi
 * Zobrazí detailní informace o registraci hooks v databázi
 */

// Načteme PrestaShop
require_once(dirname(__FILE__) . '/prestashop_loader.php');

echo "<h1>Kontrola hooks v databázi</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; font-size: 12px; }
</style>";

$module = Module::getInstanceByName('priceinquiry');
if (!$module) {
    echo "<span class='error'>❌ Modul 'priceinquiry' nebyl nalezen!</span>";
    exit;
}

$moduleId = $module->id;
$shopId = Context::getContext()->shop->id;

echo "<div class='section'>";
echo "<h2>Základní informace</h2>";
echo "<strong>Modul:</strong> {$module->name} (ID: {$moduleId})<br>";
echo "<strong>Shop:</strong> {$shopId}<br>";
echo "<strong>Databáze prefix:</strong> " . _DB_PREFIX_ . "<br>";
echo "</div>";

// Kontrola tabulky hook_module
echo "<div class='section'>";
echo "<h2>Všechny registrace modulu v hook_module</h2>";

$sql = 'SELECT hm.*, h.name as hook_name 
        FROM `' . _DB_PREFIX_ . 'hook_module` hm
        LEFT JOIN `' . _DB_PREFIX_ . 'hook` h ON h.id_hook = hm.id_hook
        WHERE hm.id_module = ' . (int)$moduleId . '
        ORDER BY hm.id_hook, hm.id_shop';

$results = Db::getInstance()->executeS($sql);

if ($results) {
    echo "<table>";
    echo "<tr><th>Hook ID</th><th>Hook Name</th><th>Shop ID</th><th>Position</th></tr>";
    
    foreach ($results as $row) {
        echo "<tr>";
        echo "<td>{$row['id_hook']}</td>";
        echo "<td>{$row['hook_name']}</td>";
        echo "<td>{$row['id_shop']}</td>";
        echo "<td>{$row['position']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<span class='error'>❌ Žádné registrace nenalezeny v databázi!</span><br>";
}

echo "</div>";

// Kontrola konkrétních hooks
echo "<div class='section'>";
echo "<h2>Kontrola požadovaných hooks</h2>";

$requiredHooks = [
    'displayProductPriceBlock',
    'displayProductActions', 
    'displayProductAdditionalInfo',
    'displayReassurance',
    'displayHeader',
    'displayFooter'
];

echo "<table>";
echo "<tr><th>Hook Name</th><th>Hook ID</th><th>Registrován v DB</th><th>PrestaShop API</th><th>Pozice</th></tr>";

foreach ($requiredHooks as $hookName) {
    $hookId = Hook::getIdByName($hookName);
    
    // Kontrola v databázi
    $dbSql = 'SELECT * FROM `' . _DB_PREFIX_ . 'hook_module` 
              WHERE id_module = ' . (int)$moduleId . ' 
              AND id_hook = ' . (int)$hookId . '
              AND id_shop = ' . (int)$shopId;
    
    $dbResult = Db::getInstance()->getRow($dbSql);
    
    // Kontrola přes PrestaShop API
    $apiResult = Hook::isModuleRegisteredOnHook($module, $hookId, $shopId);
    
    echo "<tr>";
    echo "<td>{$hookName}</td>";
    echo "<td>{$hookId}</td>";
    echo "<td>" . ($dbResult ? "<span class='ok'>✅ Ano</span>" : "<span class='error'>❌ Ne</span>") . "</td>";
    echo "<td>" . ($apiResult ? "<span class='ok'>✅ Ano</span>" : "<span class='error'>❌ Ne</span>") . "</td>";
    echo "<td>" . ($dbResult ? $dbResult['position'] : '-') . "</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Kontrola tabulky hook
echo "<div class='section'>";
echo "<h2>Dostupné hooks v systému</h2>";

$hooksSql = 'SELECT * FROM `' . _DB_PREFIX_ . 'hook` WHERE name IN ("' . implode('","', $requiredHooks) . '") ORDER BY name';
$hooksResults = Db::getInstance()->executeS($hooksSql);

if ($hooksResults) {
    echo "<table>";
    echo "<tr><th>ID</th><th>Name</th><th>Title</th><th>Description</th></tr>";
    
    foreach ($hooksResults as $hook) {
        echo "<tr>";
        echo "<td>{$hook['id_hook']}</td>";
        echo "<td>{$hook['name']}</td>";
        echo "<td>" . htmlspecialchars($hook['title']) . "</td>";
        echo "<td>" . htmlspecialchars($hook['description']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<span class='error'>❌ Žádné hooks nenalezeny!</span><br>";
}

echo "</div>";

// SQL příkazy pro manuální opravu
echo "<div class='section'>";
echo "<h2>SQL příkazy pro manuální opravu</h2>";

echo "<p>Pokud hooks nejsou registrovány, můžete použít tyto SQL příkazy:</p>";

echo "<h3>1. Smazání všech registrací modulu:</h3>";
echo "<pre>DELETE FROM `" . _DB_PREFIX_ . "hook_module` WHERE id_module = {$moduleId};</pre>";

echo "<h3>2. Registrace hooks:</h3>";
$position = 1;
foreach ($requiredHooks as $hookName) {
    $hookId = Hook::getIdByName($hookName);
    if ($hookId) {
        echo "<pre>INSERT INTO `" . _DB_PREFIX_ . "hook_module` (id_module, id_shop, id_hook, position) VALUES ({$moduleId}, {$shopId}, {$hookId}, {$position});</pre>";
        $position++;
    }
}

echo "<h3>3. Vyčištění cache:</h3>";
echo "<pre>
-- Smazání cache souborů (spustit v PHP)
Tools::clearSmartyCache();
Tools::clearXMLCache();
</pre>";

echo "</div>";

// Diagnostické informace
echo "<div class='section'>";
echo "<h2>Diagnostické informace</h2>";

echo "<h3>Databázové připojení:</h3>";
try {
    $testQuery = 'SELECT COUNT(*) as count FROM `' . _DB_PREFIX_ . 'hook`';
    $count = Db::getInstance()->getValue($testQuery);
    echo "<span class='ok'>✅ Databáze funguje (hooks v systému: {$count})</span><br>";
} catch (Exception $e) {
    echo "<span class='error'>❌ Chyba databáze: " . $e->getMessage() . "</span><br>";
}

echo "<h3>Oprávnění:</h3>";
try {
    $testInsert = 'SELECT 1'; // Jednoduchý test
    Db::getInstance()->getValue($testInsert);
    echo "<span class='ok'>✅ Databázové dotazy fungují</span><br>";
} catch (Exception $e) {
    echo "<span class='error'>❌ Problém s oprávněními: " . $e->getMessage() . "</span><br>";
}

echo "</div>";

echo "<hr>";
echo "<p><strong>Akce:</strong></p>";
echo "<ul>";
echo "<li><a href='force_register_hooks.php'>Vynucená registrace hooks</a></li>";
echo "<li><a href='debug_script.php'>Spustit diagnostiku</a></li>";
echo "<li><a href='diagnostic_tools.php'>Zpět na nástroje</a></li>";
echo "</ul>";

echo "<p><small>Kontrola dokončena: " . date('Y-m-d H:i:s') . "</small></p>";
?>
