<?php
/**
 * Debug konkrétně hookDisplayProductActions
 * Zjist<PERSON> proč hook nevrací obsah
 */

// Načteme PrestaShop
require_once(dirname(__FILE__) . '/prestashop_loader.php');

echo "<h1>Debug hookDisplayProductActions</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; font-size: 12px; }
    .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px; }
</style>";

$moduleName = 'priceinquiry';
$productId = 15336;

// 1. Načtení modulu
echo "<div class='section'>";
echo "<h2>1. Načtení modulu</h2>";

$module = Module::getInstanceByName($moduleName);
if ($module) {
    echo "<span class='ok'>✅ Modul načten: {$module->displayName}</span><br>";
} else {
    echo "<span class='error'>❌ Modul nebyl nalezen!</span><br>";
    exit;
}

echo "</div>";

// 2. Načtení produktu
echo "<div class='section'>";
echo "<h2>2. Načtení produktu</h2>";

$product = new Product($productId, false, Context::getContext()->language->id);
if (Validate::isLoadedObject($product)) {
    echo "<span class='ok'>✅ Produkt načten</span><br>";
    echo "<strong>ID:</strong> {$product->id}<br>";

    // Správné načtení názvu
    $productName = is_array($product->name) ?
                   (isset($product->name[Context::getContext()->language->id]) ?
                    $product->name[Context::getContext()->language->id] :
                    (isset($product->name[1]) ? $product->name[1] : 'Neznámý')) :
                   $product->name;
    echo "<strong>Název:</strong> {$productName}<br>";

    echo "<strong>Reference:</strong> {$product->reference}<br>";
    echo "<strong>Cena:</strong> " . Product::getPriceStatic($product->id) . " Kč<br>";
    echo "<strong>Aktivní:</strong> " . ($product->active ? 'Ano' : 'Ne') . "<br>";
} else {
    echo "<span class='error'>❌ Produkt se nepodařilo načíst!</span><br>";
    exit;
}

echo "</div>";

// 3. Test podmínek modulu
echo "<div class='section'>";
echo "<h2>3. Test podmínek modulu</h2>";

// Zkontrolujeme, jestli modul má metodu pro kontrolu zobrazení
if (method_exists($module, 'shouldShowBlock')) {
    $shouldShow = $module->shouldShowBlock($product);
    echo "<strong>shouldShowBlock():</strong> " . ($shouldShow ? "<span class='ok'>✅ TRUE</span>" : "<span class='error'>❌ FALSE</span>") . "<br>";
} else {
    echo "<strong>shouldShowBlock():</strong> <span class='info'>ℹ️ Metoda neexistuje</span><br>";
}

// Zkontrolujeme cenu produktu
$price = Product::getPriceStatic($product->id);
echo "<strong>Cena produktu:</strong> {$price} Kč<br>";
echo "<strong>Cena je nulová:</strong> " . ($price == 0 ? "<span class='ok'>✅ Ano</span>" : "<span class='error'>❌ Ne</span>") . "<br>";

// Zkontrolujeme konfiguraci
$enabled = Configuration::get('PRICE_INQUIRY_ENABLED');
echo "<strong>Modul povolen:</strong> " . ($enabled ? "<span class='ok'>✅ Ano</span>" : "<span class='error'>❌ Ne</span>") . "<br>";

// Zkontrolujeme kontext
echo "<h3>Kontrola kontextu:</h3>";
echo "<strong>Controller php_self:</strong> " . (Context::getContext()->controller->php_self ?? 'není nastaveno') . "<br>";
echo "<strong>Language ID:</strong> " . Context::getContext()->language->id . "<br>";
echo "<strong>Shop ID:</strong> " . Context::getContext()->shop->id . "<br>";

echo "</div>";

// 4. Debug hookDisplayProductActions krok za krokem
echo "<div class='section'>";
echo "<h2>4. Debug hookDisplayProductActions</h2>";

echo "<h3>Krok 1: Kontrola metody</h3>";
if (method_exists($module, 'hookDisplayProductActions')) {
    echo "<span class='ok'>✅ Metoda hookDisplayProductActions existuje</span><br>";
} else {
    echo "<span class='error'>❌ Metoda hookDisplayProductActions neexistuje!</span><br>";
    exit;
}

echo "<h3>Krok 2: Příprava parametrů</h3>";
$params = ['product' => $product];
echo "<strong>Parametry:</strong><br>";
echo "<pre>" . print_r($params, true) . "</pre>";

echo "<h3>Krok 3: Nastavení kontextu</h3>";
try {
    // Nastavíme controller context pro product stránku
    Context::getContext()->controller->php_self = 'product';
    echo "<span class='ok'>✅ Controller php_self nastaven na 'product'</span><br>";

    Context::getContext()->smarty->assign('product', $product);
    echo "<span class='ok'>✅ Produkt přiřazen do Smarty</span><br>";
} catch (Exception $e) {
    echo "<span class='error'>❌ Chyba při přiřazení do Smarty: " . $e->getMessage() . "</span><br>";
}

echo "<h3>Krok 4: Volání hook s debug výstupem</h3>";

// Zapneme error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    echo "<strong>Volám hookDisplayProductActions...</strong><br>";
    
    // Zachytíme všechny výstupy
    ob_start();
    $hookOutput = $module->hookDisplayProductActions($params);
    $debugOutput = ob_get_clean();
    
    echo "<strong>Debug výstup během volání:</strong><br>";
    if ($debugOutput) {
        echo "<pre>" . htmlspecialchars($debugOutput) . "</pre>";
    } else {
        echo "<span class='info'>ℹ️ Žádný debug výstup</span><br>";
    }
    
    echo "<strong>Návratová hodnota:</strong><br>";
    if ($hookOutput === null) {
        echo "<span class='error'>❌ NULL</span><br>";
    } elseif ($hookOutput === false) {
        echo "<span class='error'>❌ FALSE</span><br>";
    } elseif ($hookOutput === '') {
        echo "<span class='error'>❌ Prázdný string</span><br>";
    } elseif (is_string($hookOutput)) {
        echo "<span class='ok'>✅ String (" . strlen($hookOutput) . " znaků)</span><br>";
        echo "<details><summary>Zobrazit obsah</summary><pre>" . htmlspecialchars($hookOutput) . "</pre></details>";
    } else {
        echo "<span class='warning'>⚠️ Jiný typ: " . gettype($hookOutput) . "</span><br>";
        echo "<pre>" . print_r($hookOutput, true) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<span class='error'>❌ Chyba při volání: " . $e->getMessage() . "</span><br>";
    echo "<strong>Stack trace:</strong><br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch (Error $e) {
    echo "<span class='error'>❌ Fatal error: " . $e->getMessage() . "</span><br>";
    echo "<strong>Stack trace:</strong><br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div>";

// 5. Test template souboru
echo "<div class='section'>";
echo "<h2>5. Test template souborů</h2>";

$templatePath = _PS_MODULE_DIR_ . $moduleName . '/views/templates/front/inquiry_block_detail.tpl';
echo "<strong>Template path:</strong> {$templatePath}<br>";

if (file_exists($templatePath)) {
    echo "<span class='ok'>✅ Template soubor existuje</span><br>";
    echo "<strong>Velikost:</strong> " . filesize($templatePath) . " bytů<br>";
    echo "<strong>Oprávnění:</strong> " . substr(sprintf('%o', fileperms($templatePath)), -4) . "<br>";
    
    // Zkusíme načíst obsah
    $templateContent = file_get_contents($templatePath);
    if ($templateContent) {
        echo "<span class='ok'>✅ Template lze načíst (" . strlen($templateContent) . " znaků)</span><br>";
        
        // Zkontrolujeme klíčové části
        if (strpos($templateContent, 'price-inquiry-block-detail') !== false) {
            echo "<span class='ok'>✅ Obsahuje hlavní CSS třídu</span><br>";
        } else {
            echo "<span class='error'>❌ Neobsahuje hlavní CSS třídu</span><br>";
        }
    } else {
        echo "<span class='error'>❌ Template nelze načíst</span><br>";
    }
} else {
    echo "<span class='error'>❌ Template soubor neexistuje!</span><br>";
}

echo "</div>";

// 6. Test Smarty
echo "<div class='section'>";
echo "<h2>6. Test Smarty</h2>";

try {
    $smarty = Context::getContext()->smarty;
    echo "<span class='ok'>✅ Smarty context dostupný</span><br>";
    
    // Test přiřazení proměnných
    $smarty->assign([
        'product' => $product,
        'show_block' => true,
        'current_price' => 0
    ]);
    echo "<span class='ok'>✅ Proměnné přiřazeny</span><br>";
    
    // Test načtení template
    if (file_exists($templatePath)) {
        try {
            $renderedTemplate = $smarty->fetch($templatePath);
            if ($renderedTemplate && strlen(trim($renderedTemplate)) > 0) {
                echo "<span class='ok'>✅ Template se podařilo vyrenderovat (" . strlen($renderedTemplate) . " znaků)</span><br>";
                echo "<details><summary>Zobrazit vyrenderovaný template</summary><pre>" . htmlspecialchars($renderedTemplate) . "</pre></details>";
            } else {
                echo "<span class='error'>❌ Template je prázdný po renderování</span><br>";
            }
        } catch (Exception $e) {
            echo "<span class='error'>❌ Chyba při renderování template: " . $e->getMessage() . "</span><br>";
        }
    }
    
} catch (Exception $e) {
    echo "<span class='error'>❌ Chyba se Smarty: " . $e->getMessage() . "</span><br>";
}

echo "</div>";

// 7. Doporučení
echo "<div class='section'>";
echo "<h2>7. Doporučení</h2>";

echo "<p><strong>Na základě testů:</strong></p>";
echo "<ul>";
echo "<li>Hooks jsou registrovány v databázi ✅</li>";
echo "<li>displayFooter hook funguje ✅</li>";
echo "<li>displayProductActions hook nefunguje ❌</li>";
echo "</ul>";

echo "<p><strong>Možné příčiny:</strong></p>";
echo "<ul>";
echo "<li>Chyba v logice hookDisplayProductActions metody</li>";
echo "<li>Problém s template souborem</li>";
echo "<li>Chyba v podmínkách zobrazení</li>";
echo "<li>Problém s Smarty renderováním</li>";
echo "</ul>";

echo "<p><strong>Další kroky:</strong></p>";
echo "<ul>";
echo "<li><a href='check_display.php'>Test zobrazení na stránce</a></li>";
echo "<li><a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank'>Test na webu</a></li>";
echo "<li>Kontrola kódu modulu v priceinquiry.php</li>";
echo "</ul>";

echo "</div>";

echo "<hr>";
echo "<p><a href='diagnostic_tools.php'>← Zpět na diagnostické nástroje</a></p>";
echo "<p><small>Debug dokončen: " . date('Y-m-d H:i:s') . "</small></p>";
?>
