<?php
/**
 * <PERSON>mpletn<PERSON> vyčištění modulu z databáze
 * Smaže všechny záznamy modulu z databáze
 */

// Načteme PrestaShop
require_once(dirname(__FILE__) . '/prestashop_loader.php');

echo "<h1>Kompletn<PERSON> vyčištění modulu z databáze</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px; }
    .button.danger { background: #dc3545; }
</style>";

// Ko<PERSON><PERSON><PERSON>, jestli je to POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'clean_module') {
        echo "<div class='section'>";
        echo "<h2>Probíhá vyčištění...</h2>";
        
        $moduleName = 'priceinquiry';
        
        // 1. Najdeme ID modulu
        echo "<h3>1. Hledání modulu v databázi</h3>";
        
        $moduleData = Db::getInstance()->getRow('SELECT * FROM `' . _DB_PREFIX_ . 'module` WHERE name = "' . pSQL($moduleName) . '"');
        
        if ($moduleData) {
            $moduleId = $moduleData['id_module'];
            echo "<span class='ok'>✅ Modul nalezen (ID: {$moduleId})</span><br>";
        } else {
            echo "<span class='info'>ℹ️ Modul není v databázi</span><br>";
            $moduleId = null;
        }
        
        // 2. Smazání hooks
        echo "<h3>2. Smazání hooks</h3>";
        
        if ($moduleId) {
            $deletedHooks = Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'hook_module` WHERE id_module = ' . (int)$moduleId);
            if ($deletedHooks) {
                echo "<span class='ok'>✅ Hooks smazány</span><br>";
            } else {
                echo "<span class='info'>ℹ️ Žádné hooks k smazání</span><br>";
            }
        }
        
        // 3. Smazání z module_shop
        echo "<h3>3. Smazání z module_shop</h3>";
        
        if ($moduleId) {
            $deletedShop = Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'module_shop` WHERE id_module = ' . (int)$moduleId);
            if ($deletedShop) {
                echo "<span class='ok'>✅ Záznamy module_shop smazány</span><br>";
            } else {
                echo "<span class='info'>ℹ️ Žádné záznamy module_shop k smazání</span><br>";
            }
        }
        
        // 4. Smazání konfigurace
        echo "<h3>4. Smazání konfigurace</h3>";
        
        $configs = [
            'PRICE_INQUIRY_ENABLED',
            'PRICE_INQUIRY_EMAIL',
            'PRICE_INQUIRY_BUTTON_TEXT',
            'PRICE_INQUIRY_PRICE_TEXT',
            'PRICE_INQUIRY_SEND_ADMIN_EMAIL',
            'PRICE_INQUIRY_SEND_CUSTOMER_EMAIL'
        ];
        
        $deletedConfigs = 0;
        foreach ($configs as $configKey) {
            if (Configuration::deleteByName($configKey)) {
                $deletedConfigs++;
            }
        }
        
        echo "<span class='ok'>✅ Konfigurace smazána ({$deletedConfigs} položek)</span><br>";
        
        // 5. Smazání z tabulky module
        echo "<h3>5. Smazání z tabulky module</h3>";
        
        if ($moduleId) {
            $deletedModule = Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'module` WHERE id_module = ' . (int)$moduleId);
            if ($deletedModule) {
                echo "<span class='ok'>✅ Modul smazán z tabulky module</span><br>";
            } else {
                echo "<span class='error'>❌ Chyba při mazání z tabulky module</span><br>";
            }
        }
        
        // 6. Smazání tabulky dotazů (volitelné)
        echo "<h3>6. Smazání tabulky dotazů</h3>";
        
        if (isset($_POST['delete_data']) && $_POST['delete_data'] === '1') {
            $dropTable = Db::getInstance()->execute('DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'price_inquiry`');
            if ($dropTable) {
                echo "<span class='ok'>✅ Tabulka price_inquiry smazána</span><br>";
            } else {
                echo "<span class='error'>❌ Chyba při mazání tabulky</span><br>";
            }
        } else {
            echo "<span class='info'>ℹ️ Tabulka price_inquiry ponechána (data zachována)</span><br>";
        }
        
        // 7. Vyčištění cache
        echo "<h3>7. Vyčištění cache</h3>";
        
        try {
            Tools::clearSmartyCache();
            Tools::clearXMLCache();
            
            if (function_exists('opcache_reset')) {
                opcache_reset();
            }
            
            echo "<span class='ok'>✅ Cache vyčištěna</span><br>";
        } catch (Exception $e) {
            echo "<span class='warning'>⚠️ Částečná chyba při čištění cache</span><br>";
        }
        
        // 8. Finální kontrola
        echo "<h3>8. Finální kontrola</h3>";
        
        $finalCheck = Db::getInstance()->getRow('SELECT * FROM `' . _DB_PREFIX_ . 'module` WHERE name = "' . pSQL($moduleName) . '"');
        
        if (!$finalCheck) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
            echo "<h3>🎉 Vyčištění dokončeno!</h3>";
            echo "<p>Modul byl kompletně odstraněn z databáze.</p>";
            echo "<p>Nyní můžete provést <a href='manual_install.php' class='button'>manuální instalaci</a>.</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
            echo "<h3>⚠️ Vyčištění neúplné</h3>";
            echo "<p>Modul stále existuje v databázi.</p>";
            echo "</div>";
        }
        
        echo "</div>";
        
    } else {
        echo "<span class='error'>❌ Neznámá akce</span><br>";
    }
    
} else {
    // Zobrazíme formulář
    echo "<div class='section'>";
    echo "<h2>⚠️ Varování</h2>";
    echo "<p><strong style='color: red;'>Tato akce kompletně smaže modul z databáze!</strong></p>";
    
    echo "<p><strong>Co bude smazáno:</strong></p>";
    echo "<ul>";
    echo "<li>Všechny registrace hooks</li>";
    echo "<li>Záznamy v module_shop</li>";
    echo "<li>Konfigurace modulu</li>";
    echo "<li>Záznam v tabulce module</li>";
    echo "<li>Volitelně: tabulka s dotazy (všechna data!)</li>";
    echo "</ul>";
    
    echo "</div>";
    
    // Aktuální stav
    echo "<div class='section'>";
    echo "<h2>Aktuální stav modulu</h2>";
    
    $moduleData = Db::getInstance()->getRow('SELECT * FROM `' . _DB_PREFIX_ . 'module` WHERE name = "priceinquiry"');
    
    if ($moduleData) {
        echo "<strong>ID modulu:</strong> {$moduleData['id_module']}<br>";
        echo "<strong>Aktivní:</strong> " . ($moduleData['active'] ? 'Ano' : 'Ne') . "<br>";
        echo "<strong>Verze:</strong> {$moduleData['version']}<br>";
        
        // Počet hooks
        $hooksCount = Db::getInstance()->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'hook_module` WHERE id_module = ' . (int)$moduleData['id_module']);
        echo "<strong>Registrované hooks:</strong> {$hooksCount}<br>";
        
        // Počet dotazů
        $inquiriesCount = Db::getInstance()->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'price_inquiry`');
        echo "<strong>Uložené dotazy:</strong> {$inquiriesCount}<br>";
        
    } else {
        echo "<span class='info'>ℹ️ Modul není v databázi</span><br>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>Potvrzení vyčištění</h2>";
    
    echo "<form method='post'>";
    echo "<input type='hidden' name='action' value='clean_module'>";
    
    echo "<p>";
    echo "<label>";
    echo "<input type='checkbox' name='delete_data' value='1'> ";
    echo "<strong style='color: red;'>Smazat také tabulku s dotazy (všechna data budou ztracena!)</strong>";
    echo "</label>";
    echo "</p>";
    
    echo "<button type='submit' class='button danger' onclick='return confirm(\"Opravdu chcete kompletně vyčistit modul z databáze?\")'>🗑️ Vyčistit modul z databáze</button>";
    echo "</form>";
    
    echo "<p><a href='diagnostic_tools.php' class='button'>← Zpět na diagnostické nástroje</a></p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Vyčištění modulu: " . date('Y-m-d H:i:s') . "</small></p>";
?>
