<?php
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> test hooks bez problematického API
 * Testuje hooks p<PERSON><PERSON><PERSON> přes datab<PERSON>zi a p<PERSON><PERSON><PERSON> volání
 */

// Načteme PrestaShop
require_once(dirname(__FILE__) . '/prestashop_loader.php');

echo "<h1>Jed<PERSON>du<PERSON><PERSON> test hooks</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; font-size: 12px; }
    .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px; }
    .button.success { background: #28a745; }
</style>";

$moduleName = 'priceinquiry';
$shopId = Context::getContext()->shop->id;

// 1. Základní informace
echo "<div class='section'>";
echo "<h2>1. Základní informace</h2>";

$module = Module::getInstanceByName($moduleName);
if ($module) {
    echo "<strong>Modul:</strong> {$module->displayName} (ID: {$module->id})<br>";
    echo "<strong>Shop ID:</strong> {$shopId}<br>";
    echo "<strong>Aktivní:</strong> " . (Module::isEnabled($moduleName) ? 'Ano' : 'Ne') . "<br>";
} else {
    echo "<span class='error'>❌ Modul nebyl nalezen!</span><br>";
    exit;
}

echo "</div>";

// 2. Test databáze
echo "<div class='section'>";
echo "<h2>2. Test registrace v databázi</h2>";

$sql = 'SELECT hm.*, h.name as hook_name 
        FROM `' . _DB_PREFIX_ . 'hook_module` hm
        LEFT JOIN `' . _DB_PREFIX_ . 'hook` h ON h.id_hook = hm.id_hook
        WHERE hm.id_module = ' . (int)$module->id . '
        AND hm.id_shop = ' . (int)$shopId . '
        ORDER BY h.name';

$hooks = Db::getInstance()->executeS($sql);

if ($hooks) {
    echo "<span class='ok'>✅ Nalezeno " . count($hooks) . " registrovaných hooks</span><br>";
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f2f2f2;'><th style='border: 1px solid #ddd; padding: 8px;'>Hook Name</th><th style='border: 1px solid #ddd; padding: 8px;'>Hook ID</th><th style='border: 1px solid #ddd; padding: 8px;'>Position</th></tr>";
    
    foreach ($hooks as $hook) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$hook['hook_name']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$hook['id_hook']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$hook['position']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<span class='error'>❌ Žádné hooks nenalezeny v databázi</span><br>";
}

echo "</div>";

// 3. Test přímého volání hooks
echo "<div class='section'>";
echo "<h2>3. Test přímého volání hooks</h2>";

// Připravíme testovací produkt
$product = new Product(15336);
if (!Validate::isLoadedObject($product)) {
    echo "<span class='error'>❌ Testovací produkt se nepodařilo načíst</span><br>";
} else {
    echo "<span class='ok'>✅ Testovací produkt načten: {$product->name}</span><br>";
    
    // Nastavíme kontext
    Context::getContext()->smarty->assign('product', $product);
    
    // Test hlavních hooks
    $testHooks = [
        'displayProductActions' => 'hookDisplayProductActions',
        'displayHeader' => 'hookDisplayHeader',
        'displayFooter' => 'hookDisplayFooter'
    ];
    
    foreach ($testHooks as $hookName => $methodName) {
        echo "<h3>Test {$hookName}</h3>";
        
        if (method_exists($module, $methodName)) {
            echo "<span class='ok'>✅ Metoda {$methodName} existuje</span><br>";
            
            try {
                $params = ($hookName === 'displayProductActions') ? ['product' => $product] : [];
                $output = $module->$methodName($params);
                
                if ($output && strlen(trim($output)) > 0) {
                    echo "<span class='ok'>✅ Hook vrací obsah (" . strlen($output) . " znaků)</span><br>";
                    
                    // Zkontrolujeme klíčové části
                    if (strpos($output, 'price-inquiry') !== false) {
                        echo "<span class='ok'>✅ Obsahuje CSS třídy modulu</span><br>";
                    }
                    if (strpos($output, 'Cena na dotaz') !== false || strpos($output, 'Zjistit cenu') !== false) {
                        echo "<span class='ok'>✅ Obsahuje text modulu</span><br>";
                    }
                    
                    echo "<details><summary>Zobrazit výstup</summary><pre>" . htmlspecialchars($output) . "</pre></details>";
                } else {
                    echo "<span class='error'>❌ Hook nevrací obsah nebo je prázdný</span><br>";
                }
                
            } catch (Exception $e) {
                echo "<span class='error'>❌ Chyba při volání: " . $e->getMessage() . "</span><br>";
            }
        } else {
            echo "<span class='error'>❌ Metoda {$methodName} neexistuje</span><br>";
        }
        
        echo "<hr>";
    }
}

echo "</div>";

// 4. Test Hook::exec
echo "<div class='section'>";
echo "<h2>4. Test Hook::exec</h2>";

if (Validate::isLoadedObject($product)) {
    echo "<h3>Test Hook::exec('displayProductActions')</h3>";
    
    try {
        $params = ['product' => $product];
        $execOutput = Hook::exec('displayProductActions', $params);
        
        if ($execOutput && strlen(trim($execOutput)) > 0) {
            echo "<span class='ok'>✅ Hook::exec vrací obsah (" . strlen($execOutput) . " znaků)</span><br>";
            
            // Zkontrolujeme, jestli obsahuje náš modul
            if (strpos($execOutput, 'price-inquiry') !== false) {
                echo "<span class='ok'>✅ Výstup obsahuje náš modul</span><br>";
            } else {
                echo "<span class='warning'>⚠️ Výstup neobsahuje náš modul</span><br>";
            }
            
            echo "<details><summary>Zobrazit výstup Hook::exec</summary><pre>" . htmlspecialchars($execOutput) . "</pre></details>";
        } else {
            echo "<span class='error'>❌ Hook::exec nevrací obsah</span><br>";
        }
        
    } catch (Exception $e) {
        echo "<span class='error'>❌ Chyba při Hook::exec: " . $e->getMessage() . "</span><br>";
    }
}

echo "</div>";

// 5. Test konfigurace
echo "<div class='section'>";
echo "<h2>5. Test konfigurace modulu</h2>";

$configs = [
    'PRICE_INQUIRY_ENABLED' => 'Modul povolen',
    'PRICE_INQUIRY_EMAIL' => 'E-mail pro dotazy',
    'PRICE_INQUIRY_BUTTON_TEXT' => 'Text tlačítka',
    'PRICE_INQUIRY_PRICE_TEXT' => 'Text místo ceny'
];

foreach ($configs as $key => $name) {
    $value = Configuration::get($key);
    if ($value) {
        echo "<span class='ok'>✅ {$name}: {$value}</span><br>";
    } else {
        echo "<span class='error'>❌ {$name}: není nastaveno</span><br>";
    }
}

echo "</div>";

// 6. Shrnutí a doporučení
echo "<div class='section'>";
echo "<h2>6. Shrnutí a doporučení</h2>";

$dbHooksCount = count($hooks);
$directCallWorks = false;
$hookExecWorks = false;

// Zkontrolujeme, jestli přímé volání funguje
if (Validate::isLoadedObject($product) && method_exists($module, 'hookDisplayProductActions')) {
    try {
        $output = $module->hookDisplayProductActions(['product' => $product]);
        $directCallWorks = ($output && strlen(trim($output)) > 0);
    } catch (Exception $e) {
        // Ignorujeme chyby
    }
}

// Zkontrolujeme Hook::exec
if (Validate::isLoadedObject($product)) {
    try {
        $execOutput = Hook::exec('displayProductActions', ['product' => $product]);
        $hookExecWorks = ($execOutput && strpos($execOutput, 'price-inquiry') !== false);
    } catch (Exception $e) {
        // Ignorujeme chyby
    }
}

if ($dbHooksCount > 0 && $directCallWorks && $hookExecWorks) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎉 Modul funguje správně!</h3>";
    echo "<p>Hooks jsou registrovány v databázi a přímé volání i Hook::exec fungují.</p>";
    echo "<p>Modul by měl být viditelný na webu.</p>";
    echo "<p><a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank' class='button success'>Test na webu</a></p>";
    echo "</div>";
} elseif ($dbHooksCount > 0 && $directCallWorks) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px;'>";
    echo "<h3>⚠️ Částečně funkční</h3>";
    echo "<p>Hooks jsou v databázi a přímé volání funguje, ale Hook::exec možná nefunguje.</p>";
    echo "<p>Zkuste test na webu - možná bude fungovat.</p>";
    echo "<p><a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank' class='button'>Test na webu</a></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Modul nefunguje</h3>";
    echo "<p><strong>Problémy:</strong></p>";
    echo "<ul>";
    if ($dbHooksCount === 0) echo "<li>Hooks nejsou registrovány v databázi</li>";
    if (!$directCallWorks) echo "<li>Přímé volání hooks nefunguje</li>";
    if (!$hookExecWorks) echo "<li>Hook::exec nefunguje</li>";
    echo "</ul>";
    echo "<p><strong>Doporučení:</strong></p>";
    echo "<ul>";
    echo "<li><a href='force_register_hooks.php'>Vynutit registraci hooks</a></li>";
    echo "<li><a href='manual_install.php'>Manuální reinstalace</a></li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";

echo "<hr>";
echo "<p><a href='diagnostic_tools.php'>← Zpět na diagnostické nástroje</a></p>";
echo "<p><small>Test dokončen: " . date('Y-m-d H:i:s') . "</small></p>";
?>
