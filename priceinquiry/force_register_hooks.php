<?php
/**
 * Vynucená registrace hooks přímo v databázi
 * Použít pouze pokud standardní registrace nefunguje
 */

// Načteme PrestaShop
require_once(dirname(__FILE__) . '/prestashop_loader.php');

echo "<h1>Vynucená registrace hooks v databázi</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; }
</style>";

$module = Module::getInstanceByName('priceinquiry');
if (!$module) {
    echo "<span class='error'>❌ Modul 'priceinquiry' nebyl nalezen!</span>";
    exit;
}

$moduleId = $module->id;
$shopId = Context::getContext()->shop->id;

echo "<div class='section'>";
echo "<h2>Informace o modulu</h2>";
echo "<strong>ID modulu:</strong> {$moduleId}<br>";
echo "<strong>Název:</strong> {$module->name}<br>";
echo "<strong>ID shopu:</strong> {$shopId}<br>";
echo "</div>";

// Hooks které potřebujeme registrovat
$hooks = [
    'displayProductPriceBlock',
    'displayProductActions', 
    'displayProductAdditionalInfo',
    'displayReassurance',
    'displayHeader',
    'displayFooter'
];

echo "<div class='section'>";
echo "<h2>Kontrola a registrace hooks</h2>";

foreach ($hooks as $hookName) {
    echo "<h3>{$hookName}</h3>";
    
    // Najdeme ID hook
    $hookId = Hook::getIdByName($hookName);
    if (!$hookId) {
        echo "<span class='error'>❌ Hook {$hookName} neexistuje v systému</span><br>";
        continue;
    }
    
    echo "<strong>Hook ID:</strong> {$hookId}<br>";
    
    // Zkontrolujeme aktuální registraci
    $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'hook_module` 
            WHERE `id_module` = ' . (int)$moduleId . ' 
            AND `id_hook` = ' . (int)$hookId . '
            AND `id_shop` = ' . (int)$shopId;
    
    $existing = Db::getInstance()->getRow($sql);
    
    if ($existing) {
        echo "<span class='info'>ℹ️ Hook je již registrován v databázi</span><br>";
        echo "<pre>" . print_r($existing, true) . "</pre>";
        
        // Smazat a znovu vytvořit
        $deleteSql = 'DELETE FROM `' . _DB_PREFIX_ . 'hook_module` 
                     WHERE `id_module` = ' . (int)$moduleId . ' 
                     AND `id_hook` = ' . (int)$hookId . '
                     AND `id_shop` = ' . (int)$shopId;
        
        if (Db::getInstance()->execute($deleteSql)) {
            echo "<span class='ok'>✅ Starý záznam smazán</span><br>";
        }
    }
    
    // Najdeme nejvyšší pozici pro tento hook
    $positionSql = 'SELECT MAX(position) as max_position 
                    FROM `' . _DB_PREFIX_ . 'hook_module` 
                    WHERE `id_hook` = ' . (int)$hookId . '
                    AND `id_shop` = ' . (int)$shopId;
    
    $maxPosition = Db::getInstance()->getValue($positionSql);
    $newPosition = $maxPosition ? $maxPosition + 1 : 1;
    
    // Vložíme nový záznam
    $insertSql = 'INSERT INTO `' . _DB_PREFIX_ . 'hook_module` 
                  (`id_module`, `id_shop`, `id_hook`, `position`) 
                  VALUES (' . (int)$moduleId . ', ' . (int)$shopId . ', ' . (int)$hookId . ', ' . (int)$newPosition . ')';
    
    if (Db::getInstance()->execute($insertSql)) {
        echo "<span class='ok'>✅ Hook úspěšně registrován v databázi (pozice: {$newPosition})</span><br>";
        
        // Ověříme registraci
        $checkSql = 'SELECT * FROM `' . _DB_PREFIX_ . 'hook_module` 
                     WHERE `id_module` = ' . (int)$moduleId . ' 
                     AND `id_hook` = ' . (int)$hookId . '
                     AND `id_shop` = ' . (int)$shopId;
        
        $newRecord = Db::getInstance()->getRow($checkSql);
        if ($newRecord) {
            echo "<span class='ok'>✅ Registrace ověřena</span><br>";
        } else {
            echo "<span class='error'>❌ Registrace se nezdařila</span><br>";
        }
    } else {
        echo "<span class='error'>❌ Chyba při vkládání do databáze</span><br>";
        echo "<strong>SQL:</strong> " . $insertSql . "<br>";
        echo "<strong>Chyba:</strong> " . Db::getInstance()->getMsgError() . "<br>";
    }
    
    echo "<hr>";
}

echo "</div>";

// Vyčištění cache
echo "<div class='section'>";
echo "<h2>Vyčištění cache</h2>";

try {
    // Vyčistíme všechny druhy cache
    Tools::clearSmartyCache();
    Tools::clearXMLCache();
    
    // Vyčistíme cache složku
    $cacheDir = _PS_CACHE_DIR_;
    if (is_dir($cacheDir)) {
        $files = glob($cacheDir . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
    }
    
    // Vyčistíme také cache/smarty
    $smartyCacheDir = _PS_CACHE_DIR_ . 'smarty/';
    if (is_dir($smartyCacheDir)) {
        $files = glob($smartyCacheDir . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
    }
    
    echo "<span class='ok'>✅ Všechna cache vyčištěna</span><br>";
    
} catch (Exception $e) {
    echo "<span class='error'>❌ Chyba při čištění cache: " . $e->getMessage() . "</span><br>";
}

echo "</div>";

// Finální kontrola
echo "<div class='section'>";
echo "<h2>Finální kontrola</h2>";

$allRegistered = true;
foreach ($hooks as $hookName) {
    $hookId = Hook::getIdByName($hookName);
    if ($hookId) {
        $isRegistered = Hook::isModuleRegisteredOnHook($module, $hookId, $shopId);
        if ($isRegistered) {
            echo "<span class='ok'>✅ {$hookName} - registrován</span><br>";
        } else {
            echo "<span class='error'>❌ {$hookName} - NENÍ registrován</span><br>";
            $allRegistered = false;
        }
    }
}

if ($allRegistered) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
    echo "<h3>🎉 Všechny hooks byly úspěšně registrovány!</h3>";
    echo "<p>Modul by nyní měl fungovat správně na webu.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
    echo "<h3>⚠️ Některé hooks stále nejsou registrovány</h3>";
    echo "<p>Možná je problém s oprávněními databáze nebo s modulem samotným.</p>";
    echo "</div>";
}

echo "</div>";

echo "<hr>";
echo "<p><strong>Další kroky:</strong></p>";
echo "<ol>";
echo "<li><a href='debug_script.php'>Spustit diagnostiku</a> - ověřit registraci hooks</li>";
echo "<li><a href='check_display.php'>Zkontrolovat zobrazení</a> - test výstupu modulu</li>";
echo "<li><a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank'>Test na webu</a></li>";
echo "</ol>";

echo "<p><a href='diagnostic_tools.php'>← Zpět na diagnostické nástroje</a></p>";
echo "<p><small>Vynucená registrace dokončena: " . date('Y-m-d H:i:s') . "</small></p>";
?>
