<?php
/**
 * Script pro nastavení nulové ceny testovacího produktu
 * Nastaví cenu produktu 15336 na 0 Kč pro testování modulu
 */

// Nastavení pro debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Načteme PrestaShop - zkusíme různé cesty
$prestashop_paths = [
    dirname(__FILE__) . '/../../config/config.inc.php',
    dirname(__FILE__) . '/../../../config/config.inc.php',
    dirname(dirname(dirname(__FILE__))) . '/config/config.inc.php'
];

$config_loaded = false;
foreach ($prestashop_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        require_once(dirname($path) . '/init.php');
        $config_loaded = true;
        break;
    }
}

if (!$config_loaded) {
    die("Chyba: Nepodařilo se načíst PrestaShop konfiguraci. Zkontrolujte cestu k modulu.");
}

echo "<h1>Nastavení nulové ceny pro testování</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px; }
    .button:hover { background: #005a87; }
    .button.danger { background: #dc3545; }
    .button.danger:hover { background: #c82333; }
</style>";

$productId = 15336;

// Kontrola, jestli je to POST request pro změnu ceny
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    echo "<div class='section'>";
    echo "<h2>Provádění změny...</h2>";
    
    $product = new Product($productId, false, Context::getContext()->language->id);
    
    if (!Validate::isLoadedObject($product)) {
        echo "<span class='error'>❌ Produkt s ID {$productId} nebyl nalezen!</span><br>";
    } else {
        $action = $_POST['action'];
        $success = false;
        
        if ($action === 'set_zero') {
            // Nastavíme cenu na 0
            $product->price = 0;
            $success = $product->update();
            
            if ($success) {
                echo "<span class='ok'>✅ Cena produktu byla nastavena na 0 Kč</span><br>";
            } else {
                echo "<span class='error'>❌ Chyba při nastavování ceny</span><br>";
            }
            
        } elseif ($action === 'restore_price' && isset($_POST['original_price'])) {
            // Obnovíme původní cenu
            $originalPrice = (float)$_POST['original_price'];
            $product->price = $originalPrice;
            $success = $product->update();
            
            if ($success) {
                echo "<span class='ok'>✅ Cena produktu byla obnovena na " . number_format($originalPrice, 2, ',', ' ') . " Kč</span><br>";
            } else {
                echo "<span class='error'>❌ Chyba při obnovování ceny</span><br>";
            }
        }
        
        if ($success) {
            // Vyčistíme cache
            try {
                Tools::clearSmartyCache();
                Tools::clearXMLCache();
                echo "<span class='ok'>✅ Cache vyčištěna</span><br>";
            } catch (Exception $e) {
                echo "<span class='warning'>⚠️ Chyba při čištění cache: " . $e->getMessage() . "</span><br>";
            }
        }
    }
    
    echo "</div>";
}

// Zobrazíme aktuální stav
echo "<div class='section'>";
echo "<h2>Aktuální stav produktu</h2>";

$product = new Product($productId, false, Context::getContext()->language->id);

if (!Validate::isLoadedObject($product)) {
    echo "<span class='error'>❌ Produkt s ID {$productId} nebyl nalezen!</span><br>";
    exit;
}

$currentPrice = $product->price;
$displayPrice = Product::getPriceStatic($productId, true, null, 2, null, false, true);

echo "<strong>Název produktu:</strong> " . htmlspecialchars($product->name) . "<br>";
echo "<strong>ID produktu:</strong> {$productId}<br>";
echo "<strong>Základní cena:</strong> " . number_format($currentPrice, 2, ',', ' ') . " Kč<br>";
echo "<strong>Zobrazovaná cena:</strong> " . number_format($displayPrice, 2, ',', ' ') . " Kč<br>";

if ($currentPrice <= 0) {
    echo "<span class='ok'>✅ Produkt má nulovou cenu - modul 'Cena na dotaz' by se měl zobrazit</span><br>";
} else {
    echo "<span class='warning'>⚠️ Produkt má nenulovou cenu - modul se nezobrazí</span><br>";
}

echo "</div>";

// Kontrola variant
echo "<div class='section'>";
echo "<h2>Varianty produktu</h2>";

$attributes = Product::getProductAttributesIds($productId);
if ($attributes && count($attributes) > 0) {
    echo "<strong>Počet variant:</strong> " . count($attributes) . "<br><br>";
    
    echo "<table style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f2f2f2;'>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>ID varianty</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Cena</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Status</th>";
    echo "</tr>";
    
    foreach ($attributes as $attr) {
        $attrPrice = Product::getPriceStatic($productId, true, $attr['id_product_attribute'], 2, null, false, true);
        $status = $attrPrice <= 0 ? "<span class='ok'>Zobrazí modul</span>" : "<span class='warning'>Nezobrazí</span>";
        
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$attr['id_product_attribute']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . number_format($attrPrice, 2, ',', ' ') . " Kč</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<span class='info'>ℹ️ Produkt nemá varianty</span><br>";
}

echo "</div>";

// Akce
echo "<div class='section'>";
echo "<h2>Akce</h2>";

if ($currentPrice > 0) {
    echo "<p>Pro testování modulu 'Cena na dotaz' je potřeba nastavit cenu produktu na 0 Kč.</p>";
    echo "<form method='post' style='display: inline;'>";
    echo "<input type='hidden' name='action' value='set_zero'>";
    echo "<input type='hidden' name='original_price' value='{$currentPrice}'>";
    echo "<button type='submit' class='button' onclick='return confirm(\"Opravdu chcete nastavit cenu na 0 Kč?\")'>Nastavit cenu na 0 Kč</button>";
    echo "</form>";
} else {
    echo "<p>Cena je již nastavena na 0 Kč. Můžete ji obnovit na původní hodnotu.</p>";
    
    // Zkusíme najít původní cenu v historii nebo použijeme výchozí
    $originalPrice = 1000; // Výchozí cena pro obnovení
    
    echo "<form method='post' style='display: inline;'>";
    echo "<input type='hidden' name='action' value='restore_price'>";
    echo "<input type='number' name='original_price' value='{$originalPrice}' step='0.01' placeholder='Původní cena'> Kč ";
    echo "<button type='submit' class='button danger' onclick='return confirm(\"Opravdu chcete obnovit cenu?\")'>Obnovit cenu</button>";
    echo "</form>";
}

echo "<br><br>";
echo "<a href='check_display.php' class='button'>Zkontrolovat zobrazení modulu</a>";
echo "<a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank' class='button'>Otevřít produkt na webu</a>";

echo "</div>";

// Informace o testování
echo "<div class='section'>";
echo "<h2>Informace o testování</h2>";

echo "<p><strong>Postup testování:</strong></p>";
echo "<ol>";
echo "<li>Nastavte cenu produktu na 0 Kč pomocí tlačítka výše</li>";
echo "<li>Zkontrolujte zobrazení modulu pomocí <a href='check_display.php'>check_display.php</a></li>";
echo "<li>Navštivte <a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank'>stránku produktu</a></li>";
echo "<li>Měli byste vidět 'Cena na dotaz' místo ceny a tlačítko 'Zjistit cenu'</li>";
echo "<li>Po testování můžete obnovit původní cenu</li>";
echo "</ol>";

echo "<p><strong>Poznámka:</strong> Změna ceny ovlivní pouze testování. Po dokončení testů doporučujeme obnovit původní cenu.</p>";

echo "</div>";

echo "<hr>";
echo "<p><a href='debug_script.php'>Úplná diagnostika</a> | <a href='fix_module.php'>Opravit modul</a></p>";
echo "<p><small>Aktualizováno: " . date('Y-m-d H:i:s') . "</small></p>";
?>
