<?php
/**
 * Univerzální loader pro PrestaShop
 * Automaticky najde a načte config.inc.php a init.php
 */

function loadPrestaShop() {
    // Možné cesty k config.inc.php
    $config_paths = [
        dirname(__FILE__) . '/../../config/config.inc.php',
        dirname(__FILE__) . '/../../../config/config.inc.php',
        dirname(dirname(dirname(__FILE__))) . '/config/config.inc.php',
        $_SERVER['DOCUMENT_ROOT'] . '/config/config.inc.php'
    ];

    // Možné cesty k init.php
    $init_paths = [
        dirname(__FILE__) . '/../../init.php',
        dirname(__FILE__) . '/../../../init.php',
        dirname(dirname(dirname(__FILE__))) . '/init.php',
        $_SERVER['DOCUMENT_ROOT'] . '/init.php'
    ];

    $found_config = null;
    $found_init = null;

    // Najdeme config.inc.php
    foreach ($config_paths as $path) {
        if (file_exists($path)) {
            $found_config = $path;
            break;
        }
    }

    // Najdeme init.php
    foreach ($init_paths as $path) {
        if (file_exists($path)) {
            $found_init = $path;
            break;
        }
    }

    if (!$found_config) {
        throw new Exception("Nepodařilo se najít config.inc.php. Zkontrolujte umístění modulu.");
    }

    if (!$found_init) {
        throw new Exception("Nepodařilo se najít init.php. Zkontrolujte umístění modulu.");
    }

    // Načteme soubory
    try {
        require_once($found_config);
        require_once($found_init);
        return true;
    } catch (Exception $e) {
        throw new Exception("Chyba při načítání PrestaShop: " . $e->getMessage());
    }
}

// Automatické načtení pokud je soubor includován
if (!defined('_PS_VERSION_')) {
    try {
        loadPrestaShop();
    } catch (Exception $e) {
        die("Chyba při načítání PrestaShop: " . $e->getMessage() . "<br><a href='path_finder.php'>Zkontrolovat cesty</a>");
    }
}
?>
