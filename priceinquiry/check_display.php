<?php
/**
 * R<PERSON>lá kontrola zobrazení modulu na konkrétní stránce
 * Simuluje návštěvu stránky produktu a kontroluje výstup hooks
 */

// Nastavení pro debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Načteme PrestaShop
require_once(dirname(__FILE__) . '/../../config/config.inc.php');
require_once(dirname(__FILE__) . '/../../init.php');

echo "<h1>Kontrola zobrazení modulu 'Cena na dotaz'</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 300px; }
    .highlight { background: yellow; }
</style>";

$productId = 15336;

// Načteme modul a produkt
$module = Module::getInstanceByName('priceinquiry');
$product = new Product($productId, false, Context::getContext()->language->id);

if (!$module) {
    echo "<span class='error'>❌ Modul nenalezen!</span>";
    exit;
}

if (!Validate::isLoadedObject($product)) {
    echo "<span class='error'>❌ Produkt s ID {$productId} nenalezen!</span>";
    exit;
}

echo "<div class='section'>";
echo "<h2>Základní informace</h2>";
echo "<strong>Produkt:</strong> " . htmlspecialchars($product->name) . "<br>";
echo "<strong>Modul povolen:</strong> " . (Configuration::get('PRICE_INQUIRY_ENABLED') ? 'Ano' : 'Ne') . "<br>";

$price = Product::getPriceStatic($productId, true, null, 2, null, false, true);
echo "<strong>Cena produktu:</strong> " . number_format($price, 2, ',', ' ') . " Kč<br>";
echo "</div>";

// Simulujeme kontext stránky produktu
echo "<div class='section'>";
echo "<h2>Simulace stránky produktu</h2>";

// Nastavíme kontext
Context::getContext()->controller = new ProductController();
Context::getContext()->controller->php_self = 'product';

// Přidáme produkt do kontextu
Context::getContext()->smarty->assign('product', $product);

echo "<span class='info'>Kontext nastaven na: product page</span><br>";

// Test displayProductActions hook
echo "<h3>Test hook displayProductActions</h3>";
$params = ['product' => $product];
$hookOutput = $module->hookDisplayProductActions($params);

if (!empty($hookOutput)) {
    echo "<span class='ok'>✅ Hook vrací obsah (" . strlen($hookOutput) . " znaků)</span><br>";
    
    // Analyzujeme obsah
    if (strpos($hookOutput, 'price-inquiry-block-detail') !== false) {
        echo "<span class='ok'>✅ Obsahuje blok price-inquiry-block-detail</span><br>";
    }
    
    if (strpos($hookOutput, 'display: block') !== false) {
        echo "<span class='ok'>✅ Blok je viditelný (display: block)</span><br>";
    } elseif (strpos($hookOutput, 'display: none') !== false) {
        echo "<span class='warning'>⚠️ Blok je skrytý (display: none)</span><br>";
    }
    
    if (strpos($hookOutput, 'Cena na dotaz') !== false) {
        echo "<span class='ok'>✅ Obsahuje text 'Cena na dotaz'</span><br>";
    }
    
    if (strpos($hookOutput, 'Zjistit cenu') !== false) {
        echo "<span class='ok'>✅ Obsahuje tlačítko 'Zjistit cenu'</span><br>";
    }
    
    echo "<h4>Výstup hook:</h4>";
    $highlightedOutput = str_replace(
        ['display: block', 'display: none', 'Cena na dotaz', 'Zjistit cenu'],
        ['<span class="highlight">display: block</span>', '<span class="highlight">display: none</span>', '<span class="highlight">Cena na dotaz</span>', '<span class="highlight">Zjistit cenu</span>'],
        htmlspecialchars($hookOutput)
    );
    echo "<pre>{$highlightedOutput}</pre>";
    
} else {
    echo "<span class='error'>❌ Hook nevrací žádný obsah</span><br>";
    
    // Debug - proč hook nevrací obsah
    echo "<h4>Debug informace:</h4>";
    echo "- php_self: " . Context::getContext()->controller->php_self . "<br>";
    echo "- Modul povolen: " . (Configuration::get('PRICE_INQUIRY_ENABLED') ? 'true' : 'false') . "<br>";
    echo "- Cena produktu: " . $price . "<br>";
    echo "- Produkt validní: " . (Validate::isLoadedObject($product) ? 'true' : 'false') . "<br>";
}

echo "</div>";

// Test displayHeader hook (CSS a JS)
echo "<div class='section'>";
echo "<h2>Test hook displayHeader (CSS/JS)</h2>";

$headerOutput = $module->hookDisplayHeader();
if (!empty($headerOutput)) {
    echo "<span class='ok'>✅ Hook displayHeader vrací obsah</span><br>";
    echo "<pre>" . htmlspecialchars($headerOutput) . "</pre>";
} else {
    echo "<span class='info'>ℹ️ Hook displayHeader nevrací obsah (CSS/JS se přidávají přímo)</span><br>";
    
    // Zkontrolujeme, jestli soubory existují
    $cssFile = _PS_MODULE_DIR_ . 'priceinquiry/views/css/front.css';
    $jsFile = _PS_MODULE_DIR_ . 'priceinquiry/views/js/front.js';
    
    echo "CSS soubor: " . (file_exists($cssFile) ? "<span class='ok'>✅ Existuje</span>" : "<span class='error'>❌ Neexistuje</span>") . "<br>";
    echo "JS soubor: " . (file_exists($jsFile) ? "<span class='ok'>✅ Existuje</span>" : "<span class='error'>❌ Neexistuje</span>") . "<br>";
}

echo "</div>";

// Test displayFooter hook (modal)
echo "<div class='section'>";
echo "<h2>Test hook displayFooter (modal)</h2>";

$footerOutput = $module->hookDisplayFooter();
if (!empty($footerOutput)) {
    echo "<span class='ok'>✅ Hook displayFooter vrací obsah (" . strlen($footerOutput) . " znaků)</span><br>";
    
    if (strpos($footerOutput, 'piModal') !== false) {
        echo "<span class='ok'>✅ Obsahuje modal piModal</span><br>";
    }
    
    echo "<h4>Náhled modal:</h4>";
    echo "<pre>" . htmlspecialchars(substr($footerOutput, 0, 500)) . "...</pre>";
} else {
    echo "<span class='error'>❌ Hook displayFooter nevrací obsah</span><br>";
}

echo "</div>";

// Kontrola variant produktu
echo "<div class='section'>";
echo "<h2>Kontrola variant produktu</h2>";

$attributes = Product::getProductAttributesIds($productId);
if ($attributes && count($attributes) > 0) {
    echo "<strong>Počet variant:</strong> " . count($attributes) . "<br>";
    
    $zeroVariants = 0;
    foreach ($attributes as $attr) {
        $attrPrice = Product::getPriceStatic($productId, true, $attr['id_product_attribute'], 2, null, false, true);
        if ($attrPrice <= 0) {
            $zeroVariants++;
        }
    }
    
    echo "<strong>Varianty s nulovou cenou:</strong> {$zeroVariants}<br>";
    
    if ($zeroVariants > 0) {
        echo "<span class='ok'>✅ Některé varianty mají nulovou cenu - modul se může zobrazit při změně varianty</span><br>";
    } else {
        echo "<span class='warning'>⚠️ Žádná varianta nemá nulovou cenu</span><br>";
    }
} else {
    echo "<span class='info'>ℹ️ Produkt nemá varianty</span><br>";
}

echo "</div>";

// Závěrečné doporučení
echo "<div class='section'>";
echo "<h2>Závěr a doporučení</h2>";

$shouldDisplay = Configuration::get('PRICE_INQUIRY_ENABLED') && ($price <= 0 || $zeroVariants > 0);

if ($shouldDisplay && !empty($hookOutput)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎉 Modul by se měl zobrazovat!</h3>";
    echo "<p>Všechny podmínky jsou splněny a hook vrací obsah.</p>";
    echo "<p><strong>Test na webu:</strong> <a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank'>https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html</a></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>⚠️ Modul se nezobrazuje</h3>";
    echo "<p>Možné příčiny:</p>";
    echo "<ul>";
    if (!Configuration::get('PRICE_INQUIRY_ENABLED')) {
        echo "<li>Modul není povolen v konfiguraci</li>";
    }
    if ($price > 0 && $zeroVariants == 0) {
        echo "<li>Produkt ani jeho varianty nemají nulovou cenu</li>";
    }
    if (empty($hookOutput)) {
        echo "<li>Hook nevrací žádný obsah</li>";
    }
    echo "</ul>";
    echo "<p><a href='fix_module.php'>Spustit opravu modulu</a></p>";
    echo "</div>";
}

echo "</div>";

echo "<hr>";
echo "<p><a href='debug_script.php'>Úplná diagnostika</a> | <a href='test_product.php'>Detailní test produktu</a></p>";
echo "<p><small>Kontrola dokončena: " . date('Y-m-d H:i:s') . "</small></p>";
?>
