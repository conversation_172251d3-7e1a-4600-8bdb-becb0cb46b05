<?php
/**
 * <PERSON><PERSON><PERSON>n<PERSON> reinstalace modulu "Cena na dotaz"
 * Odinstaluje a znovu nainstaluje modul
 */

// Načteme PrestaShop
require_once(dirname(__FILE__) . '/prestashop_loader.php');

echo "<h1>Kompletní reinstalace modulu 'Cena na dotaz'</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px; }
    .button.danger { background: #dc3545; }
    .button.success { background: #28a745; }
</style>";

// <PERSON><PERSON>rol<PERSON>, jest<PERSON> je to POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'reinstall') {
        echo "<div class='section'>";
        echo "<h2>Probíhá reinstalace...</h2>";
        
        $module = Module::getInstanceByName('priceinquiry');
        if (!$module) {
            echo "<span class='error'>❌ Modul nebyl nalezen!</span><br>";
            exit;
        }
        
        $moduleId = $module->id;
        
        // 1. Uložíme konfiguraci
        echo "<h3>1. Zálohování konfigurace</h3>";
        $config_backup = [
            'PRICE_INQUIRY_ENABLED' => Configuration::get('PRICE_INQUIRY_ENABLED'),
            'PRICE_INQUIRY_EMAIL' => Configuration::get('PRICE_INQUIRY_EMAIL'),
            'PRICE_INQUIRY_BUTTON_TEXT' => Configuration::get('PRICE_INQUIRY_BUTTON_TEXT'),
            'PRICE_INQUIRY_PRICE_TEXT' => Configuration::get('PRICE_INQUIRY_PRICE_TEXT'),
            'PRICE_INQUIRY_SEND_ADMIN_EMAIL' => Configuration::get('PRICE_INQUIRY_SEND_ADMIN_EMAIL'),
            'PRICE_INQUIRY_SEND_CUSTOMER_EMAIL' => Configuration::get('PRICE_INQUIRY_SEND_CUSTOMER_EMAIL')
        ];
        echo "<span class='ok'>✅ Konfigurace zálohována</span><br>";
        
        // 2. Manuální vyčištění databáze
        echo "<h3>2. Vyčištění databáze</h3>";
        
        // Smazání hooks
        $deleteSql = 'DELETE FROM `' . _DB_PREFIX_ . 'hook_module` WHERE id_module = ' . (int)$moduleId;
        if (Db::getInstance()->execute($deleteSql)) {
            echo "<span class='ok'>✅ Hooks smazány z databáze</span><br>";
        } else {
            echo "<span class='error'>❌ Chyba při mazání hooks</span><br>";
        }
        
        // 3. Odinstalace modulu
        echo "<h3>3. Odinstalace modulu</h3>";
        if ($module->uninstall()) {
            echo "<span class='ok'>✅ Modul odinstalován</span><br>";
        } else {
            echo "<span class='error'>❌ Chyba při odinstalaci</span><br>";
        }
        
        // 4. Vyčištění cache
        echo "<h3>4. Vyčištění cache</h3>";
        try {
            Tools::clearSmartyCache();
            Tools::clearXMLCache();
            
            // Vyčistíme cache složky
            $cacheDirs = [
                _PS_CACHE_DIR_,
                _PS_CACHE_DIR_ . 'smarty/',
                _PS_CACHE_DIR_ . 'tcpdf/',
                _PS_CACHE_DIR_ . 'cachefs/'
            ];
            
            foreach ($cacheDirs as $dir) {
                if (is_dir($dir)) {
                    $files = glob($dir . '*');
                    foreach ($files as $file) {
                        if (is_file($file)) {
                            unlink($file);
                        }
                    }
                }
            }
            
            echo "<span class='ok'>✅ Cache vyčištěna</span><br>";
        } catch (Exception $e) {
            echo "<span class='warning'>⚠️ Částečná chyba při čištění cache: " . $e->getMessage() . "</span><br>";
        }
        
        // 5. Reinstalace modulu
        echo "<h3>5. Reinstalace modulu</h3>";
        
        // Znovu načteme modul
        $newModule = Module::getInstanceByName('priceinquiry');
        if (!$newModule) {
            echo "<span class='error'>❌ Modul nebyl nalezen pro reinstalaci!</span><br>";
            exit;
        }
        
        if ($newModule->install()) {
            echo "<span class='ok'>✅ Modul reinstalován</span><br>";
        } else {
            echo "<span class='error'>❌ Chyba při reinstalaci</span><br>";
            exit;
        }
        
        // 6. Obnovení konfigurace
        echo "<h3>6. Obnovení konfigurace</h3>";
        foreach ($config_backup as $key => $value) {
            if ($value !== null && $value !== '') {
                Configuration::updateValue($key, $value);
            }
        }
        echo "<span class='ok'>✅ Konfigurace obnovena</span><br>";
        
        // 7. Finální vyčištění cache
        echo "<h3>7. Finální vyčištění cache</h3>";
        Tools::clearSmartyCache();
        Tools::clearXMLCache();
        echo "<span class='ok'>✅ Cache vyčištěna</span><br>";
        
        // 8. Kontrola výsledku
        echo "<h3>8. Kontrola výsledku</h3>";
        
        $finalModule = Module::getInstanceByName('priceinquiry');
        if ($finalModule && Module::isInstalled('priceinquiry')) {
            echo "<span class='ok'>✅ Modul je nainstalován</span><br>";
            
            // Kontrola hooks
            $hooks = ['displayProductActions', 'displayHeader', 'displayFooter'];
            $hooksOk = 0;
            
            foreach ($hooks as $hookName) {
                $hookId = Hook::getIdByName($hookName);
                if ($hookId && Hook::isModuleRegisteredOnHook($finalModule, $hookId, Context::getContext()->shop->id)) {
                    echo "<span class='ok'>✅ Hook {$hookName} registrován</span><br>";
                    $hooksOk++;
                } else {
                    echo "<span class='error'>❌ Hook {$hookName} NENÍ registrován</span><br>";
                }
            }
            
            if ($hooksOk === count($hooks)) {
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
                echo "<h3>🎉 Reinstalace úspěšná!</h3>";
                echo "<p>Modul byl úspěšně reinstalován a všechny hooks jsou registrovány.</p>";
                echo "<p><a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank' class='button success'>Test na webu</a></p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
                echo "<h3>⚠️ Reinstalace částečně úspěšná</h3>";
                echo "<p>Modul byl reinstalován, ale některé hooks nejsou registrovány.</p>";
                echo "<p><a href='force_register_hooks.php' class='button danger'>Vynutit registraci hooks</a></p>";
                echo "</div>";
            }
        } else {
            echo "<span class='error'>❌ Reinstalace se nezdařila</span><br>";
        }
        
        echo "</div>";
        
    } else {
        echo "<span class='error'>❌ Neznámá akce</span><br>";
    }
    
} else {
    // Zobrazíme formulář pro potvrzení
    $module = Module::getInstanceByName('priceinquiry');
    
    echo "<div class='section'>";
    echo "<h2>Informace o modulu</h2>";
    
    if ($module) {
        echo "<strong>Název:</strong> " . $module->displayName . "<br>";
        echo "<strong>Verze:</strong> " . $module->version . "<br>";
        echo "<strong>Nainstalován:</strong> " . (Module::isInstalled('priceinquiry') ? 'Ano' : 'Ne') . "<br>";
        echo "<strong>Aktivní:</strong> " . (Module::isEnabled('priceinquiry') ? 'Ano' : 'Ne') . "<br>";
    } else {
        echo "<span class='error'>❌ Modul nebyl nalezen!</span><br>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>⚠️ Varování</h2>";
    echo "<p><strong>Tato akce provede:</strong></p>";
    echo "<ul>";
    echo "<li>Zálohování aktuální konfigurace</li>";
    echo "<li>Kompletní odinstalaci modulu</li>";
    echo "<li>Vyčištění všech souvisejících dat z databáze</li>";
    echo "<li>Vyčištění cache</li>";
    echo "<li>Reinstalaci modulu</li>";
    echo "<li>Obnovení konfigurace</li>";
    echo "</ul>";
    
    echo "<p><strong style='color: red;'>Pozor:</strong> Všechny dotazy na cenu budou smazány!</p>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>Potvrzení</h2>";
    echo "<p>Opravdu chcete provést kompletní reinstalaci modulu?</p>";
    
    echo "<form method='post'>";
    echo "<input type='hidden' name='action' value='reinstall'>";
    echo "<button type='submit' class='button danger' onclick='return confirm(\"Opravdu chcete reinstalovat modul? Všechny dotazy budou smazány!\")'>🔄 Reinstalovat modul</button>";
    echo "</form>";
    
    echo "<p><a href='diagnostic_tools.php' class='button'>← Zpět na diagnostické nástroje</a></p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Reinstalace: " . date('Y-m-d H:i:s') . "</small></p>";
?>
