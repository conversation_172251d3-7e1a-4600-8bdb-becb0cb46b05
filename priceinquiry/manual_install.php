<?php
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> instalace modulu "Cena na dotaz"
 * Obchází problém s PrestaShop core při instalaci
 */

// Načteme PrestaShop
require_once(dirname(__FILE__) . '/prestashop_loader.php');

echo "<h1>Manu<PERSON>lní instalace modulu 'Cena na dotaz'</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px; }
    .button.danger { background: #dc3545; }
    .button.success { background: #28a745; }
</style>";

// <PERSON><PERSON><PERSON><PERSON>, jestli je to POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'manual_install') {
        echo "<div class='section'>";
        echo "<h2>Probíhá manuální instalace...</h2>";
        
        $moduleName = 'priceinquiry';
        $shopId = Context::getContext()->shop->id;
        
        // 1. Kontrola existence modulu
        echo "<h3>1. Kontrola modulu</h3>";
        if (!file_exists(_PS_MODULE_DIR_ . $moduleName . '/' . $moduleName . '.php')) {
            echo "<span class='error'>❌ Soubor modulu neexistuje!</span><br>";
            exit;
        }
        echo "<span class='ok'>✅ Soubor modulu existuje</span><br>";
        
        // 2. Registrace modulu v databázi
        echo "<h3>2. Registrace v databázi</h3>";
        
        // Zkontrolujeme, jestli modul už není v databázi
        $existingModule = Db::getInstance()->getRow('SELECT * FROM `' . _DB_PREFIX_ . 'module` WHERE name = "' . pSQL($moduleName) . '"');
        
        if ($existingModule) {
            echo "<span class='info'>ℹ️ Modul už je v databázi (ID: {$existingModule['id_module']})</span><br>";
            $moduleId = $existingModule['id_module'];
        } else {
            // Vložíme modul do databáze
            $insertSql = 'INSERT INTO `' . _DB_PREFIX_ . 'module` (name, active, version) VALUES ("' . pSQL($moduleName) . '", 1, "8.2.0")';
            
            if (Db::getInstance()->execute($insertSql)) {
                $moduleId = Db::getInstance()->Insert_ID();
                echo "<span class='ok'>✅ Modul registrován v databázi (ID: {$moduleId})</span><br>";
            } else {
                echo "<span class='error'>❌ Chyba při registraci modulu</span><br>";
                exit;
            }
        }
        
        // 3. Aktivace modulu pro shop
        echo "<h3>3. Aktivace pro shop</h3>";
        
        $shopModuleExists = Db::getInstance()->getRow('SELECT * FROM `' . _DB_PREFIX_ . 'module_shop` WHERE id_module = ' . (int)$moduleId . ' AND id_shop = ' . (int)$shopId);
        
        if (!$shopModuleExists) {
            $insertShopSql = 'INSERT INTO `' . _DB_PREFIX_ . 'module_shop` (id_module, id_shop, enable_device) VALUES (' . (int)$moduleId . ', ' . (int)$shopId . ', 7)';
            
            if (Db::getInstance()->execute($insertShopSql)) {
                echo "<span class='ok'>✅ Modul aktivován pro shop</span><br>";
            } else {
                echo "<span class='error'>❌ Chyba při aktivaci pro shop</span><br>";
            }
        } else {
            echo "<span class='info'>ℹ️ Modul už je aktivován pro shop</span><br>";
        }
        
        // 4. Vytvoření tabulky pro dotazy
        echo "<h3>4. Vytvoření databázových tabulek</h3>";
        
        $createTableSql = '
        CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'price_inquiry` (
            `id_price_inquiry` int(11) NOT NULL AUTO_INCREMENT,
            `id_product` int(11) NOT NULL,
            `product_name` varchar(255) NOT NULL,
            `product_reference` varchar(64) DEFAULT NULL,
            `customer_name` varchar(255) NOT NULL,
            `customer_email` varchar(255) NOT NULL,
            `customer_phone` varchar(32) DEFAULT NULL,
            `customer_company` varchar(255) DEFAULT NULL,
            `quantity` int(11) DEFAULT 1,
            `message` text,
            `resolved` tinyint(1) DEFAULT 0,
            `date_add` datetime NOT NULL,
            PRIMARY KEY (`id_price_inquiry`),
            KEY `id_product` (`id_product`),
            KEY `resolved` (`resolved`),
            KEY `date_add` (`date_add`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;';
        
        if (Db::getInstance()->execute($createTableSql)) {
            echo "<span class='ok'>✅ Tabulka price_inquiry vytvořena</span><br>";
        } else {
            echo "<span class='error'>❌ Chyba při vytváření tabulky</span><br>";
        }
        
        // 5. Registrace hooks
        echo "<h3>5. Registrace hooks</h3>";
        
        $hooks = [
            'displayProductPriceBlock' => 'Hook pro přehled produktů',
            'displayProductActions' => 'Hook pro detail produktu',
            'displayProductAdditionalInfo' => 'Záložní hook pro detail',
            'displayReassurance' => 'Další záložní hook',
            'displayHeader' => 'Hook pro CSS a JavaScript',
            'displayFooter' => 'Hook pro modal'
        ];
        
        $registeredHooks = 0;
        
        foreach ($hooks as $hookName => $description) {
            $hookId = Hook::getIdByName($hookName);
            
            if (!$hookId) {
                echo "<span class='error'>❌ Hook {$hookName} neexistuje</span><br>";
                continue;
            }
            
            // Smazat existující registraci
            $deleteSql = 'DELETE FROM `' . _DB_PREFIX_ . 'hook_module` WHERE id_module = ' . (int)$moduleId . ' AND id_hook = ' . (int)$hookId . ' AND id_shop = ' . (int)$shopId;
            Db::getInstance()->execute($deleteSql);
            
            // Najít pozici
            $positionSql = 'SELECT MAX(position) as max_position FROM `' . _DB_PREFIX_ . 'hook_module` WHERE id_hook = ' . (int)$hookId . ' AND id_shop = ' . (int)$shopId;
            $maxPosition = Db::getInstance()->getValue($positionSql);
            $newPosition = $maxPosition ? $maxPosition + 1 : 1;
            
            // Registrovat hook
            $insertHookSql = 'INSERT INTO `' . _DB_PREFIX_ . 'hook_module` (id_module, id_shop, id_hook, position) VALUES (' . (int)$moduleId . ', ' . (int)$shopId . ', ' . (int)$hookId . ', ' . (int)$newPosition . ')';
            
            if (Db::getInstance()->execute($insertHookSql)) {
                echo "<span class='ok'>✅ Hook {$hookName} registrován (pozice: {$newPosition})</span><br>";
                $registeredHooks++;
            } else {
                echo "<span class='error'>❌ Chyba při registraci hook {$hookName}</span><br>";
            }
        }
        
        // 6. Nastavení konfigurace
        echo "<h3>6. Nastavení konfigurace</h3>";
        
        $configs = [
            'PRICE_INQUIRY_ENABLED' => 1,
            'PRICE_INQUIRY_EMAIL' => Configuration::get('PS_SHOP_EMAIL'),
            'PRICE_INQUIRY_BUTTON_TEXT' => 'Zjistit cenu',
            'PRICE_INQUIRY_PRICE_TEXT' => 'Cena na dotaz',
            'PRICE_INQUIRY_SEND_ADMIN_EMAIL' => 1,
            'PRICE_INQUIRY_SEND_CUSTOMER_EMAIL' => 1
        ];
        
        foreach ($configs as $key => $value) {
            Configuration::updateValue($key, $value);
        }
        
        echo "<span class='ok'>✅ Konfigurace nastavena</span><br>";
        
        // 7. Vyčištění cache
        echo "<h3>7. Vyčištění cache</h3>";
        
        try {
            Tools::clearSmartyCache();
            Tools::clearXMLCache();
            
            if (function_exists('opcache_reset')) {
                opcache_reset();
            }
            
            echo "<span class='ok'>✅ Cache vyčištěna</span><br>";
        } catch (Exception $e) {
            echo "<span class='warning'>⚠️ Částečná chyba při čištění cache</span><br>";
        }
        
        // 8. Finální kontrola
        echo "<h3>8. Finální kontrola</h3>";
        
        $module = Module::getInstanceByName($moduleName);
        if ($module) {
            echo "<span class='ok'>✅ Modul načten z kódu</span><br>";
            
            // Kontrola hooks přes API
            $apiHooksOk = 0;
            foreach ($hooks as $hookName => $description) {
                $hookId = Hook::getIdByName($hookName);
                if ($hookId && Hook::isModuleRegisteredOnHook($module, $hookId, $shopId)) {
                    $apiHooksOk++;
                }
            }
            
            echo "<strong>Hooks registrovány v DB:</strong> {$registeredHooks}/" . count($hooks) . "<br>";
            echo "<strong>Hooks viditelné přes API:</strong> {$apiHooksOk}/" . count($hooks) . "<br>";
            
            if ($apiHooksOk === count($hooks)) {
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
                echo "<h3>🎉 Manuální instalace úspěšná!</h3>";
                echo "<p>Modul byl úspěšně nainstalován a všechny hooks jsou registrovány.</p>";
                echo "<p><a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank' class='button success'>Test na webu</a></p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
                echo "<h3>⚠️ Instalace částečně úspěšná</h3>";
                echo "<p>Modul byl nainstalován, ale hooks nejsou plně funkční.</p>";
                echo "<p>Možná je problém s PrestaShop cache nebo core.</p>";
                echo "</div>";
            }
        } else {
            echo "<span class='error'>❌ Modul se nepodařilo načíst</span><br>";
        }
        
        echo "</div>";
        
    } else {
        echo "<span class='error'>❌ Neznámá akce</span><br>";
    }
    
} else {
    // Zobrazíme formulář
    echo "<div class='section'>";
    echo "<h2>Manuální instalace modulu</h2>";
    echo "<p>Tento script provede manuální instalaci modulu přímo v databázi, čímž obejde problém s PrestaShop core.</p>";
    
    echo "<p><strong>Co bude provedeno:</strong></p>";
    echo "<ul>";
    echo "<li>Registrace modulu v tabulce modules</li>";
    echo "<li>Aktivace modulu pro aktuální shop</li>";
    echo "<li>Vytvoření databázových tabulek</li>";
    echo "<li>Manuální registrace všech hooks</li>";
    echo "<li>Nastavení výchozí konfigurace</li>";
    echo "<li>Vyčištění cache</li>";
    echo "</ul>";
    
    echo "<form method='post'>";
    echo "<input type='hidden' name='action' value='manual_install'>";
    echo "<button type='submit' class='button success'>🔧 Provést manuální instalaci</button>";
    echo "</form>";
    
    echo "<p><a href='diagnostic_tools.php' class='button'>← Zpět na diagnostické nástroje</a></p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Manuální instalace: " . date('Y-m-d H:i:s') . "</small></p>";
?>
