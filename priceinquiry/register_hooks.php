<?php
/**
 * Script pro registraci hooks modulu "Cena na dotaz"
 * Vynutí registraci všech potřebných hooks
 */

// Načteme PrestaShop
require_once(dirname(__FILE__) . '/prestashop_loader.php');

echo "<h1>Registrace hooks pro modul 'Cena na dotaz'</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px; }
    .button:hover { background: #005a87; }
</style>";

$module = Module::getInstanceByName('priceinquiry');
if (!$module) {
    echo "<span class='error'>❌ Modul 'priceinquiry' nebyl nalezen!</span>";
    exit;
}

echo "<div class='section'>";
echo "<h2>Stav modulu</h2>";
echo "<strong>Název:</strong> " . $module->displayName . "<br>";
echo "<strong>Verze:</strong> " . $module->version . "<br>";
echo "<strong>Nainstalován:</strong> " . (Module::isInstalled('priceinquiry') ? 'Ano' : 'Ne') . "<br>";
echo "<strong>Aktivní:</strong> " . (Module::isEnabled('priceinquiry') ? 'Ano' : 'Ne') . "<br>";
echo "</div>";

// Kontrola a registrace hooks
echo "<div class='section'>";
echo "<h2>Registrace hooks</h2>";

$hooks = [
    'displayProductPriceBlock' => 'Hook pro přehled produktů (kategorie)',
    'displayProductActions' => 'Hook pro detail produktu',
    'displayProductAdditionalInfo' => 'Záložní hook pro detail',
    'displayReassurance' => 'Další záložní hook',
    'displayHeader' => 'Hook pro CSS a JavaScript',
    'displayFooter' => 'Hook pro modal'
];

$registered = 0;
$failed = 0;

foreach ($hooks as $hookName => $description) {
    echo "<h3>{$hookName}</h3>";
    echo "<p><em>{$description}</em></p>";
    
    $hookId = Hook::getIdByName($hookName);
    if (!$hookId) {
        echo "<span class='error'>❌ Hook neexistuje v systému!</span><br>";
        $failed++;
        continue;
    }
    
    // Zkontrolujeme aktuální stav
    $isRegistered = Hook::isModuleRegisteredOnHook($module, $hookId, Context::getContext()->shop->id);
    
    if ($isRegistered) {
        echo "<span class='info'>ℹ️ Hook je již registrován</span><br>";
        
        // Odregistrujeme a znovu zaregistrujeme pro jistotu
        echo "Odregistrovávám a znovu registruji...<br>";
        $module->unregisterHook($hookName);
    }
    
    // Registrujeme hook
    if ($module->registerHook($hookName)) {
        echo "<span class='ok'>✅ Hook úspěšně zaregistrován</span><br>";
        $registered++;
    } else {
        echo "<span class='error'>❌ Chyba při registraci hook</span><br>";
        $failed++;
    }
    
    echo "<hr>";
}

echo "</div>";

// Vyčištění cache
echo "<div class='section'>";
echo "<h2>Vyčištění cache</h2>";

try {
    Tools::clearSmartyCache();
    echo "<span class='ok'>✅ Smarty cache vyčištěna</span><br>";
    
    Tools::clearXMLCache();
    echo "<span class='ok'>✅ XML cache vyčištěna</span><br>";
    
    // Vyčistíme také cache složku
    $cacheDir = _PS_CACHE_DIR_;
    if (is_dir($cacheDir)) {
        $files = glob($cacheDir . '*');
        $deletedFiles = 0;
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
                $deletedFiles++;
            }
        }
        echo "<span class='ok'>✅ Cache složka vyčištěna ({$deletedFiles} souborů)</span><br>";
    }
    
} catch (Exception $e) {
    echo "<span class='error'>❌ Chyba při čištění cache: " . $e->getMessage() . "</span><br>";
}

echo "</div>";

// Shrnutí
echo "<div class='section'>";
echo "<h2>Shrnutí</h2>";

if ($failed == 0) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎉 Všechny hooks byly úspěšně zaregistrovány!</h3>";
    echo "<p><strong>Zaregistrováno:</strong> {$registered} hooks</p>";
    echo "<p>Modul by nyní měl fungovat správně.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>⚠️ Některé hooks se nepodařilo zaregistrovat</h3>";
    echo "<p><strong>Úspěšné:</strong> {$registered}</p>";
    echo "<p><strong>Neúspěšné:</strong> {$failed}</p>";
    echo "</div>";
}

echo "<h3>Další kroky:</h3>";
echo "<ol>";
echo "<li><a href='debug_script.php'>Spustit úplnou diagnostiku</a></li>";
echo "<li><a href='check_display.php'>Zkontrolovat zobrazení modulu</a></li>";
echo "<li><a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank'>Otestovat na webu</a></li>";
echo "</ol>";

echo "</div>";

echo "<hr>";
echo "<p><a href='diagnostic_tools.php'>← Zpět na diagnostické nástroje</a></p>";
echo "<p><small>Registrace dokončena: " . date('Y-m-d H:i:s') . "</small></p>";
?>
