<?php
/**
 * Script pro zjiš<PERSON>ění správné cesty k PrestaShop konfiguraci
 */

echo "<h1>Hledání cesty k PrestaShop konfiguraci</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; }
</style>";

echo "<h2>Aktuální umístění:</h2>";
echo "<strong>__FILE__:</strong> " . __FILE__ . "<br>";
echo "<strong>__DIR__:</strong> " . __DIR__ . "<br>";
echo "<strong>dirname(__FILE__):</strong> " . dirname(__FILE__) . "<br>";

echo "<h2><PERSON><PERSON><PERSON><PERSON> mo<PERSON>n<PERSON>ch cest:</h2>";

$possible_paths = [
    dirname(__FILE__) . '/../../config/config.inc.php',
    dirname(__FILE__) . '/../../../config/config.inc.php', 
    dirname(dirname(dirname(__FILE__))) . '/config/config.inc.php',
    dirname(dirname(__FILE__)) . '/config/config.inc.php',
    '/www/doc/czimg-dev1.www2.peterman.cz/www/config/config.inc.php',
    $_SERVER['DOCUMENT_ROOT'] . '/config/config.inc.php'
];

$found_path = null;

foreach ($possible_paths as $i => $path) {
    echo "<strong>Cesta " . ($i + 1) . ":</strong> " . $path . " - ";
    
    if (file_exists($path)) {
        echo "<span class='ok'>✅ EXISTUJE</span><br>";
        if (!$found_path) {
            $found_path = $path;
        }
    } else {
        echo "<span class='error'>❌ Neexistuje</span><br>";
    }
}

echo "<h2>Informace o serveru:</h2>";
echo "<strong>DOCUMENT_ROOT:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "<strong>SERVER_NAME:</strong> " . $_SERVER['SERVER_NAME'] . "<br>";
echo "<strong>REQUEST_URI:</strong> " . $_SERVER['REQUEST_URI'] . "<br>";

if ($found_path) {
    echo "<h2>✅ Nalezena správná cesta:</h2>";
    echo "<pre>" . $found_path . "</pre>";
    
    echo "<h3>Test načtení:</h3>";
    try {
        require_once($found_path);
        require_once(dirname($found_path) . '/init.php');
        echo "<span class='ok'>✅ PrestaShop úspěšně načten</span><br>";
        
        if (defined('_PS_VERSION_')) {
            echo "<strong>PrestaShop verze:</strong> " . _PS_VERSION_ . "<br>";
        }
        
        if (defined('_PS_MODULE_DIR_')) {
            echo "<strong>Module directory:</strong> " . _PS_MODULE_DIR_ . "<br>";
        }
        
    } catch (Exception $e) {
        echo "<span class='error'>❌ Chyba při načítání: " . $e->getMessage() . "</span><br>";
    }
} else {
    echo "<h2>❌ Žádná cesta nebyla nalezena</h2>";
    echo "<p>Zkuste ručně najít soubor config.inc.php v kořenovém adresáři PrestaShop.</p>";
}

echo "<hr>";
echo "<p><a href='diagnostic_tools.php'>← Zpět na diagnostické nástroje</a></p>";
?>
