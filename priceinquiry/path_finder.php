<?php
/**
 * Script pro zjiš<PERSON>ění správné cesty k PrestaShop konfiguraci
 */

echo "<h1>Hledání cesty k PrestaShop konfiguraci</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; }
</style>";

echo "<h2>Aktuální umístění:</h2>";
echo "<strong>__FILE__:</strong> " . __FILE__ . "<br>";
echo "<strong>__DIR__:</strong> " . __DIR__ . "<br>";
echo "<strong>dirname(__FILE__):</strong> " . dirname(__FILE__) . "<br>";

echo "<h2><PERSON><PERSON><PERSON><PERSON> mo<PERSON>n<PERSON>ch cest:</h2>";

$possible_paths = [
    dirname(__FILE__) . '/../../config/config.inc.php',
    dirname(__FILE__) . '/../../../config/config.inc.php',
    dirname(dirname(dirname(__FILE__))) . '/config/config.inc.php',
    dirname(dirname(__FILE__)) . '/config/config.inc.php',
    '/www/doc/czimg-dev1.www2.peterman.cz/www/config/config.inc.php',
    $_SERVER['DOCUMENT_ROOT'] . '/config/config.inc.php'
];

$possible_init_paths = [
    dirname(__FILE__) . '/../../init.php',
    dirname(__FILE__) . '/../../../init.php',
    dirname(dirname(dirname(__FILE__))) . '/init.php',
    dirname(dirname(__FILE__)) . '/init.php',
    '/www/doc/czimg-dev1.www2.peterman.cz/www/init.php',
    $_SERVER['DOCUMENT_ROOT'] . '/init.php'
];

$found_config_path = null;
$found_init_path = null;

echo "<h3>Hledání config.inc.php:</h3>";
foreach ($possible_paths as $i => $path) {
    echo "<strong>Cesta " . ($i + 1) . ":</strong> " . $path . " - ";

    if (file_exists($path)) {
        echo "<span class='ok'>✅ EXISTUJE</span><br>";
        if (!$found_config_path) {
            $found_config_path = $path;
        }
    } else {
        echo "<span class='error'>❌ Neexistuje</span><br>";
    }
}

echo "<h3>Hledání init.php:</h3>";
foreach ($possible_init_paths as $i => $path) {
    echo "<strong>Cesta " . ($i + 1) . ":</strong> " . $path . " - ";

    if (file_exists($path)) {
        echo "<span class='ok'>✅ EXISTUJE</span><br>";
        if (!$found_init_path) {
            $found_init_path = $path;
        }
    } else {
        echo "<span class='error'>❌ Neexistuje</span><br>";
    }
}

echo "<h2>Informace o serveru:</h2>";
echo "<strong>DOCUMENT_ROOT:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "<strong>SERVER_NAME:</strong> " . $_SERVER['SERVER_NAME'] . "<br>";
echo "<strong>REQUEST_URI:</strong> " . $_SERVER['REQUEST_URI'] . "<br>";

if ($found_config_path && $found_init_path) {
    echo "<h2>✅ Nalezeny správné cesty:</h2>";
    echo "<strong>Config:</strong> <pre>" . $found_config_path . "</pre>";
    echo "<strong>Init:</strong> <pre>" . $found_init_path . "</pre>";

    echo "<h3>Test načtení:</h3>";
    try {
        require_once($found_config_path);
        require_once($found_init_path);
        echo "<span class='ok'>✅ PrestaShop úspěšně načten</span><br>";

        if (defined('_PS_VERSION_')) {
            echo "<strong>PrestaShop verze:</strong> " . _PS_VERSION_ . "<br>";
        }

        if (defined('_PS_MODULE_DIR_')) {
            echo "<strong>Module directory:</strong> " . _PS_MODULE_DIR_ . "<br>";
        }

        // Test modulu
        $module = Module::getInstanceByName('priceinquiry');
        if ($module) {
            echo "<strong>Modul 'priceinquiry':</strong> <span class='ok'>✅ Nalezen</span><br>";
            echo "<strong>Verze modulu:</strong> " . $module->version . "<br>";
        } else {
            echo "<strong>Modul 'priceinquiry':</strong> <span class='error'>❌ Nenalezen</span><br>";
        }

    } catch (Exception $e) {
        echo "<span class='error'>❌ Chyba při načítání: " . $e->getMessage() . "</span><br>";
    } catch (Error $e) {
        echo "<span class='error'>❌ Chyba při načítání: " . $e->getMessage() . "</span><br>";
    }
} else {
    echo "<h2>❌ Nebyly nalezeny všechny potřebné soubory</h2>";
    if (!$found_config_path) {
        echo "<p>❌ Nebyl nalezen config.inc.php</p>";
    }
    if (!$found_init_path) {
        echo "<p>❌ Nebyl nalezen init.php</p>";
    }
}

echo "<hr>";
echo "<p><a href='diagnostic_tools.php'>← Zpět na diagnostické nástroje</a></p>";
?>
