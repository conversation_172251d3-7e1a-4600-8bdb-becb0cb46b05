<?php
/**
 * Test konkrétního produktu pro modul "Cena na dotaz"
 * Testuje produkt s ID 15336 (braz-klobouk)
 */

// Nastavení pro debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Načteme PrestaShop pomocí univerzálního loaderu
require_once(dirname(__FILE__) . '/prestashop_loader.php');

echo "<h1>Test produktu pro modul 'Cena na dotaz'</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

$productId = 15336;

// 1. Základní informace o produktu
echo "<div class='section'>";
echo "<h2>1. Informace o produktu</h2>";

$product = new Product($productId, false, Context::getContext()->language->id);

if (!Validate::isLoadedObject($product)) {
    echo "<span class='error'>❌ Produkt s ID {$productId} nebyl nalezen!</span><br>";
    exit;
}

echo "<table>";
echo "<tr><th>Vlastnost</th><th>Hodnota</th></tr>";
echo "<tr><td>ID</td><td>{$product->id}</td></tr>";
echo "<tr><td>Název</td><td>" . htmlspecialchars($product->name) . "</td></tr>";
echo "<tr><td>Reference</td><td>" . htmlspecialchars($product->reference) . "</td></tr>";
echo "<tr><td>Aktivní</td><td>" . ($product->active ? 'Ano' : 'Ne') . "</td></tr>";
echo "<tr><td>Dostupný pro objednávku</td><td>" . ($product->available_for_order ? 'Ano' : 'Ne') . "</td></tr>";
echo "</table>";

echo "</div>";

// 2. Analýza cen
echo "<div class='section'>";
echo "<h2>2. Analýza cen</h2>";

// Základní cena
$basePrice = Product::getPriceStatic($productId, true, null, 2, null, false, true);
echo "<strong>Základní cena produktu:</strong> " . number_format($basePrice, 2, ',', ' ') . " Kč<br>";

if ($basePrice <= 0) {
    echo "<span class='ok'>✅ Základní cena je nulová - modul by se měl zobrazit</span><br>";
} else {
    echo "<span class='warning'>⚠️ Základní cena není nulová - modul se nezobrazí</span><br>";
}

// Kontrola variant
$attributes = Product::getProductAttributesIds($productId);
if ($attributes && count($attributes) > 0) {
    echo "<br><strong>Varianty produktu:</strong><br>";
    echo "<table>";
    echo "<tr><th>ID varianty</th><th>Cena</th><th>Status pro modul</th></tr>";
    
    $hasZeroPriceVariant = false;
    foreach ($attributes as $attr) {
        $attrPrice = Product::getPriceStatic($productId, true, $attr['id_product_attribute'], 2, null, false, true);
        $status = $attrPrice <= 0 ? "<span class='ok'>✅ Zobrazí se modul</span>" : "<span class='info'>Nezobrazí se</span>";
        
        if ($attrPrice <= 0) {
            $hasZeroPriceVariant = true;
        }
        
        echo "<tr>";
        echo "<td>{$attr['id_product_attribute']}</td>";
        echo "<td>" . number_format($attrPrice, 2, ',', ' ') . " Kč</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($hasZeroPriceVariant) {
        echo "<span class='ok'>✅ Alespoň jedna varianta má nulovou cenu</span><br>";
    } else {
        echo "<span class='warning'>⚠️ Žádná varianta nemá nulovou cenu</span><br>";
    }
} else {
    echo "<br><strong>Produkt nemá varianty</strong><br>";
}

echo "</div>";

// 3. Test modulu
echo "<div class='section'>";
echo "<h2>3. Test modulu</h2>";

$module = Module::getInstanceByName('priceinquiry');
if (!$module) {
    echo "<span class='error'>❌ Modul 'priceinquiry' nebyl nalezen!</span><br>";
} else {
    echo "<span class='ok'>✅ Modul nalezen</span><br>";
    
    // Kontrola konfigurace
    $enabled = Configuration::get('PRICE_INQUIRY_ENABLED');
    echo "<strong>Modul povolen:</strong> " . ($enabled ? 'Ano' : 'Ne') . "<br>";
    
    if (!$enabled) {
        echo "<span class='error'>❌ Modul není povolen v konfiguraci!</span><br>";
    } else {
        // Simulujeme kontext produktu
        Context::getContext()->controller = new FrontController();
        Context::getContext()->controller->php_self = 'product';
        
        // Test displayProductActions hook
        $params = ['product' => $product];
        $hookOutput = $module->hookDisplayProductActions($params);
        
        echo "<h3>Výstup hook displayProductActions:</h3>";
        if (!empty($hookOutput)) {
            echo "<span class='ok'>✅ Hook vrací obsah (" . strlen($hookOutput) . " znaků)</span><br>";
            
            // Zkontrolujeme, jestli obsahuje náš blok
            if (strpos($hookOutput, 'price-inquiry-block-detail') !== false) {
                echo "<span class='ok'>✅ Obsahuje blok price-inquiry-block-detail</span><br>";
            } else {
                echo "<span class='warning'>⚠️ Neobsahuje blok price-inquiry-block-detail</span><br>";
            }
            
            // Zkontrolujeme, jestli je blok viditelný
            if (strpos($hookOutput, 'display: block') !== false) {
                echo "<span class='ok'>✅ Blok je nastaven jako viditelný</span><br>";
            } elseif (strpos($hookOutput, 'display: none') !== false) {
                echo "<span class='info'>ℹ️ Blok je nastaven jako skrytý (JavaScript ho může zobrazit)</span><br>";
            }
            
            echo "<h4>Náhled výstupu:</h4>";
            echo "<pre>" . htmlspecialchars(substr($hookOutput, 0, 500)) . "...</pre>";
        } else {
            echo "<span class='error'>❌ Hook nevrací žádný obsah</span><br>";
            
            // Debug informace
            echo "<strong>Debug informace:</strong><br>";
            echo "- php_self: " . Context::getContext()->controller->php_self . "<br>";
            echo "- PRICE_INQUIRY_ENABLED: " . (Configuration::get('PRICE_INQUIRY_ENABLED') ? 'true' : 'false') . "<br>";
            echo "- Cena produktu: " . $basePrice . "<br>";
        }
    }
}

echo "</div>";

// 4. Test hook displayProductPriceBlock (pro kategorie)
echo "<div class='section'>";
echo "<h2>4. Test hook pro kategorie</h2>";

if ($module && $enabled) {
    Context::getContext()->controller->php_self = 'category';
    
    $params = [
        'product' => ['id_product' => $productId, 'id' => $productId],
        'type' => 'before_price'
    ];
    
    $categoryHookOutput = $module->hookDisplayProductPriceBlock($params);
    
    if (!empty($categoryHookOutput)) {
        echo "<span class='ok'>✅ Hook pro kategorie vrací obsah</span><br>";
        echo "<pre>" . htmlspecialchars($categoryHookOutput) . "</pre>";
    } else {
        echo "<span class='info'>ℹ️ Hook pro kategorie nevrací obsah (možná kvůli ceně > 0)</span><br>";
    }
}

echo "</div>";

// 5. Kontrola souborů
echo "<div class='section'>";
echo "<h2>5. Kontrola souborů</h2>";

$files = [
    'views/js/front.js' => 'JavaScript',
    'views/css/front.css' => 'CSS',
    'views/templates/front/inquiry_block_detail.tpl' => 'Template detail',
    'views/templates/front/inquiry_modal.tpl' => 'Template modal'
];

foreach ($files as $file => $description) {
    $fullPath = _PS_MODULE_DIR_ . 'priceinquiry/' . $file;
    if (file_exists($fullPath)) {
        $size = filesize($fullPath);
        echo "<span class='ok'>✅ {$description}: {$file} (" . number_format($size) . " B)</span><br>";
    } else {
        echo "<span class='error'>❌ {$description}: {$file} - CHYBÍ!</span><br>";
    }
}

echo "</div>";

// 6. Doporučení
echo "<div class='section'>";
echo "<h2>6. Doporučení</h2>";

$recommendations = [];

if (!$enabled) {
    $recommendations[] = "❌ Zapněte modul v konfiguraci";
}

if ($basePrice > 0 && (!$attributes || !$hasZeroPriceVariant)) {
    $recommendations[] = "⚠️ Nastavte cenu produktu nebo alespoň jedné varianty na 0 Kč";
}

if (empty($hookOutput) && $enabled) {
    $recommendations[] = "❌ Hook nevrací obsah - zkontrolujte registraci hooks";
}

if (empty($recommendations)) {
    echo "<span class='ok'>✅ Vše vypadá v pořádku!</span>";
    echo "<p><strong>Test URL:</strong> <a href='https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html' target='_blank'>https://czimg-dev1.www2.peterman.cz/klobouky/15336-braz-klobouk.html</a></p>";
} else {
    echo "<ul>";
    foreach ($recommendations as $rec) {
        echo "<li>{$rec}</li>";
    }
    echo "</ul>";
}

echo "</div>";

echo "<hr>";
echo "<p><a href='debug_script.php'>Spustit úplnou diagnostiku</a> | <a href='fix_module.php'>Opravit modul</a></p>";
echo "<p><small>Test dokončen: " . date('Y-m-d H:i:s') . "</small></p>";
?>
